# GNN Model Performance Comparison

## Current Data Models

| Model | Architecture | Accuracy | Precision | Recall | F1 Score | ROC AUC | Parameters | Epochs | Training Time |
|-------|-------------|----------|-----------|--------|----------|---------|------------|--------|---------------|
| **Complex GATv2 (Temp 3)** | 4-layer GATv2, 8 heads, 128 hidden, layer+batch norm | 72.84% | 71.05% | 68.17% | 69.58% | 79.93% | 169,601 | 86 | ~4h |
| **Complex GATv2 (Temp 5)** | 4-layer GATv2, 8 heads, 128 hidden, layer+batch norm | 70.03% | 66.93% | 69.08% | 67.99% | 77.61% | 169,601 | 59 | ~2.5h |
| **Enhanced GATv2 (Temp 3)** | 4-layer GATv2, residual, self-attention, hierarchical pooling | 67.25% | 60.14% | 83.46% | 69.90% | 71.85% | 6,045,313 | 11 | ~6h |
| **GATv2 Standard (Temp 3)** | 3-layer GATv2, 4 heads, 64 hidden | 66.17% | 59.08% | 83.83% | 69.31% | 69.48% | ~25,000 | 22 | ~1h |
| **GATv2 (Temp 5)** | 3-layer GATv2, 4 heads, 64 hidden | 63.85% | 57.39% | 83.59% | 68.06% | 68.96% | ~30,000 | 32 | ~1.5h |
| **GATv2 5-Layer (Temp 3)** | 5-layer GATv2, 4 heads, 64 hidden | 57.83% | 52.09% | 93.06% | 66.79% | 64.89% | 51,905 | 21 | ~1h |

## Regression Metrics (Current Data Models)

| Model | R² Score | MSE | RMSE | MAE | Explained Variance | Max Error | Median AE |
|-------|----------|-----|------|-----|-------------------|-----------|-----------|
| **Complex GATv2 (Temp 3)** | 0.2425 | 0.1879 | 0.4335 | 0.3954 | 0.2463 | 0.8941 | 0.3778 |
| **Complex GATv2 (Temp 5)** | 0.2095 | 0.1964 | 0.4432 | 0.4132 | 0.2096 | 0.8821 | 0.3899 |
| **Enhanced GATv2 (Temp 3)** | 0.1465 | 0.2117 | 0.4601 | 0.4303 | 0.1496 | 0.8113 | 0.3812 |

## Historical Data Models

| Model | Architecture | Accuracy | Precision | Recall | F1 Score | ROC AUC | Parameters | Epochs | Training Time |
|-------|-------------|----------|-----------|--------|----------|---------|------------|--------|---------------|
| **GraphSAGE (Old Data)** | 3-layer GraphSAGE, 64 hidden, dropout 0.5 | 73.04% | 70.36% | 89.32% | 78.72% | 76.13% | ~15,000 | 46 | ~2h |

## Parameter Efficiency Ranking

| Model | Parameters | F1 Score | F1 per 1K Parameters |
|-------|------------|----------|---------------------|
| GraphSAGE (Old Data) | ~15,000 | 78.72% | 5.25 |
| GATv2 Standard (Temp 3) | ~25,000 | 69.31% | 2.77 |
| GATv2 (Temp 5) | ~30,000 | 68.06% | 2.27 |
| GATv2 5-Layer (Temp 3) | 51,905 | 66.79% | 1.29 |
| Complex GATv2 (Temp 3) | 169,601 | 69.58% | 0.41 |
| Complex GATv2 (Temp 5) | 169,601 | 67.99% | 0.40 |
| Enhanced GATv2 (Temp 3) | 6,045,313 | 69.90% | 0.012 |

## Training Efficiency Ranking

| Model | Training Time | Epochs | Accuracy | Time per % Accuracy |
|-------|---------------|--------|----------|-------------------|
| GATv2 Standard (Temp 3) | ~1h | 22 | 66.17% | 0.91 min/% |
| GATv2 5-Layer (Temp 3) | ~1h | 21 | 57.83% | 1.04 min/% |
| GATv2 (Temp 5) | ~1.5h | 32 | 63.85% | 1.41 min/% |
| GraphSAGE (Old Data) | ~2h | 46 | 73.04% | 1.64 min/% |
| Complex GATv2 (Temp 5) | ~2.5h | 59 | 70.03% | 2.14 min/% |
| Complex GATv2 (Temp 3) | ~4h | 86 | 72.84% | 3.30 min/% |
| Enhanced GATv2 (Temp 3) | ~6h | 11 | 67.25% | 5.35 min/% |

## Performance Summary by Category

### Best Accuracy
1. GraphSAGE (Old Data): 73.04%
2. Complex GATv2 (Temp 3): 72.84%
3. Complex GATv2 (Temp 5): 70.03%

### Best Precision
1. Complex GATv2 (Temp 3): 71.05%
2. GraphSAGE (Old Data): 70.36%
3. Complex GATv2 (Temp 5): 66.93%

### Best Recall
1. GATv2 5-Layer (Temp 3): 93.06%
2. GraphSAGE (Old Data): 89.32%
3. Enhanced GATv2 (Temp 3): 83.46%

### Best F1 Score
1. GraphSAGE (Old Data): 78.72%
2. Enhanced GATv2 (Temp 3): 69.90%
3. Complex GATv2 (Temp 3): 69.58%

### Best ROC AUC
1. Complex GATv2 (Temp 3): 79.93%
2. Complex GATv2 (Temp 5): 77.61%
3. GraphSAGE (Old Data): 76.13%

### Most Parameter Efficient
1. GraphSAGE (Old Data): 5.25 F1/1K params
2. GATv2 Standard (Temp 3): 2.77 F1/1K params
3. GATv2 (Temp 5): 2.27 F1/1K params

### Fastest Training
1. GATv2 Standard (Temp 3): 1 hour
2. GATv2 5-Layer (Temp 3): 1 hour
3. GATv2 (Temp 5): 1.5 hours

---

**Total Models Evaluated**: 7  
**Total Parameters Trained**: 12.4M  
**Total Training Time**: ~18 hours  
**Evaluation Date**: January 25, 2025
