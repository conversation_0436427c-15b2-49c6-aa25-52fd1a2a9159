#!/usr/bin/env python3
"""
Comprehensive 2D IoU Evaluation System for GNN Occupancy Prediction Models

This script provides a complete evaluation framework for comparing 10 GNN models
using 2D Intersection over Union (IoU) metrics on test data.

Features:
- Loads all trained model checkpoints
- Processes test data from temporal_3 and temporal_5 directories
- Converts point clouds to 2D occupancy grids
- Calculates IoU metrics with comprehensive statistics
- Generates detailed visualizations and reports
- Performs statistical significance testing
- Identifies failure cases and spatial patterns

Author: AI Assistant
Date: 2024
"""

import os
import glob
import yaml
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from tqdm import tqdm
import warnings
from datetime import datetime
import json
from scipy import stats
from matplotlib.backends.backend_pdf import PdfPages

# Import local modules
from model import create_model
from data import OccupancyDataset
from torch_geometric.data import DataLoader

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

@dataclass
class ModelInfo:
    """Information about a trained model."""
    name: str
    checkpoint_path: str
    config_path: str
    temporal_window: int
    architecture: str
    num_layers: int
    hidden_dim: int
    num_parameters: int
    training_time: Optional[float] = None
    best_f1: Optional[float] = None

@dataclass
class IoUResults:
    """Results from IoU evaluation."""
    model_name: str
    sample_ious: List[float]
    mean_iou: float
    median_iou: float
    std_iou: float
    min_iou: float
    max_iou: float
    grid_resolution: float
    total_samples: int

@dataclass
class GridConfig:
    """Configuration for 2D grid conversion."""
    resolution: float = 0.1  # Grid cell size in meters
    padding: float = 0.5     # Padding around data bounds
    threshold: float = 0.5   # Prediction threshold for binary classification

class Comprehensive2DIoUEvaluator:
    """
    Comprehensive 2D IoU evaluation system for GNN occupancy prediction models.
    """

    def __init__(self,
                 gnn_occupancy_dir: str = ".",
                 data_dir: str = "../data/07_gnn_ready",
                 results_dir: str = "results/2d_iou_evaluation",
                 grid_config: Optional[GridConfig] = None):
        """
        Initialize the evaluator.

        Args:
            gnn_occupancy_dir: Path to gnn_occupancy directory
            data_dir: Path to test data directory
            results_dir: Path to save results
            grid_config: Configuration for 2D grid conversion
        """
        self.gnn_occupancy_dir = Path(gnn_occupancy_dir)
        self.data_dir = Path(data_dir)
        self.results_dir = Path(results_dir)
        self.grid_config = grid_config or GridConfig()

        # Create results directory
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # Initialize storage
        self.models: List[ModelInfo] = []
        self.iou_results: Dict[str, IoUResults] = {}
        self.spatial_analysis: Dict[str, np.ndarray] = {}

        # Set up device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 Using device: {self.device}")

    def discover_models(self) -> List[ModelInfo]:
        """
        Discover all trained models in checkpoint directories.

        Returns:
            List of discovered model information
        """
        print("🔍 Discovering trained models...")

        # Find all checkpoint directories
        checkpoint_dirs = []
        for pattern in ["checkpoints*", "checkpoints_*"]:
            checkpoint_dirs.extend(glob.glob(str(self.gnn_occupancy_dir / pattern)))

        models = []

        for checkpoint_dir in checkpoint_dirs:
            checkpoint_path = Path(checkpoint_dir)

            # Look for model files
            model_files = list(checkpoint_path.glob("model_temporal_*_best.pt"))
            config_files = list(checkpoint_path.glob("config.yaml"))

            if not model_files:
                continue

            # Handle ECC models that don't have config.yaml
            is_ecc_model = 'ecc' in checkpoint_path.name.lower()
            if not config_files and not is_ecc_model:
                continue

            for model_file in model_files:
                # Extract temporal window from filename
                temporal_window = self._extract_temporal_window(model_file.name)
                if temporal_window is None:
                    continue

                # Load config to get model details
                try:
                    if is_ecc_model:
                        # Create default config for ECC models
                        config = self._create_ecc_config(checkpoint_path, temporal_window)
                        config_file_path = str(checkpoint_path / "config.yaml")  # Virtual path
                    else:
                        config_file = config_files[0]
                        config_file_path = str(config_file)
                        with open(config_file, 'r') as f:
                            config = yaml.safe_load(f)

                    # Extract model information
                    model_config = config.get('model', {})
                    architecture = model_config.get('gnn_type', 'unknown')
                    num_layers = model_config.get('num_layers', 0)
                    hidden_dim = model_config.get('hidden_dim', 0)

                    # Load checkpoint to get additional info
                    checkpoint = torch.load(model_file, map_location='cpu')
                    best_f1 = checkpoint.get('best_f1', None)

                    # Calculate number of parameters
                    if is_ecc_model:
                        # For ECC models, estimate parameters from checkpoint
                        num_parameters = sum(p.numel() for p in checkpoint['model_state_dict'].values())
                    else:
                        model = create_model(config)
                        num_parameters = sum(p.numel() for p in model.parameters())

                    # Create model info
                    model_name = f"{architecture}_{num_layers}L_{hidden_dim}H_temp{temporal_window}_{checkpoint_path.name}"

                    model_info = ModelInfo(
                        name=model_name,
                        checkpoint_path=str(model_file),
                        config_path=config_file_path,
                        temporal_window=temporal_window,
                        architecture=architecture,
                        num_layers=num_layers,
                        hidden_dim=hidden_dim,
                        num_parameters=num_parameters,
                        best_f1=best_f1
                    )

                    models.append(model_info)
                    print(f"  ✅ Found: {model_name}")

                except Exception as e:
                    print(f"  ❌ Error processing {model_file}: {e}")
                    continue

        self.models = models
        print(f"🎯 Discovered {len(models)} trained models")
        return models

    def _extract_temporal_window(self, filename: str) -> Optional[int]:
        """Extract temporal window from model filename."""
        import re
        match = re.search(r'temporal_(\d+)_', filename)
        return int(match.group(1)) if match else None

    def _create_ecc_config(self, checkpoint_path: Path, temporal_window: int) -> Dict:
        """Create a default configuration for ECC models."""
        # Determine ECC model type from path
        is_hybrid = 'hybrid' in checkpoint_path.name.lower()

        # Default ECC configuration
        config = {
            'model': {
                'gnn_type': 'ecc_hybrid' if is_hybrid else 'ecc',
                'input_dim': 10,  # Standard input dimension
                'hidden_dim': 64,  # Default hidden dimension
                'output_dim': 1,
                'num_layers': 3,  # Default number of layers
                'dropout': 0.2,
                'skip_connections': True,
                'batch_norm': True,
                'layer_norm': False,
                'pooling': 'mean_max',
                'attention_heads': 4  # Not used for ECC but needed for compatibility
            },
            'data': {
                'temporal_window': temporal_window,
                'binary_mapping': {
                    'occupied': [1, 2, 3, 4],
                    'unoccupied': [0]
                }
            }
        }

        return config

    def load_test_data(self, temporal_window: int):
        """
        Load test data for a specific temporal window.

        Args:
            temporal_window: Temporal window size (3 or 5)

        Returns:
            DataLoader for test data
        """
        # Create a minimal config for data loading
        config = {
            'data': {
                'data_dir': str(self.data_dir),
                'batch_size': 1,  # Process one sample at a time for IoU calculation
                'num_workers': 0,  # Avoid multiprocessing issues
                'binary_mapping': {
                    'occupied': [1, 2, 3, 4],
                    'unoccupied': [0]
                }
            }
        }

        # Create dataset
        dataset = OccupancyDataset(
            root=str(self.data_dir),
            split='test',
            temporal_window=temporal_window,
            binary_mapping=config['data']['binary_mapping']
        )

        # Create data loader
        data_loader = DataLoader(
            dataset,
            batch_size=config['data']['batch_size'],
            shuffle=False,
            num_workers=config['data']['num_workers']
        )

        return data_loader

    def points_to_2d_grid(self,
                         positions: np.ndarray,
                         labels: np.ndarray,
                         predictions: Optional[np.ndarray] = None) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        Convert point cloud data to 2D occupancy grids.

        Args:
            positions: Point positions (N, 2) - X, Y coordinates
            labels: Ground truth labels (N,)
            predictions: Model predictions (N,) - optional

        Returns:
            Tuple of (ground_truth_grid, prediction_grid, grid_info)
        """
        # Calculate grid bounds with padding
        min_x, min_y = positions.min(axis=0) - self.grid_config.padding
        max_x, max_y = positions.max(axis=0) + self.grid_config.padding

        # Calculate grid dimensions
        grid_width = int(np.ceil((max_x - min_x) / self.grid_config.resolution))
        grid_height = int(np.ceil((max_y - min_y) / self.grid_config.resolution))

        # Initialize grids
        gt_grid = np.zeros((grid_height, grid_width), dtype=np.float32)
        pred_grid = np.zeros((grid_height, grid_width), dtype=np.float32) if predictions is not None else None

        # Count grids for averaging
        gt_count = np.zeros((grid_height, grid_width), dtype=np.int32)
        pred_count = np.zeros((grid_height, grid_width), dtype=np.int32) if predictions is not None else None

        # Convert points to grid indices
        grid_x = ((positions[:, 0] - min_x) / self.grid_config.resolution).astype(int)
        grid_y = ((positions[:, 1] - min_y) / self.grid_config.resolution).astype(int)

        # Clip to grid bounds
        grid_x = np.clip(grid_x, 0, grid_width - 1)
        grid_y = np.clip(grid_y, 0, grid_height - 1)

        # Aggregate ground truth using majority voting
        for i in range(len(positions)):
            y, x = grid_y[i], grid_x[i]
            gt_grid[y, x] += labels[i]
            gt_count[y, x] += 1

            if predictions is not None:
                pred_grid[y, x] += predictions[i]
                pred_count[y, x] += 1

        # Average the grids
        valid_gt = gt_count > 0
        gt_grid[valid_gt] = gt_grid[valid_gt] / gt_count[valid_gt]

        if predictions is not None:
            valid_pred = pred_count > 0
            pred_grid[valid_pred] = pred_grid[valid_pred] / pred_count[valid_pred]

            # Apply threshold to predictions
            pred_grid = (pred_grid > self.grid_config.threshold).astype(np.float32)

        # Apply threshold to ground truth (should already be binary, but ensure)
        gt_grid = (gt_grid > 0.5).astype(np.float32)

        # Grid information
        grid_info = {
            'min_x': min_x,
            'min_y': min_y,
            'max_x': max_x,
            'max_y': max_y,
            'grid_width': grid_width,
            'grid_height': grid_height,
            'resolution': self.grid_config.resolution,
            'total_points': len(positions),
            'occupied_points': np.sum(labels > 0.5),
            'grid_cells_with_data': np.sum(valid_gt)
        }

        return gt_grid, pred_grid, grid_info

    def calculate_2d_iou(self, gt_grid: np.ndarray, pred_grid: np.ndarray) -> float:
        """
        Calculate 2D IoU between ground truth and prediction grids.

        Args:
            gt_grid: Ground truth occupancy grid
            pred_grid: Prediction occupancy grid

        Returns:
            IoU score
        """
        # Flatten grids
        gt_flat = gt_grid.flatten()
        pred_flat = pred_grid.flatten()

        # Calculate intersection and union
        intersection = np.sum((gt_flat == 1) & (pred_flat == 1))
        union = np.sum((gt_flat == 1) | (pred_flat == 1))

        # Handle edge case where union is 0
        if union == 0:
            return 1.0 if intersection == 0 else 0.0

        return intersection / union

    def evaluate_model_iou(self, model_info: ModelInfo) -> IoUResults:
        """
        Evaluate a single model using 2D IoU metrics.

        Args:
            model_info: Information about the model to evaluate

        Returns:
            IoU evaluation results
        """
        print(f"📊 Evaluating model: {model_info.name}")

        # Load model configuration
        if 'ecc' in model_info.name.lower():
            # For ECC models, recreate config
            config = self._create_ecc_config(Path(model_info.checkpoint_path).parent, model_info.temporal_window)
        else:
            with open(model_info.config_path, 'r') as f:
                config = yaml.safe_load(f)

        # Load checkpoint first to get model state
        checkpoint = torch.load(model_info.checkpoint_path, map_location=self.device)

        # Try to create model with config, but handle mismatches
        try:
            model = create_model(config)
            model.load_state_dict(checkpoint['model_state_dict'])
        except RuntimeError as e:
            if "size mismatch" in str(e):
                print(f"  ⚠️ Model architecture mismatch detected")
                print(f"  🔧 Attempting to infer correct architecture from checkpoint...")

                # Try to infer model parameters from checkpoint
                print(f"  🔧 Inferring model parameters from checkpoint...")

                # Infer attention heads
                if 'convs.0.att' in checkpoint['model_state_dict']:
                    att_shape = checkpoint['model_state_dict']['convs.0.att'].shape
                    if len(att_shape) >= 2:
                        inferred_heads = att_shape[1]
                        print(f"  📊 Inferred attention heads: {inferred_heads}")
                        config['model']['attention_heads'] = inferred_heads

                # Infer input dimension
                if 'embedding.weight' in checkpoint['model_state_dict']:
                    embedding_shape = checkpoint['model_state_dict']['embedding.weight'].shape
                    if len(embedding_shape) >= 2:
                        inferred_input_dim = embedding_shape[1]
                        print(f"  📊 Inferred input dimension: {inferred_input_dim}")
                        config['model']['input_dim'] = inferred_input_dim

                # Try creating model again with inferred parameters
                try:
                    model = create_model(config)
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"  ✅ Successfully loaded model with corrected architecture")
                except RuntimeError:
                    # If we still can't load, try strict=False as last resort
                    print(f"  ⚠️ Still cannot load, trying strict=False")
                    model = create_model(config)
                    model.load_state_dict(checkpoint['model_state_dict'], strict=False)
            else:
                raise e

        model.to(self.device)
        model.eval()

        # Load test data
        test_loader = self.load_test_data(model_info.temporal_window)

        sample_ious = []
        spatial_iou_grids = []

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc=f"Processing {model_info.name}")):
                try:
                    # Move batch to device
                    batch = batch.to(self.device)

                    # Get model predictions
                    predictions = model(batch.x, batch.edge_index, batch.batch)
                    predictions = torch.sigmoid(predictions).cpu().numpy().flatten()

                    # Extract positions and labels
                    # Use X,Y coordinates from position data (first two columns)
                    try:
                        positions = batch.pos[:, :2].cpu().numpy()  # X, Y coordinates
                    except IndexError:
                        # Fallback: try to extract from node features
                        try:
                            positions = batch.x[:, 2:4].cpu().numpy()  # X,Y from features
                        except IndexError:
                            print(f"    ⚠️ Cannot extract X,Y positions from batch")
                            continue

                    labels = batch.y.cpu().numpy().flatten()

                    # Handle different prediction formats
                    if len(predictions) == 1 and len(labels) > 1:
                        # Graph-level prediction: broadcast to all nodes
                        predictions = np.full(len(labels), predictions[0])
                        print(f"    📊 Broadcasting graph-level prediction {predictions[0]:.4f} to {len(labels)} nodes")
                    elif len(predictions) != len(labels):
                        print(f"    ⚠️ Prediction-label size mismatch: {len(predictions)} vs {len(labels)}")
                        continue

                    # Convert to 2D grids
                    gt_grid, pred_grid, grid_info = self.points_to_2d_grid(
                        positions, labels, predictions
                    )

                    # Calculate IoU
                    iou = self.calculate_2d_iou(gt_grid, pred_grid)
                    sample_ious.append(iou)

                    # Store spatial information for analysis
                    spatial_iou_grids.append({
                        'gt_grid': gt_grid,
                        'pred_grid': pred_grid,
                        'grid_info': grid_info,
                        'iou': iou
                    })

                except Exception as e:
                    print(f"  ⚠️ Error processing batch {batch_idx}: {e}")
                    continue

        # Calculate statistics
        sample_ious = np.array(sample_ious)

        if len(sample_ious) == 0:
            print(f"  ⚠️ No valid IoU samples collected for {model_info.name}")
            results = IoUResults(
                model_name=model_info.name,
                sample_ious=[],
                mean_iou=0.0,
                median_iou=0.0,
                std_iou=0.0,
                min_iou=0.0,
                max_iou=0.0,
                grid_resolution=self.grid_config.resolution,
                total_samples=0
            )
        else:
            results = IoUResults(
                model_name=model_info.name,
                sample_ious=sample_ious.tolist(),
                mean_iou=float(np.mean(sample_ious)),
                median_iou=float(np.median(sample_ious)),
                std_iou=float(np.std(sample_ious)),
                min_iou=float(np.min(sample_ious)),
                max_iou=float(np.max(sample_ious)),
                grid_resolution=self.grid_config.resolution,
                total_samples=len(sample_ious)
            )

        # Store spatial analysis data
        self.spatial_analysis[model_info.name] = spatial_iou_grids

        print(f"  ✅ Mean IoU: {results.mean_iou:.4f} ± {results.std_iou:.4f}")
        return results

    def evaluate_all_models(self) -> Dict[str, IoUResults]:
        """
        Evaluate all discovered models using 2D IoU metrics.

        Returns:
            Dictionary of IoU results for all models
        """
        print("🚀 Starting comprehensive 2D IoU evaluation...")

        if not self.models:
            self.discover_models()

        results = {}

        for model_info in self.models:
            try:
                iou_results = self.evaluate_model_iou(model_info)
                results[model_info.name] = iou_results
                self.iou_results[model_info.name] = iou_results

                # Save intermediate results
                self._save_intermediate_results(model_info.name, iou_results)

            except Exception as e:
                print(f"❌ Failed to evaluate {model_info.name}: {e}")
                continue

        print(f"🎯 Completed evaluation of {len(results)} models")
        return results

    def _save_intermediate_results(self, model_name: str, results: IoUResults):
        """Save intermediate results to prevent data loss."""
        results_file = self.results_dir / f"{model_name}_iou_results.json"

        # Convert to serializable format
        results_dict = {
            'model_name': results.model_name,
            'sample_ious': results.sample_ious,
            'mean_iou': results.mean_iou,
            'median_iou': results.median_iou,
            'std_iou': results.std_iou,
            'min_iou': results.min_iou,
            'max_iou': results.max_iou,
            'grid_resolution': results.grid_resolution,
            'total_samples': results.total_samples,
            'timestamp': datetime.now().isoformat()
        }

        with open(results_file, 'w') as f:
            json.dump(results_dict, f, indent=2)

    def create_model_comparison_charts(self):
        """Create comprehensive model comparison visualizations."""
        print("📈 Creating model comparison charts...")

        if not self.iou_results:
            print("❌ No IoU results available. Run evaluation first.")
            return

        # Set up the plotting style
        try:
            plt.style.use('seaborn-v0_8')
        except:
            try:
                plt.style.use('seaborn')
            except:
                plt.style.use('default')
        sns.set_palette("husl")

        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))

        # 1. Bar chart with error bars
        ax1 = plt.subplot(2, 3, 1)
        self._create_mean_iou_bar_chart(ax1)

        # 2. Box plots
        ax2 = plt.subplot(2, 3, 2)
        self._create_iou_box_plots(ax2)

        # 3. Scatter plot: IoU vs Parameters
        ax3 = plt.subplot(2, 3, 3)
        self._create_iou_vs_parameters_scatter(ax3)

        # 4. Temporal window comparison
        ax4 = plt.subplot(2, 3, 4)
        self._create_temporal_window_comparison(ax4)

        # 5. Architecture comparison
        ax5 = plt.subplot(2, 3, 5)
        self._create_architecture_comparison(ax5)

        # 6. IoU distribution histograms
        ax6 = plt.subplot(2, 3, 6)
        self._create_iou_distribution_histograms(ax6)

        plt.tight_layout()
        plt.savefig(self.results_dir / 'model_comparison_charts.png', dpi=300, bbox_inches='tight')
        plt.savefig(self.results_dir / 'model_comparison_charts.pdf', bbox_inches='tight')
        plt.close()

        print(f"  ✅ Saved model comparison charts")

    def _create_mean_iou_bar_chart(self, ax):
        """Create bar chart of mean IoU with error bars."""
        model_names = []
        mean_ious = []
        std_ious = []

        for model_name, results in self.iou_results.items():
            model_names.append(model_name.replace('_', '\n'))
            mean_ious.append(results.mean_iou)
            std_ious.append(results.std_iou)

        # Sort by mean IoU
        sorted_data = sorted(zip(model_names, mean_ious, std_ious), key=lambda x: x[1], reverse=True)
        model_names, mean_ious, std_ious = zip(*sorted_data)

        bars = ax.bar(range(len(model_names)), mean_ious, yerr=std_ious,
                     capsize=5, alpha=0.8, edgecolor='black', linewidth=0.5)

        # Color bars by performance
        colors = plt.cm.RdYlGn([iou for iou in mean_ious])
        for bar, color in zip(bars, colors):
            bar.set_color(color)

        ax.set_xlabel('Models')
        ax.set_ylabel('Mean IoU')
        ax.set_title('Model Performance Comparison\n(Mean IoU ± Std Dev)')
        ax.set_xticks(range(len(model_names)))
        ax.set_xticklabels(model_names, rotation=45, ha='right', fontsize=8)
        ax.grid(True, alpha=0.3)

        # Add value labels on bars
        for i, (mean_iou, std_iou) in enumerate(zip(mean_ious, std_ious)):
            ax.text(i, mean_iou + std_iou + 0.01, f'{mean_iou:.3f}',
                   ha='center', va='bottom', fontsize=8, fontweight='bold')

    def _create_iou_box_plots(self, ax):
        """Create box plots showing IoU distributions."""
        data = []
        labels = []

        for model_name, results in self.iou_results.items():
            data.append(results.sample_ious)
            labels.append(model_name.replace('_', '\n'))

        # Sort by median IoU
        sorted_data = sorted(zip(data, labels), key=lambda x: np.median(x[0]), reverse=True)
        data, labels = zip(*sorted_data)

        bp = ax.boxplot(data, labels=labels, patch_artist=True, showfliers=True)

        # Color boxes by median performance
        medians = [np.median(d) for d in data]
        colors = plt.cm.RdYlGn(medians)
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax.set_xlabel('Models')
        ax.set_ylabel('IoU Distribution')
        ax.set_title('IoU Score Distributions')
        ax.tick_params(axis='x', rotation=45, labelsize=8)
        ax.grid(True, alpha=0.3)

    def _create_iou_vs_parameters_scatter(self, ax):
        """Create scatter plot of IoU vs number of parameters."""
        mean_ious = []
        num_params = []
        model_names = []
        architectures = []

        for model_info in self.models:
            if model_info.name in self.iou_results:
                mean_ious.append(self.iou_results[model_info.name].mean_iou)
                num_params.append(model_info.num_parameters)
                model_names.append(model_info.name)
                architectures.append(model_info.architecture)

        # Create scatter plot with different colors for architectures
        unique_archs = list(set(architectures))
        colors = plt.cm.Set1(np.linspace(0, 1, len(unique_archs)))
        arch_colors = {arch: color for arch, color in zip(unique_archs, colors)}

        for arch in unique_archs:
            arch_ious = [iou for iou, a in zip(mean_ious, architectures) if a == arch]
            arch_params = [params for params, a in zip(num_params, architectures) if a == arch]
            ax.scatter(arch_params, arch_ious, c=[arch_colors[arch]],
                      label=arch.upper(), s=100, alpha=0.7, edgecolors='black')

        ax.set_xlabel('Number of Parameters')
        ax.set_ylabel('Mean IoU')
        ax.set_title('IoU vs Model Complexity')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Add efficiency line (IoU per parameter)
        if mean_ious and num_params:
            efficiency = [iou/params*1e6 for iou, params in zip(mean_ious, num_params)]
            best_efficiency_idx = np.argmax(efficiency)
            ax.annotate(f'Most Efficient:\n{model_names[best_efficiency_idx].split("_")[0]}',
                       xy=(num_params[best_efficiency_idx], mean_ious[best_efficiency_idx]),
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

    def _create_temporal_window_comparison(self, ax):
        """Compare performance across temporal windows."""
        temp3_results = []
        temp5_results = []

        for model_info in self.models:
            if model_info.name in self.iou_results:
                if model_info.temporal_window == 3:
                    temp3_results.append(self.iou_results[model_info.name].mean_iou)
                elif model_info.temporal_window == 5:
                    temp5_results.append(self.iou_results[model_info.name].mean_iou)

        data = [temp3_results, temp5_results]
        labels = ['Temporal-3', 'Temporal-5']

        bp = ax.boxplot(data, labels=labels, patch_artist=True)
        colors = ['lightblue', 'lightcoral']
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax.set_ylabel('Mean IoU')
        ax.set_title('Temporal Window Comparison')
        ax.grid(True, alpha=0.3)

        # Add statistical test
        if len(temp3_results) > 1 and len(temp5_results) > 1:
            _, p_value = stats.mannwhitneyu(temp3_results, temp5_results, alternative='two-sided')
            ax.text(0.5, 0.95, f'Mann-Whitney U test\np-value: {p_value:.4f}',
                   transform=ax.transAxes, ha='center', va='top',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    def _create_architecture_comparison(self, ax):
        """Compare performance across different architectures."""
        arch_results = {}

        for model_info in self.models:
            if model_info.name in self.iou_results:
                arch = model_info.architecture
                if arch not in arch_results:
                    arch_results[arch] = []
                arch_results[arch].append(self.iou_results[model_info.name].mean_iou)

        data = list(arch_results.values())
        labels = [arch.upper() for arch in arch_results.keys()]

        bp = ax.boxplot(data, labels=labels, patch_artist=True)
        colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax.set_ylabel('Mean IoU')
        ax.set_title('Architecture Comparison')
        ax.grid(True, alpha=0.3)

    def _create_iou_distribution_histograms(self, ax):
        """Create overlapping histograms of IoU distributions."""
        # Get top 3 models by mean IoU
        sorted_results = sorted(self.iou_results.items(),
                              key=lambda x: x[1].mean_iou, reverse=True)[:3]

        colors = ['red', 'blue', 'green']
        alphas = [0.7, 0.6, 0.5]

        for i, (model_name, results) in enumerate(sorted_results):
            ax.hist(results.sample_ious, bins=30, alpha=alphas[i],
                   color=colors[i], label=model_name.split('_')[0], density=True)

        ax.set_xlabel('IoU Score')
        ax.set_ylabel('Density')
        ax.set_title('IoU Score Distributions\n(Top 3 Models)')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def create_spatial_analysis_heatmaps(self):
        """Create spatial analysis heatmaps showing IoU performance across different regions."""
        print("🗺️ Creating spatial analysis heatmaps...")

        if not self.spatial_analysis:
            print("❌ No spatial analysis data available. Run evaluation first.")
            return

        # Get top 3 models for detailed analysis
        sorted_results = sorted(self.iou_results.items(),
                              key=lambda x: x[1].mean_iou, reverse=True)[:3]

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()

        for idx, (model_name, results) in enumerate(sorted_results):
            if model_name not in self.spatial_analysis:
                continue

            # Calculate average spatial IoU
            spatial_data = self.spatial_analysis[model_name]
            avg_spatial_iou = self._calculate_average_spatial_iou(spatial_data)

            # Check if we have valid spatial data
            if avg_spatial_iou['avg_gt'].size == 0:
                # Create placeholder plots for models with no spatial data
                ax1 = axes[idx * 2]
                ax1.text(0.5, 0.5, f'{model_name.split("_")[0]}\nNo Spatial Data\nAvailable',
                        ha='center', va='center', transform=ax1.transAxes, fontsize=12)
                ax1.set_title(f'{model_name.split("_")[0]}\nGround Truth Density')
                ax1.set_xticks([])
                ax1.set_yticks([])

                ax2 = axes[idx * 2 + 1]
                ax2.text(0.5, 0.5, f'{model_name.split("_")[0]}\nNo Spatial Data\nAvailable',
                        ha='center', va='center', transform=ax2.transAxes, fontsize=12)
                ax2.set_title(f'{model_name.split("_")[0]}\nSpatial IoU Performance')
                ax2.set_xticks([])
                ax2.set_yticks([])
            else:
                # Plot ground truth heatmap
                ax1 = axes[idx * 2]
                im1 = ax1.imshow(avg_spatial_iou['avg_gt'], cmap='Blues', aspect='auto')
                ax1.set_title(f'{model_name.split("_")[0]}\nGround Truth Density')
                plt.colorbar(im1, ax=ax1, fraction=0.046, pad=0.04)

                # Plot IoU heatmap
                ax2 = axes[idx * 2 + 1]
                im2 = ax2.imshow(avg_spatial_iou['avg_iou'], cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
                ax2.set_title(f'{model_name.split("_")[0]}\nSpatial IoU Performance')
                plt.colorbar(im2, ax=ax2, fraction=0.046, pad=0.04)

            # Add grid info
            for ax in [ax1, ax2]:
                ax.set_xlabel('X Grid Cells')
                ax.set_ylabel('Y Grid Cells')

        plt.tight_layout()
        plt.savefig(self.results_dir / 'spatial_analysis_heatmaps.png', dpi=300, bbox_inches='tight')
        plt.savefig(self.results_dir / 'spatial_analysis_heatmaps.pdf', bbox_inches='tight')
        plt.close()

        print(f"  ✅ Saved spatial analysis heatmaps")

    def _calculate_average_spatial_iou(self, spatial_data: List[Dict]) -> Dict[str, np.ndarray]:
        """Calculate average spatial IoU across all samples for a model."""
        if not spatial_data:
            return {'avg_gt': np.array([]), 'avg_iou': np.array([])}

        # Find common grid size (use the most common size)
        grid_sizes = [(data['grid_info']['grid_height'], data['grid_info']['grid_width'])
                     for data in spatial_data]
        from collections import Counter
        most_common_size = Counter(grid_sizes).most_common(1)[0][0]

        # Filter data with the most common grid size
        filtered_data = [data for data in spatial_data
                        if (data['grid_info']['grid_height'], data['grid_info']['grid_width']) == most_common_size]

        if not filtered_data:
            return {'avg_gt': np.array([]), 'avg_iou': np.array([])}

        # Stack grids and calculate averages
        gt_grids = np.stack([data['gt_grid'] for data in filtered_data])
        pred_grids = np.stack([data['pred_grid'] for data in filtered_data])

        avg_gt = np.mean(gt_grids, axis=0)

        # Calculate per-cell IoU
        intersection = np.sum((gt_grids == 1) & (pred_grids == 1), axis=0)
        union = np.sum((gt_grids == 1) | (pred_grids == 1), axis=0)

        # Handle division by zero
        avg_iou = np.divide(intersection, union, out=np.ones_like(intersection, dtype=float), where=union!=0)
        avg_iou[union == 0] = 1.0  # Perfect score when both are empty

        return {'avg_gt': avg_gt, 'avg_iou': avg_iou}

    def create_individual_model_analysis(self):
        """Create detailed analysis for individual models."""
        print("🔬 Creating individual model analysis...")

        # Filter out models with no valid results
        valid_results = {name: results for name, results in self.iou_results.items()
                        if results.total_samples > 0}

        if not valid_results:
            print("  ⚠️ No valid results available for individual analysis")
            return

        # Create analysis for top 3 models with valid results
        sorted_results = sorted(valid_results.items(),
                              key=lambda x: x[1].mean_iou, reverse=True)[:3]

        for model_name, results in sorted_results:
            self._create_single_model_analysis(model_name, results)

        print(f"  ✅ Created individual model analyses")

    def _create_single_model_analysis(self, model_name: str, results: IoUResults):
        """Create detailed analysis for a single model."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. IoU histogram
        ax1 = axes[0, 0]
        ax1.hist(results.sample_ious, bins=30, alpha=0.7, edgecolor='black')
        ax1.axvline(results.mean_iou, color='red', linestyle='--', linewidth=2, label=f'Mean: {results.mean_iou:.3f}')
        ax1.axvline(results.median_iou, color='blue', linestyle='--', linewidth=2, label=f'Median: {results.median_iou:.3f}')
        ax1.set_xlabel('IoU Score')
        ax1.set_ylabel('Frequency')
        ax1.set_title(f'{model_name}\nIoU Distribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. Performance over samples (sorted)
        ax2 = axes[0, 1]
        sorted_ious = sorted(results.sample_ious)
        ax2.plot(sorted_ious, alpha=0.7)
        ax2.axhline(results.mean_iou, color='red', linestyle='--', alpha=0.7)
        ax2.fill_between(range(len(sorted_ious)),
                        [results.mean_iou - results.std_iou] * len(sorted_ious),
                        [results.mean_iou + results.std_iou] * len(sorted_ious),
                        alpha=0.2, color='red')
        ax2.set_xlabel('Sample Index (sorted)')
        ax2.set_ylabel('IoU Score')
        ax2.set_title('IoU Performance (Sorted)')
        ax2.grid(True, alpha=0.3)

        # 3. Failure cases (lowest IoU samples)
        ax3 = axes[1, 0]
        failure_threshold = np.percentile(results.sample_ious, 10)  # Bottom 10%
        failure_cases = [iou for iou in results.sample_ious if iou <= failure_threshold]
        success_cases = [iou for iou in results.sample_ious if iou > failure_threshold]

        ax3.hist([failure_cases, success_cases], bins=20, alpha=0.7,
                label=[f'Failure Cases (<{failure_threshold:.2f})', 'Success Cases'],
                color=['red', 'green'])
        ax3.set_xlabel('IoU Score')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Failure vs Success Cases')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. Performance statistics
        ax4 = axes[1, 1]
        stats_data = [
            ('Mean', results.mean_iou),
            ('Median', results.median_iou),
            ('Std Dev', results.std_iou),
            ('Min', results.min_iou),
            ('Max', results.max_iou),
            ('Q1', np.percentile(results.sample_ious, 25)),
            ('Q3', np.percentile(results.sample_ious, 75))
        ]

        stats_names, stats_values = zip(*stats_data)
        bars = ax4.bar(stats_names, stats_values, alpha=0.7, edgecolor='black')

        # Color bars based on values
        colors = plt.cm.RdYlGn([v for v in stats_values])
        for bar, color in zip(bars, colors):
            bar.set_color(color)

        ax4.set_ylabel('Value')
        ax4.set_title('Performance Statistics')
        ax4.tick_params(axis='x', rotation=45)
        ax4.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, value in zip(bars, stats_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()

        # Save individual model analysis
        safe_name = model_name.replace('/', '_').replace('\\', '_')
        plt.savefig(self.results_dir / f'individual_analysis_{safe_name}.png', dpi=300, bbox_inches='tight')
        plt.close()

    def generate_summary_report(self):
        """Generate comprehensive summary report."""
        print("📋 Generating comprehensive summary report...")

        # Create summary DataFrame
        summary_data = []
        for model_info in self.models:
            if model_info.name in self.iou_results:
                results = self.iou_results[model_info.name]
                summary_data.append({
                    'Model': model_info.name,
                    'Architecture': model_info.architecture.upper(),
                    'Temporal_Window': model_info.temporal_window,
                    'Num_Layers': model_info.num_layers,
                    'Hidden_Dim': model_info.hidden_dim,
                    'Num_Parameters': model_info.num_parameters,
                    'Mean_IoU': results.mean_iou,
                    'Median_IoU': results.median_iou,
                    'Std_IoU': results.std_iou,
                    'Min_IoU': results.min_iou,
                    'Max_IoU': results.max_iou,
                    'Total_Samples': results.total_samples,
                    'Best_F1': model_info.best_f1 or 'N/A',
                    'IoU_per_Parameter': results.mean_iou / model_info.num_parameters * 1e6
                })

        df = pd.DataFrame(summary_data)
        df = df.sort_values('Mean_IoU', ascending=False)

        # Save detailed CSV
        df.to_csv(self.results_dir / 'iou_detailed_results.csv', index=False)

        # Create summary statistics
        summary_stats = {
            'evaluation_timestamp': datetime.now().isoformat(),
            'total_models_evaluated': len(df),
            'best_model': {
                'name': df.iloc[0]['Model'],
                'mean_iou': df.iloc[0]['Mean_IoU'],
                'architecture': df.iloc[0]['Architecture']
            },
            'architecture_performance': df.groupby('Architecture')['Mean_IoU'].agg(['mean', 'std', 'count']).to_dict(),
            'temporal_window_performance': df.groupby('Temporal_Window')['Mean_IoU'].agg(['mean', 'std', 'count']).to_dict(),
            'overall_statistics': {
                'mean_iou_across_models': df['Mean_IoU'].mean(),
                'std_iou_across_models': df['Mean_IoU'].std(),
                'best_iou': df['Mean_IoU'].max(),
                'worst_iou': df['Mean_IoU'].min()
            }
        }

        # Save summary statistics
        with open(self.results_dir / 'summary_statistics.json', 'w') as f:
            json.dump(summary_stats, f, indent=2, default=str)

        print(f"  ✅ Saved detailed results and summary statistics")
        return df, summary_stats

    def create_comprehensive_pdf_report(self):
        """Create a comprehensive PDF report with all visualizations."""
        print("📄 Creating comprehensive PDF report...")

        pdf_path = self.results_dir / 'iou_summary_report.pdf'

        with PdfPages(pdf_path) as pdf:
            # Page 1: Model comparison charts
            if os.path.exists(self.results_dir / 'model_comparison_charts.png'):
                fig = plt.figure(figsize=(11, 8.5))
                img = plt.imread(self.results_dir / 'model_comparison_charts.png')
                plt.imshow(img)
                plt.axis('off')
                plt.title('Model Comparison Charts', fontsize=16, fontweight='bold', pad=20)
                pdf.savefig(fig, bbox_inches='tight')
                plt.close()

            # Page 2: Spatial analysis heatmaps
            if os.path.exists(self.results_dir / 'spatial_analysis_heatmaps.png'):
                fig = plt.figure(figsize=(11, 8.5))
                img = plt.imread(self.results_dir / 'spatial_analysis_heatmaps.png')
                plt.imshow(img)
                plt.axis('off')
                plt.title('Spatial Analysis Heatmaps', fontsize=16, fontweight='bold', pad=20)
                pdf.savefig(fig, bbox_inches='tight')
                plt.close()

            # Page 3: Summary table
            if self.iou_results:
                fig, ax = plt.subplots(figsize=(11, 8.5))

                # Create summary table
                summary_data = []
                for model_info in self.models:
                    if model_info.name in self.iou_results:
                        results = self.iou_results[model_info.name]
                        summary_data.append([
                            model_info.architecture.upper(),
                            f"T{model_info.temporal_window}",
                            f"{model_info.num_layers}L",
                            f"{model_info.hidden_dim}H",
                            f"{model_info.num_parameters:,}",
                            f"{results.mean_iou:.4f}",
                            f"{results.std_iou:.4f}",
                            f"{results.median_iou:.4f}"
                        ])

                # Sort by mean IoU
                summary_data.sort(key=lambda x: float(x[5]), reverse=True)

                headers = ['Arch', 'Temp', 'Layers', 'Hidden', 'Params', 'Mean IoU', 'Std IoU', 'Median IoU']

                table = ax.table(cellText=summary_data, colLabels=headers,
                               cellLoc='center', loc='center')
                table.auto_set_font_size(False)
                table.set_fontsize(9)
                table.scale(1.2, 1.5)

                # Color code the table
                for i in range(len(summary_data)):
                    mean_iou = float(summary_data[i][5])
                    color = plt.cm.RdYlGn(mean_iou)
                    table[(i+1, 5)].set_facecolor(color)

                ax.axis('off')
                ax.set_title('Model Performance Summary', fontsize=16, fontweight='bold', pad=20)
                pdf.savefig(fig, bbox_inches='tight')
                plt.close()

        print(f"  ✅ Saved comprehensive PDF report: {pdf_path}")

    def run_complete_evaluation(self):
        """Run the complete 2D IoU evaluation pipeline."""
        print("🚀 Starting Complete 2D IoU Evaluation Pipeline")
        print("=" * 60)

        start_time = datetime.now()

        try:
            # Step 1: Discover models
            self.discover_models()

            # Step 2: Evaluate all models
            self.evaluate_all_models()

            # Step 3: Create visualizations
            self.create_model_comparison_charts()
            self.create_spatial_analysis_heatmaps()
            self.create_individual_model_analysis()

            # Step 4: Generate reports
            self.generate_summary_report()
            self.create_comprehensive_pdf_report()

            # Step 5: Print summary
            end_time = datetime.now()
            duration = end_time - start_time

            print("\n" + "=" * 60)
            print("🎉 EVALUATION COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print(f"⏱️  Total Duration: {duration}")
            print(f"📊 Models Evaluated: {len(self.iou_results)}")
            print(f"📁 Results Directory: {self.results_dir}")

            if self.iou_results:
                best_model = max(self.iou_results.items(), key=lambda x: x[1].mean_iou)
                print(f"🏆 Best Model: {best_model[0]}")
                print(f"🎯 Best IoU: {best_model[1].mean_iou:.4f} ± {best_model[1].std_iou:.4f}")

            print("\n📋 Generated Files:")
            for file_path in sorted(self.results_dir.glob("*")):
                print(f"  📄 {file_path.name}")

            return True

        except Exception as e:
            print(f"\n❌ Evaluation failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function."""
    print("🔬 Comprehensive 2D IoU Evaluation System for GNN Occupancy Prediction")
    print("=" * 80)

    # Configuration
    grid_config = GridConfig(
        resolution=0.1,  # 10cm grid cells
        padding=0.5,     # 50cm padding
        threshold=0.5    # 50% threshold for predictions
    )

    # Create evaluator
    evaluator = Comprehensive2DIoUEvaluator(
        gnn_occupancy_dir=".",
        data_dir="../data/07_gnn_ready",
        results_dir="results/2d_iou_evaluation",
        grid_config=grid_config
    )

    # Run complete evaluation
    success = evaluator.run_complete_evaluation()

    if success:
        print("\n✅ All done! Check the results directory for detailed analysis.")
    else:
        print("\n❌ Evaluation failed. Check the error messages above.")

    return success


if __name__ == "__main__":
    main()
