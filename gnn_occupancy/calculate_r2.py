#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
from sklearn.metrics import r2_score
import argparse
from tqdm import tqdm

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])

from data import create_data_loaders
from model import create_model


def calculate_r2(model_dir, temporal_window):
    """
    Calculate R-squared for a trained model.

    Args:
        model_dir: Directory containing the model checkpoint and config
        temporal_window: Size of temporal window

    Returns:
        R-squared value
    """
    # Load configuration
    config_path = os.path.join(model_dir, "config.yaml")
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)

    # Set device
    device = torch.device(config["training"]["device"])

    # Create data loaders
    # Update config to use only the specified temporal window
    config_copy = config.copy()
    config_copy["data"]["temporal_windows"] = [temporal_window]
    data_loaders = create_data_loaders(config_copy)

    # Print available keys for debugging
    print(f"Available data loader keys: {list(data_loaders.keys())}")

    # Use the available key
    if f"temporal_{temporal_window}" in data_loaders:
        test_loader = data_loaders[f"temporal_{temporal_window}"]
    else:
        # Try other possible keys
        for key in data_loaders.keys():
            if str(temporal_window) in key:
                test_loader = data_loaders[key]
                print(f"Using data loader with key: {key}")
                break
        else:
            raise ValueError(f"No data loader found for temporal window {temporal_window}")

    # Create model
    model = create_model(config_copy)

    # Load checkpoint
    checkpoint_path = os.path.join(
        config["training"]["checkpoint_dir"],
        f"model_temporal_{temporal_window}_best.pt"
    )
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint["model_state_dict"])

    # Move model to device
    model = model.to(device)

    # Set model to evaluation mode
    model.eval()

    # Collect predictions and targets
    all_targets = []
    all_probs = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc=f"Evaluating temporal_{temporal_window}"):
            # Move batch to device
            batch = batch.to(device)

            # Forward pass
            outputs = model(batch)

            # Get predictions
            probs = torch.sigmoid(outputs).cpu().numpy()

            # Get targets
            targets = batch.y.cpu().numpy()

            # Append to lists
            all_targets.extend(targets)
            all_probs.extend(probs)

    # Convert to numpy arrays
    all_targets = np.array(all_targets)
    all_probs = np.array(all_probs)

    # Calculate R-squared
    r2 = r2_score(all_targets, all_probs)

    return r2


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Calculate R-squared for a trained model")
    parser.add_argument(
        "--model_dir",
        type=str,
        required=True,
        help="Directory containing the model checkpoint and config",
    )
    parser.add_argument(
        "--temporal_window",
        type=int,
        required=True,
        help="Size of temporal window",
    )

    args = parser.parse_args()

    r2 = calculate_r2(args.model_dir, args.temporal_window)

    print(f"R-squared: {r2:.4f}")

    # Save to results file
    results_path = os.path.join(args.model_dir, "evaluation_results.md")
    if os.path.exists(results_path):
        with open(results_path, "r") as f:
            content = f.read()

        # Check if R-squared is already in the file
        if "R-squared" not in content:
            # Add R-squared to Test Metrics section
            content = content.replace(
                "### Test Metrics (Best Model)",
                "### Test Metrics (Best Model)\n- R-squared: {:.4f}".format(r2)
            )

            with open(results_path, "w") as f:
                f.write(content)

            print(f"Added R-squared to {results_path}")
    else:
        print(f"Results file {results_path} not found")
