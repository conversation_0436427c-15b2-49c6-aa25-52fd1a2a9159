#!/usr/bin/env python3

import os
import torch
import numpy as np
import random
import yaml
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
from torch_geometric.data import Data


def set_seed(seed: int):
    """
    Set random seed for reproducibility.

    Args:
        seed: Random seed
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def setup_logging(log_dir: str = "logs"):
    """
    Set up logging.

    Args:
        log_dir: Directory to save logs
    """
    os.makedirs(log_dir, exist_ok=True)

    # Create logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create file handler
    file_handler = logging.FileHandler(os.path.join(log_dir, "training.log"))
    file_handler.setLevel(logging.INFO)

    # Create formatter
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger


def load_config(config_path: str) -> Dict:
    """
    Load configuration from YAML file.

    Args:
        config_path: Path to configuration file

    Returns:
        Configuration dictionary
    """
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)

    return config


def save_config(config: Dict, config_path: str):
    """
    Save configuration to YAML file.

    Args:
        config: Configuration dictionary
        config_path: Path to save configuration file
    """
    with open(config_path, "w") as f:
        yaml.dump(config, f, default_flow_style=False)


def visualize_point_cloud(
    data: Data,
    title: str = "Point Cloud Visualization",
    save_path: Optional[str] = None,
    show: bool = True,
):
    """
    Visualize a point cloud.

    Args:
        data: PyTorch Geometric Data object
        title: Plot title
        save_path: Path to save the visualization
        show: Whether to show the plot
    """
    # Create 2D scatter plot (only X and Y)
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111)

    # Get positions and labels
    pos = data.pos.cpu().numpy()
    labels = data.y.cpu().numpy()

    # Define colors for each label
    label_colors = {
        0: "blue",   # Unknown (unoccupied)
        1: "red",    # Occupied
    }

    # Plot points (only X and Y)
    colors = [label_colors.get(label, "gray") for label in labels]
    ax.scatter(pos[:, 0], pos[:, 1], c=colors, alpha=0.5)

    # Set labels
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_title(title)

    # Add legend
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], marker="o", color="w", markerfacecolor="blue", markersize=10, label="Unoccupied"),
        Line2D([0], [0], marker="o", color="w", markerfacecolor="red", markersize=10, label="Occupied"),
    ]
    ax.legend(handles=legend_elements, loc="upper right")

    # Save figure
    if save_path:
        plt.savefig(save_path)

    # Show figure
    if show:
        plt.show()
    else:
        plt.close()


def count_parameters(model: torch.nn.Module) -> int:
    """
    Count the number of trainable parameters in a model.

    Args:
        model: PyTorch model

    Returns:
        Number of trainable parameters
    """
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


def get_device() -> torch.device:
    """
    Get the device to use for training.

    Returns:
        Device
    """
    return torch.device("cuda" if torch.cuda.is_available() else "cpu")


def create_edge_features(
    pos: torch.Tensor,
    edge_index: torch.Tensor,
    feature_type: str = "distance",
) -> torch.Tensor:
    """
    Create edge features based on node positions.

    Args:
        pos: Node positions
        edge_index: Edge indices
        feature_type: Type of edge features to create

    Returns:
        Edge features
    """
    # Get source and target node indices
    src, dst = edge_index

    if feature_type == "distance":
        # Compute Euclidean distance between nodes (only X and Y)
        edge_attr = torch.norm(pos[src, :2] - pos[dst, :2], dim=1, keepdim=True)
    elif feature_type == "displacement":
        # Compute displacement vector between nodes (only X and Y)
        edge_attr = pos[dst, :2] - pos[src, :2]
    elif feature_type == "combined":
        # Compute both distance and displacement (only X and Y)
        distance = torch.norm(pos[src, :2] - pos[dst, :2], dim=1, keepdim=True)
        displacement = pos[dst, :2] - pos[src, :2]
        edge_attr = torch.cat([distance, displacement], dim=1)
    else:
        raise ValueError(f"Unknown edge feature type: {feature_type}")

    return edge_attr


def create_temporal_encoding(
    temporal_offset: torch.Tensor,
    max_offset: int = 2,
    encoding_dim: int = 1,
) -> torch.Tensor:
    """
    Create temporal encoding for nodes.

    Args:
        temporal_offset: Temporal offset of each node
        max_offset: Maximum temporal offset
        encoding_dim: Dimension of the encoding

    Returns:
        Temporal encoding
    """
    if encoding_dim == 1:
        # Simple normalized offset
        return temporal_offset.float() / max_offset
    else:
        # Sinusoidal encoding
        encoding = torch.zeros(temporal_offset.size(0), encoding_dim)
        for i in range(encoding_dim):
            if i % 2 == 0:
                encoding[:, i] = torch.sin(temporal_offset.float() * (2 ** (i // 2)) * np.pi / max_offset)
            else:
                encoding[:, i] = torch.cos(temporal_offset.float() * (2 ** (i // 2)) * np.pi / max_offset)
        return encoding
