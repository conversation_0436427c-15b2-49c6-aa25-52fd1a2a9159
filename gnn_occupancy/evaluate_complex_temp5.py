#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
from typing import Dict, List, Tuple, Optional, Union, Any
import argparse

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    confusion_matrix,
    roc_curve,
    auc,
    mean_squared_error,
    mean_absolute_error,
    r2_score,
    explained_variance_score,
    max_error,
    median_absolute_error,
)
import seaborn as sns

from data import create_data_loaders
from model import create_model


class ComplexGATv2Temp5Evaluator:
    """Evaluator for the Complex GATv2 model with temporal window 5."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device(config["training"]["device"])
        
        # Create visualization directory
        os.makedirs(config["evaluation"]["visualization"]["save_dir"], exist_ok=True)
    
    def evaluate(
        self,
        model: torch.nn.Module,
        test_loader: torch.utils.data.DataLoader,
        temporal_window: int,
    ) -> Dict[str, float]:
        """Evaluate the Complex GATv2 model with temporal window 5."""
        model = model.to(self.device)
        model.eval()
        
        # Initialize metrics
        all_preds = []
        all_targets = []
        all_probs = []
        all_data = []
        
        # Evaluation loop
        with torch.no_grad():
            for batch in tqdm(test_loader, desc=f"Evaluating Complex GATv2 (temporal_{temporal_window})"):
                batch = batch.to(self.device)
                
                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)
                
                # Convert logits to predictions and probabilities
                probs = torch.sigmoid(logits).cpu().numpy()
                preds = probs > 0.5
                
                # Handle graph-level predictions
                if logits.size(0) != batch.y.size(0):
                    graph_labels = []
                    for i in range(batch.num_graphs):
                        mask = batch.batch == i
                        graph_label = batch.y[mask].float().mean().round()
                        graph_labels.append(graph_label)
                    targets = torch.tensor(graph_labels).cpu().numpy()
                else:
                    targets = batch.y.cpu().numpy()
                
                all_probs.extend(probs.flatten())
                all_preds.extend(preds.flatten())
                all_targets.extend(targets.flatten())
                
                # Store data for visualization
                for i in range(batch.num_graphs):
                    mask = batch.batch.cpu() == i
                    data = {
                        "pos": batch.pos[mask].cpu().numpy(),
                        "y_true": batch.y[mask].cpu().numpy(),
                        "y_pred": preds[i],
                        "prob": probs[i],
                    }
                    all_data.append(data)
        
        # Convert to numpy arrays
        all_targets = np.array(all_targets)
        all_preds = np.array(all_preds)
        all_probs = np.array(all_probs)
        
        # Compute comprehensive metrics
        metrics = self._compute_comprehensive_metrics(all_targets, all_preds, all_probs)
        
        # Print metrics
        print(f"\n🎯 Complex GATv2 Evaluation Results (Temporal Window: {temporal_window})")
        print("=" * 70)
        
        print("\n📊 Classification Metrics:")
        print(f"  Accuracy:  {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)")
        print(f"  Precision: {metrics['precision']:.4f} ({metrics['precision']*100:.2f}%)")
        print(f"  Recall:    {metrics['recall']:.4f} ({metrics['recall']*100:.2f}%)")
        print(f"  F1 Score:  {metrics['f1']:.4f} ({metrics['f1']*100:.2f}%)")
        print(f"  ROC AUC:   {metrics['roc_auc']:.4f} ({metrics['roc_auc']*100:.2f}%)")
        
        print("\n📈 Regression Metrics:")
        print(f"  R² Score:           {metrics['r2_score']:.4f}")
        print(f"  MSE:                {metrics['mse']:.4f}")
        print(f"  RMSE:               {metrics['rmse']:.4f}")
        print(f"  MAE:                {metrics['mae']:.4f}")
        print(f"  Explained Variance: {metrics['explained_variance']:.4f}")
        print(f"  Max Error:          {metrics['max_error']:.4f}")
        print(f"  Median AE:          {metrics['median_ae']:.4f}")
        
        # Generate visualizations
        self._generate_visualizations(all_targets, all_preds, all_probs, all_data, temporal_window)
        
        return metrics
    
    def _compute_comprehensive_metrics(
        self,
        targets: np.ndarray,
        preds: np.ndarray,
        probs: np.ndarray,
    ) -> Dict[str, float]:
        """Compute comprehensive evaluation metrics."""
        
        # Classification metrics
        classification_metrics = {
            "accuracy": accuracy_score(targets, preds),
            "precision": precision_score(targets, preds, zero_division=0),
            "recall": recall_score(targets, preds, zero_division=0),
            "f1": f1_score(targets, preds, zero_division=0),
            "roc_auc": roc_auc_score(targets, probs) if len(np.unique(targets)) > 1 else 0.5,
        }
        
        # Regression metrics (treating probabilities as continuous predictions)
        regression_metrics = {
            "r2_score": r2_score(targets, probs),
            "mse": mean_squared_error(targets, probs),
            "rmse": np.sqrt(mean_squared_error(targets, probs)),
            "mae": mean_absolute_error(targets, probs),
            "explained_variance": explained_variance_score(targets, probs),
            "max_error": max_error(targets, probs),
            "median_ae": median_absolute_error(targets, probs),
        }
        
        # Combine all metrics
        metrics = {**classification_metrics, **regression_metrics}
        
        return metrics
    
    def _generate_visualizations(
        self,
        targets: np.ndarray,
        preds: np.ndarray,
        probs: np.ndarray,
        data: List[Dict],
        temporal_window: int,
    ):
        """Generate comprehensive visualizations."""
        
        # 1. Confusion Matrix
        cm = confusion_matrix(targets, preds)
        plt.figure(figsize=(10, 8))
        sns.heatmap(
            cm,
            annot=True,
            fmt="d",
            cmap="Blues",
            xticklabels=["Unoccupied", "Occupied"],
            yticklabels=["Unoccupied", "Occupied"],
            cbar_kws={'label': 'Count'}
        )
        plt.xlabel("Predicted", fontsize=12)
        plt.ylabel("True", fontsize=12)
        plt.title(f"Complex GATv2 - Confusion Matrix\n(Temporal Window: {temporal_window})", fontsize=14)
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            f"complex_gatv2_confusion_matrix_temporal_{temporal_window}.png"
        ), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. ROC Curve
        fpr, tpr, _ = roc_curve(targets, probs)
        roc_auc = auc(fpr, tpr)
        plt.figure(figsize=(10, 8))
        plt.plot(
            fpr,
            tpr,
            color="darkorange",
            lw=3,
            label=f"Complex GATv2 ROC (AUC = {roc_auc:.3f})",
        )
        plt.plot([0, 1], [0, 1], color="navy", lw=2, linestyle="--", alpha=0.8)
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel("False Positive Rate", fontsize=12)
        plt.ylabel("True Positive Rate", fontsize=12)
        plt.title(f"Complex GATv2 - ROC Curve\n(Temporal Window: {temporal_window})", fontsize=14)
        plt.legend(loc="lower right", fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            f"complex_gatv2_roc_curve_temporal_{temporal_window}.png"
        ), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Comprehensive Analysis Dashboard
        plt.figure(figsize=(16, 12))
        
        # Probability Distribution
        plt.subplot(2, 3, 1)
        plt.hist(probs[targets == 0], bins=30, alpha=0.7, label='Unoccupied', color='blue')
        plt.hist(probs[targets == 1], bins=30, alpha=0.7, label='Occupied', color='red')
        plt.xlabel('Predicted Probability')
        plt.ylabel('Frequency')
        plt.title('Probability Distribution by True Label')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Prediction vs Truth Scatter
        plt.subplot(2, 3, 2)
        plt.scatter(targets, probs, alpha=0.6, s=20)
        plt.plot([0, 1], [0, 1], 'r--', lw=2)
        plt.xlabel('True Labels')
        plt.ylabel('Predicted Probabilities')
        plt.title('Predictions vs Ground Truth')
        plt.grid(True, alpha=0.3)
        
        # Metrics Summary
        plt.subplot(2, 3, 3)
        metrics_names = ['Accuracy', 'Precision', 'Recall', 'F1', 'ROC AUC']
        metrics_values = [
            accuracy_score(targets, preds),
            precision_score(targets, preds, zero_division=0),
            recall_score(targets, preds, zero_division=0),
            f1_score(targets, preds, zero_division=0),
            roc_auc_score(targets, probs) if len(np.unique(targets)) > 1 else 0.5
        ]
        bars = plt.bar(metrics_names, metrics_values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'])
        plt.ylim(0, 1)
        plt.ylabel('Score')
        plt.title('Classification Metrics Summary')
        plt.xticks(rotation=45)
        
        # Add value labels on bars
        for bar, value in zip(bars, metrics_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{value:.3f}', ha='center', va='bottom', fontsize=10)
        
        # Error Analysis
        plt.subplot(2, 3, 4)
        errors = np.abs(targets - probs)
        plt.hist(errors, bins=30, alpha=0.7, color='orange')
        plt.xlabel('Absolute Error')
        plt.ylabel('Frequency')
        plt.title(f'Error Distribution (MAE: {np.mean(errors):.3f})')
        plt.grid(True, alpha=0.3)
        
        # Regression Metrics
        plt.subplot(2, 3, 5)
        reg_metrics = ['R²', 'MSE', 'RMSE', 'MAE']
        reg_values = [
            r2_score(targets, probs),
            mean_squared_error(targets, probs),
            np.sqrt(mean_squared_error(targets, probs)),
            mean_absolute_error(targets, probs)
        ]
        bars = plt.bar(reg_metrics, reg_values, color=['lightblue', 'lightcoral', 'lightgreen', 'gold'])
        plt.ylabel('Score')
        plt.title('Regression Metrics')
        plt.xticks(rotation=45)
        
        # Add value labels on bars
        for bar, value in zip(bars, reg_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        
        # Performance Summary
        plt.subplot(2, 3, 6)
        plt.text(0.1, 0.8, f"Complex GATv2 (Temporal {temporal_window})", fontsize=16, fontweight='bold')
        plt.text(0.1, 0.7, f"Accuracy: {accuracy_score(targets, preds):.3f}", fontsize=12)
        plt.text(0.1, 0.6, f"F1 Score: {f1_score(targets, preds, zero_division=0):.3f}", fontsize=12)
        plt.text(0.1, 0.5, f"ROC AUC: {roc_auc_score(targets, probs) if len(np.unique(targets)) > 1 else 0.5:.3f}", fontsize=12)
        plt.text(0.1, 0.4, f"R² Score: {r2_score(targets, probs):.3f}", fontsize=12)
        plt.text(0.1, 0.3, f"Total Samples: {len(targets):,}", fontsize=12)
        plt.text(0.1, 0.2, f"Parameters: 169,601", fontsize=12)
        plt.xlim(0, 1)
        plt.ylim(0, 1)
        plt.axis('off')
        plt.title('Model Summary')
        
        plt.suptitle(f'Complex GATv2 - Comprehensive Analysis\n(Temporal Window: {temporal_window})', fontsize=18)
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            f"complex_gatv2_comprehensive_analysis_temporal_{temporal_window}.png"
        ), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 4. Sample Point Cloud Visualizations
        num_samples = min(self.config["evaluation"]["visualization"]["num_samples"], len(data))
        for i in range(num_samples):
            sample = data[i]
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
            
            # Ground truth
            colors_true = ["blue" if label == 0 else "red" for label in sample["y_true"]]
            ax1.scatter(sample["pos"][:, 0], sample["pos"][:, 1], c=colors_true, alpha=0.7, s=30)
            ax1.set_xlabel("X Coordinate")
            ax1.set_ylabel("Y Coordinate")
            ax1.set_title(f"Ground Truth\n{'Occupied' if sample['y_true'][0] == 1 else 'Unoccupied'}")
            ax1.grid(True, alpha=0.3)
            
            # Prediction
            pred_color = "red" if sample['y_pred'] else "blue"
            ax2.scatter(sample["pos"][:, 0], sample["pos"][:, 1], c=pred_color, alpha=0.7, s=30)
            ax2.set_xlabel("X Coordinate")
            ax2.set_ylabel("Y Coordinate")
            ax2.set_title(f"Prediction (Prob: {sample['prob'][0]:.3f})\n{'Occupied' if sample['y_pred'] else 'Unoccupied'}")
            ax2.grid(True, alpha=0.3)
            
            # Add legend
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], marker="o", color="w", markerfacecolor="blue", markersize=10, label="Unoccupied"),
                Line2D([0], [0], marker="o", color="w", markerfacecolor="red", markersize=10, label="Occupied"),
            ]
            ax1.legend(handles=legend_elements, loc="upper right")
            ax2.legend(handles=legend_elements, loc="upper right")
            
            plt.suptitle(f"Complex GATv2 - Point Cloud Sample {i+1}\n(Temporal Window: {temporal_window})", fontsize=14)
            plt.tight_layout()
            plt.savefig(os.path.join(
                self.config["evaluation"]["visualization"]["save_dir"],
                f"complex_gatv2_point_cloud_temporal_{temporal_window}_sample_{i+1}.png"
            ), dpi=300, bbox_inches='tight')
            plt.close()


def main():
    parser = argparse.ArgumentParser(description="Evaluate Complex GATv2 with Temporal Window 5")
    parser.add_argument("--config", default="config_complex_temp5.yaml", help="Path to config file")
    parser.add_argument("--checkpoint", default="checkpoints_gatv2_complex_temp5/model_temporal_5_best.pt", help="Path to checkpoint")
    args = parser.parse_args()
    
    # Load configuration
    with open(args.config, "r") as f:
        config = yaml.safe_load(f)
    
    # Create data loaders
    data_loaders = create_data_loaders(config)
    
    # Create evaluator
    evaluator = ComplexGATv2Temp5Evaluator(config)
    
    # Evaluate model
    temporal_window = 5
    print(f"🚀 Evaluating Complex GATv2 for temporal window {temporal_window}")
    
    # Adjust input dimension
    config["model"]["input_dim"] = 10  # Temporal features for window 5
    
    # Create model
    model = create_model(config)
    
    # Load checkpoint
    if os.path.exists(args.checkpoint):
        checkpoint = torch.load(args.checkpoint, map_location='cpu')
        model.load_state_dict(checkpoint["model_state_dict"])
        print(f"✅ Loaded checkpoint from {args.checkpoint}")
        print(f"📊 Model trained for {checkpoint.get('epoch', 'unknown')} epochs")
        print(f"🎯 Best F1 score: {checkpoint.get('best_f1', 'unknown'):.4f}")
        
        # Evaluate model
        metrics = evaluator.evaluate(
            model,
            data_loaders[f"temporal_{temporal_window}"]["test"],
            temporal_window,
        )
        
        print(f"\n✅ Evaluation completed! Results saved to: {config['evaluation']['visualization']['save_dir']}")
        
    else:
        print(f"❌ No checkpoint found at {args.checkpoint}")


if __name__ == "__main__":
    main()
