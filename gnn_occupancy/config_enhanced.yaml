data:
  data_dir: /home/<USER>/ma_yugi/data/07_gnn_ready/
  batch_size: 16  # Reduced for memory efficiency
  num_workers: 4
  temporal_windows: [3]
  binary_mapping:
    unoccupied: [0]
    occupied: [1, 2, 3, 4]
  augmentation:
    rotation_angle: 15
    scaling_range: [0.9, 1.1]

model:
  name: EnhancedOccupancyGNN
  input_dim: 10  # Will be set based on temporal window
  hidden_dim: 192  # Divisible by 8 attention heads (192 = 8 * 24)
  output_dim: 1
  num_layers: 4  # Reduced for memory efficiency
  attention_heads: 8  # Reduced from 12
  dropout: 0.15  # Slightly reduced for better performance
  use_transformer: true
  pool_ratios: [0.8, 0.6]  # Reduced hierarchical levels

  # Enhanced features
  layer_norm: true
  batch_norm: true
  skip_connections: true
  pooling: hierarchical

training:
  device: cpu
  epochs: 150  # Increased for complex model
  learning_rate: 0.0005  # Reduced for stability
  weight_decay: 0.0001
  checkpoint_dir: /home/<USER>/ma_yugi/gnn_occupancy/checkpoints_enhanced_temp3/

  # Enhanced training features
  lr_scheduler:
    type: cosine_annealing
    T_max: 150
    eta_min: 0.00001

  early_stopping:
    patience: 25  # Increased patience for complex model
    min_delta: 0.0005

  # Gradient clipping
  gradient_clipping:
    enabled: true
    max_norm: 1.0

  # Advanced optimization
  optimizer:
    type: adamw
    betas: [0.9, 0.999]
    eps: 0.00000001

  # Warmup
  warmup:
    enabled: true
    warmup_epochs: 10
    warmup_factor: 0.1

evaluation:
  metrics:
    - accuracy
    - precision
    - recall
    - f1
    - roc_auc
  visualization:
    save_dir: /home/<USER>/ma_yugi/gnn_occupancy/visualizations_enhanced_temp3/
    num_samples: 5

ablation:
  feature_importance: true
  gnn_types: [enhanced]
  temporal_windows: [3]
