#!/usr/bin/env python3
"""
Step 3: Advanced Interactive Visualizations
Create interactive visualizations with layer toggles, zoom capabilities, and detailed statistics.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
from datetime import datetime
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.patches import Rectangle
import matplotlib.gridspec as gridspec

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


class ArenaVisualizer:
    """Basic arena visualization creator."""
    
    def __init__(self, results_dir: str = "results/arena_characterization"):
        self.results_dir = Path(results_dir)
        self.viz_dir = self.results_dir / "visualizations"
        self.viz_dir.mkdir(parents=True, exist_ok=True)
        
        # Load analysis results
        self.arena_chars = None
        self.spatial_grid = None
        self.density_grid = None
        self.occupancy_grid = None
        self.all_positions = None
        self.all_labels = None
        
        self._load_analysis_results()
        
    def _load_analysis_results(self):
        """Load results from Step 1 analysis."""
        print("📂 Loading Step 1 analysis results...")
        
        # Load arena characteristics
        chars_file = self.results_dir / 'arena_characteristics.json'
        if chars_file.exists():
            with open(chars_file, 'r') as f:
                self.arena_chars = json.load(f)
            print(f"  ✅ Loaded arena characteristics")
        else:
            raise FileNotFoundError("Arena characteristics not found. Run Step 1 first.")
        
        # Load grids
        grid_files = {
            'spatial_grid': 'spatial_coverage_grid.npy',
            'density_grid': 'density_grid.npy',
            'occupancy_grid': 'occupancy_grid.npy',
            'all_positions': 'all_positions.npy',
            'all_labels': 'all_labels.npy'
        }
        
        for attr_name, filename in grid_files.items():
            file_path = self.results_dir / filename
            if file_path.exists():
                setattr(self, attr_name, np.load(file_path))
                print(f"  ✅ Loaded {filename}")
            else:
                print(f"  ⚠️ Missing {filename}")
    
    def create_arena_overview(self):
        """Create comprehensive arena overview visualization."""
        print("🎨 Creating arena overview visualization...")
        
        # Set up the figure with professional styling
        plt.style.use('default')
        fig = plt.figure(figsize=(20, 12))
        gs = gridspec.GridSpec(2, 3, figure=fig, hspace=0.3, wspace=0.3)
        
        # Color schemes
        coverage_cmap = LinearSegmentedColormap.from_list('coverage', ['white', 'lightblue', 'darkblue'])
        density_cmap = 'viridis'
        occupancy_cmap = LinearSegmentedColormap.from_list('occupancy', ['green', 'yellow', 'red'])
        
        # 1. Spatial Coverage Map
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_spatial_coverage(ax1, coverage_cmap)
        
        # 2. Sample Density Heatmap
        ax2 = fig.add_subplot(gs[0, 1])
        self._plot_density_heatmap(ax2, density_cmap)
        
        # 3. Occupancy Probability Map
        ax3 = fig.add_subplot(gs[0, 2])
        self._plot_occupancy_map(ax3, occupancy_cmap)
        
        # 4. Arena Dimensions and Statistics
        ax4 = fig.add_subplot(gs[1, 0])
        self._plot_arena_statistics(ax4)
        
        # 5. Data Quality Assessment
        ax5 = fig.add_subplot(gs[1, 1])
        self._plot_data_quality(ax5)
        
        # 6. Occupancy Distribution
        ax6 = fig.add_subplot(gs[1, 2])
        self._plot_occupancy_distribution(ax6)
        
        # Add main title
        fig.suptitle('Arena Characterization Overview\nExperimental Environment Analysis', 
                    fontsize=16, fontweight='bold', y=0.95)
        
        # Save the overview
        overview_path = self.viz_dir / 'arena_overview.png'
        plt.savefig(overview_path, dpi=300, bbox_inches='tight', facecolor='white')
        overview_pdf_path = self.viz_dir / 'arena_overview.pdf'
        plt.savefig(overview_pdf_path, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"  ✅ Saved arena overview: {overview_path}")
        
    def _plot_spatial_coverage(self, ax, cmap):
        """Plot spatial coverage map."""
        # Create extent for proper coordinate mapping
        extent = [
            self.arena_chars['spatial_dimensions']['min_x'],
            self.arena_chars['spatial_dimensions']['max_x'],
            self.arena_chars['spatial_dimensions']['min_y'],
            self.arena_chars['spatial_dimensions']['max_y']
        ]
        
        im = ax.imshow(self.spatial_grid, cmap=cmap, extent=extent, 
                      origin='lower', aspect='equal', alpha=0.8)
        
        # Add arena boundary
        width = self.arena_chars['spatial_dimensions']['width']
        height = self.arena_chars['spatial_dimensions']['height']
        min_x = self.arena_chars['spatial_dimensions']['min_x']
        min_y = self.arena_chars['spatial_dimensions']['min_y']
        
        boundary = Rectangle((min_x, min_y), width, height, 
                           linewidth=2, edgecolor='red', facecolor='none')
        ax.add_patch(boundary)
        
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.set_title(f'Spatial Coverage Map\n{self.arena_chars["data_coverage"]["spatial_coverage_percentage"]:.1f}% Coverage')
        ax.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('Data Coverage')
        
    def _plot_density_heatmap(self, ax, cmap):
        """Plot sample density heatmap."""
        extent = [
            self.arena_chars['spatial_dimensions']['min_x'],
            self.arena_chars['spatial_dimensions']['max_x'],
            self.arena_chars['spatial_dimensions']['min_y'],
            self.arena_chars['spatial_dimensions']['max_y']
        ]
        
        # Use log scale for better visualization of density variations
        density_log = np.log1p(self.density_grid)  # log(1 + x) to handle zeros
        
        im = ax.imshow(density_log, cmap=cmap, extent=extent, 
                      origin='lower', aspect='equal')
        
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.set_title(f'Sample Density Heatmap\n{self.arena_chars["data_coverage"]["samples_per_square_meter"]:.1f} samples/m²')
        ax.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('Log(1 + Sample Count)')
        
    def _plot_occupancy_map(self, ax, cmap):
        """Plot occupancy probability map."""
        extent = [
            self.arena_chars['spatial_dimensions']['min_x'],
            self.arena_chars['spatial_dimensions']['max_x'],
            self.arena_chars['spatial_dimensions']['min_y'],
            self.arena_chars['spatial_dimensions']['max_y']
        ]
        
        im = ax.imshow(self.occupancy_grid, cmap=cmap, extent=extent, 
                      origin='lower', aspect='equal', vmin=0, vmax=1)
        
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.set_title(f'Occupancy Probability Map\n{self.arena_chars["occupancy_statistics"]["occupancy_ratio"]*100:.1f}% Overall Occupancy')
        ax.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('Occupancy Probability')
        
    def _plot_arena_statistics(self, ax):
        """Plot arena statistics as a table."""
        # Prepare statistics data
        stats_data = [
            ['Arena Dimensions', ''],
            ['Width', f"{self.arena_chars['spatial_dimensions']['width']:.2f} m"],
            ['Height', f"{self.arena_chars['spatial_dimensions']['height']:.2f} m"],
            ['Total Area', f"{self.arena_chars['spatial_dimensions']['total_area']:.1f} m²"],
            ['', ''],
            ['Grid Properties', ''],
            ['Resolution', f"{self.arena_chars['grid_properties']['resolution']} m/cell"],
            ['Grid Size', f"{self.arena_chars['grid_properties']['grid_width']} × {self.arena_chars['grid_properties']['grid_height']}"],
            ['Total Cells', f"{self.arena_chars['grid_properties']['total_cells']:,}"],
            ['', ''],
            ['Data Coverage', ''],
            ['Total Samples', f"{self.arena_chars['data_coverage']['total_samples']:,}"],
            ['Unique Positions', f"{self.arena_chars['data_coverage']['unique_positions']:,}"],
            ['Coverage', f"{self.arena_chars['data_coverage']['spatial_coverage_percentage']:.1f}%"]
        ]
        
        # Create table
        table = ax.table(cellText=stats_data, cellLoc='left', loc='center',
                        colWidths=[0.6, 0.4])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        
        # Style the table
        for i, row in enumerate(stats_data):
            if row[1] == '':  # Header rows
                table[(i, 0)].set_facecolor('#4472C4')
                table[(i, 0)].set_text_props(weight='bold', color='white')
                table[(i, 1)].set_facecolor('#4472C4')
        
        ax.axis('off')
        ax.set_title('Arena Statistics', fontsize=12, fontweight='bold', pad=20)
        
    def _plot_data_quality(self, ax):
        """Plot data quality metrics."""
        # Create quality metrics visualization
        quality_metrics = [
            'Well-sampled\nRegions',
            'Under-sampled\nRegions',
            'Coverage\nPercentage'
        ]
        
        values = [
            self.arena_chars['data_quality']['well_sampled_regions'],
            self.arena_chars['data_quality']['under_sampled_regions'],
            self.arena_chars['data_coverage']['spatial_coverage_percentage']
        ]
        
        colors = ['green', 'orange', 'blue']
        
        bars = ax.bar(quality_metrics, values, color=colors, alpha=0.7, edgecolor='black')
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                   f'{value:.0f}' if value >= 1 else f'{value:.1f}',
                   ha='center', va='bottom', fontweight='bold')
        
        ax.set_ylabel('Count / Percentage')
        ax.set_title('Data Quality Assessment')
        ax.grid(True, alpha=0.3, axis='y')
        
    def _plot_occupancy_distribution(self, ax):
        """Plot occupancy distribution histogram."""
        # Get occupancy values from non-zero cells
        valid_occupancy = self.occupancy_grid[self.occupancy_grid > 0]
        
        if len(valid_occupancy) > 0:
            ax.hist(valid_occupancy, bins=30, alpha=0.7, color='skyblue', 
                   edgecolor='black', density=True)
            
            # Add statistics lines
            mean_occ = np.mean(valid_occupancy)
            median_occ = np.median(valid_occupancy)
            
            ax.axvline(mean_occ, color='red', linestyle='--', linewidth=2, 
                      label=f'Mean: {mean_occ:.3f}')
            ax.axvline(median_occ, color='blue', linestyle='--', linewidth=2, 
                      label=f'Median: {median_occ:.3f}')
            
            ax.set_xlabel('Occupancy Probability')
            ax.set_ylabel('Density')
            ax.set_title('Occupancy Distribution')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'No occupancy data available', 
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title('Occupancy Distribution')
    
    def create_detailed_arena_map(self):
        """Create detailed arena map with coordinate system."""
        print("🗺️ Creating detailed arena map...")
        
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        
        # Plot sample points with occupancy coloring
        occupied_mask = self.all_labels > 0.5
        free_mask = self.all_labels <= 0.5
        
        # Plot free space points
        ax.scatter(self.all_positions[free_mask, 0], self.all_positions[free_mask, 1], 
                  c='green', s=1, alpha=0.6, label='Free Space')
        
        # Plot occupied points
        ax.scatter(self.all_positions[occupied_mask, 0], self.all_positions[occupied_mask, 1], 
                  c='red', s=1, alpha=0.6, label='Occupied Space')
        
        # Add arena boundary
        width = self.arena_chars['spatial_dimensions']['width']
        height = self.arena_chars['spatial_dimensions']['height']
        min_x = self.arena_chars['spatial_dimensions']['min_x']
        min_y = self.arena_chars['spatial_dimensions']['min_y']
        
        boundary = Rectangle((min_x, min_y), width, height, 
                           linewidth=3, edgecolor='black', facecolor='none',
                           label='Arena Boundary')
        ax.add_patch(boundary)
        
        # Add coordinate grid
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('X Position (m)', fontsize=12)
        ax.set_ylabel('Y Position (m)', fontsize=12)
        ax.set_title(f'Detailed Arena Map\n{len(self.all_positions):,} Sample Points', 
                    fontsize=14, fontweight='bold')
        
        # Set equal aspect ratio
        ax.set_aspect('equal')
        
        # Add legend
        ax.legend(loc='upper right')
        
        # Add scale bar
        scale_length = 2.0  # 2 meters
        scale_x = min_x + width * 0.05
        scale_y = min_y + height * 0.05
        ax.plot([scale_x, scale_x + scale_length], [scale_y, scale_y], 
               'k-', linewidth=3)
        ax.text(scale_x + scale_length/2, scale_y - height*0.02, 
               f'{scale_length}m', ha='center', fontweight='bold')
        
        # Save detailed map
        detailed_path = self.viz_dir / 'detailed_arena_map.png'
        plt.savefig(detailed_path, dpi=300, bbox_inches='tight', facecolor='white')
        detailed_pdf_path = self.viz_dir / 'detailed_arena_map.pdf'
        plt.savefig(detailed_pdf_path, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"  ✅ Saved detailed arena map: {detailed_path}")
    
    def run_step2_visualization(self):
        """Run complete Step 2 visualization creation."""
        print("🚀 Starting Step 2: Basic Arena Visualization Creation")
        print("=" * 70)
        
        try:
            # Create arena overview
            self.create_arena_overview()
            
            # Create detailed arena map
            self.create_detailed_arena_map()
            
            print("\n" + "=" * 70)
            print("🎉 STEP 2 COMPLETED SUCCESSFULLY!")
            print("=" * 70)
            print(f"📁 Visualizations saved to: {self.viz_dir}")
            print("🔄 Ready for Step 3: Advanced Interactive Visualizations")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Step 2 failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function for Step 2."""
    print("🔬 Arena Characterization System - Step 2")
    print("🎨 Basic Visualization Creation")
    print("=" * 80)
    
    visualizer = ArenaVisualizer()
    success = visualizer.run_step2_visualization()
    
    if success:
        print("\n✅ Step 2 completed! Proceed to Step 3 for advanced interactive visualizations.")
    else:
        print("\n❌ Step 2 failed. Check the error messages above.")
    
    return success


if __name__ == "__main__":
    main()
