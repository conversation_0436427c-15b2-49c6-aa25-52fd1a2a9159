#!/usr/bin/env python3
"""
Comprehensive 2D IoU Evaluation System for ECC Models
"""

import os
import sys
import torch
import numpy as np
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data import OccupancyDataset
from model import create_model, OccupancyGNN
from torch_geometric.data import DataLoader
from comprehensive_2d_iou_evaluation import (
    GridConfig, ModelInfo, IoUResults, 
    Comprehensive2DIoUEvaluator
)


class ECCModelEvaluator(Comprehensive2DIoUEvaluator):
    """Extended evaluator specifically for ECC models."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.results_dir = Path("results/ecc_2d_iou_evaluation")
        self.results_dir.mkdir(parents=True, exist_ok=True)
    
    def discover_ecc_models(self) -> List[ModelInfo]:
        """Discover ECC models specifically."""
        print("🔍 Discovering ECC models...")
        
        ecc_dirs = [
            "checkpoints_ecc_temp3",
            "checkpoints_ecc_temp5", 
            "checkpoints_ecc_hybrid_temp3"
        ]
        
        models = []
        
        for ecc_dir in ecc_dirs:
            if not os.path.exists(ecc_dir):
                continue
                
            checkpoint_path = Path(ecc_dir)
            model_files = list(checkpoint_path.glob("model_temporal_*_best.pt"))
            
            if not model_files:
                continue
                
            for model_file in model_files:
                # Extract temporal window
                temporal_window = self._extract_temporal_window(model_file.name)
                if temporal_window is None:
                    continue
                
                # Determine model type
                is_hybrid = 'hybrid' in ecc_dir.lower()
                architecture = 'ecc_hybrid' if is_hybrid else 'ecc'
                
                # Create ECC config
                config = self._create_ecc_config(checkpoint_path, temporal_window)
                
                # Load checkpoint to get info
                try:
                    checkpoint = torch.load(model_file, map_location='cpu')
                    best_f1 = checkpoint.get('best_f1', None)
                    
                    # Calculate parameters from checkpoint
                    num_parameters = sum(p.numel() for p in checkpoint['model_state_dict'].values())
                    
                    # Create model name
                    model_name = f"{architecture}_{config['model']['num_layers']}L_{config['model']['hidden_dim']}H_temp{temporal_window}_{ecc_dir}"
                    
                    model_info = ModelInfo(
                        name=model_name,
                        checkpoint_path=str(model_file),
                        config_path=str(checkpoint_path / "virtual_config.yaml"),  # Virtual
                        temporal_window=temporal_window,
                        architecture=architecture,
                        num_layers=config['model']['num_layers'],
                        hidden_dim=config['model']['hidden_dim'],
                        num_parameters=num_parameters,
                        best_f1=best_f1
                    )
                    
                    models.append(model_info)
                    print(f"  ✅ Found ECC model: {model_name}")
                    
                except Exception as e:
                    print(f"  ❌ Error processing {model_file}: {e}")
                    continue
        
        self.models = models
        print(f"🎯 Discovered {len(models)} ECC models")
        return models
    
    def evaluate_ecc_model(self, model_info: ModelInfo) -> IoUResults:
        """Evaluate a single ECC model."""
        print(f"📊 Evaluating ECC model: {model_info.name}")
        
        # Create ECC config
        config = self._create_ecc_config(Path(model_info.checkpoint_path).parent, model_info.temporal_window)
        
        # Load checkpoint
        checkpoint = torch.load(model_info.checkpoint_path, map_location=self.device)
        
        # Create model
        try:
            model = create_model(config)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"  ✅ Successfully loaded ECC model")
        except Exception as e:
            print(f"  ❌ Failed to load ECC model: {e}")
            # Try with inferred parameters
            try:
                # Infer input dimension from checkpoint
                if 'embedding.weight' in checkpoint['model_state_dict']:
                    embedding_shape = checkpoint['model_state_dict']['embedding.weight'].shape
                    if len(embedding_shape) >= 2:
                        inferred_input_dim = embedding_shape[1]
                        print(f"  📊 Inferred input dimension: {inferred_input_dim}")
                        config['model']['input_dim'] = inferred_input_dim
                
                model = create_model(config)
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"  ✅ Successfully loaded ECC model with inferred parameters")
            except Exception as e2:
                print(f"  ❌ Failed to load ECC model even with inference: {e2}")
                return IoUResults(
                    model_name=model_info.name,
                    sample_ious=[],
                    mean_iou=0.0,
                    median_iou=0.0,
                    std_iou=0.0,
                    min_iou=0.0,
                    max_iou=0.0,
                    grid_resolution=self.grid_config.resolution,
                    total_samples=0
                )
        
        model.to(self.device)
        model.eval()
        
        # Load test data
        test_loader = self.load_test_data(model_info.temporal_window)
        
        sample_ious = []
        spatial_iou_grids = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc=f"Processing {model_info.name}")):
                try:
                    # Move batch to device
                    batch = batch.to(self.device)
                    
                    # Get model predictions
                    predictions = model(batch.x, batch.edge_index, batch.batch)
                    predictions = torch.sigmoid(predictions).cpu().numpy().flatten()
                    
                    # Extract positions and labels
                    try:
                        positions = batch.pos[:, :2].cpu().numpy()  # X, Y coordinates
                    except IndexError:
                        try:
                            positions = batch.x[:, 2:4].cpu().numpy()  # X,Y from features
                        except IndexError:
                            print(f"    ⚠️ Cannot extract X,Y positions from batch")
                            continue
                    
                    labels = batch.y.cpu().numpy().flatten()
                    
                    # Handle different prediction formats
                    if len(predictions) == 1 and len(labels) > 1:
                        # Graph-level prediction: broadcast to all nodes
                        predictions = np.full(len(labels), predictions[0])
                        if batch_idx < 10:  # Only print for first few batches
                            print(f"    📊 Broadcasting graph-level prediction {predictions[0]:.4f} to {len(labels)} nodes")
                    elif len(predictions) != len(labels):
                        print(f"    ⚠️ Prediction-label size mismatch: {len(predictions)} vs {len(labels)}")
                        continue
                    
                    # Convert to 2D grids
                    gt_grid, pred_grid, grid_info = self.points_to_2d_grid(
                        positions, labels, predictions
                    )
                    
                    # Calculate IoU
                    iou = self.calculate_2d_iou(gt_grid, pred_grid)
                    sample_ious.append(iou)
                    
                    # Store spatial information
                    spatial_iou_grids.append({
                        'gt_grid': gt_grid,
                        'pred_grid': pred_grid,
                        'grid_info': grid_info,
                        'iou': iou
                    })
                    
                except Exception as e:
                    print(f"  ⚠️ Error processing batch {batch_idx}: {e}")
                    continue
        
        # Calculate statistics
        sample_ious = np.array(sample_ious)
        
        if len(sample_ious) == 0:
            print(f"  ⚠️ No valid IoU samples collected for {model_info.name}")
            results = IoUResults(
                model_name=model_info.name,
                sample_ious=[],
                mean_iou=0.0,
                median_iou=0.0,
                std_iou=0.0,
                min_iou=0.0,
                max_iou=0.0,
                grid_resolution=self.grid_config.resolution,
                total_samples=0
            )
        else:
            results = IoUResults(
                model_name=model_info.name,
                sample_ious=sample_ious.tolist(),
                mean_iou=float(np.mean(sample_ious)),
                median_iou=float(np.median(sample_ious)),
                std_iou=float(np.std(sample_ious)),
                min_iou=float(np.min(sample_ious)),
                max_iou=float(np.max(sample_ious)),
                grid_resolution=self.grid_config.resolution,
                total_samples=len(sample_ious)
            )
        
        # Store spatial analysis data
        self.spatial_analysis[model_info.name] = spatial_iou_grids
        
        print(f"  ✅ Mean IoU: {results.mean_iou:.4f} ± {results.std_iou:.4f}")
        return results
    
    def run_ecc_evaluation(self):
        """Run complete ECC evaluation."""
        print("🚀 Starting ECC Model 2D IoU Evaluation")
        print("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # Discover ECC models
            self.discover_ecc_models()
            
            if not self.models:
                print("❌ No ECC models found!")
                return False
            
            # Evaluate all ECC models
            for model_info in self.models:
                try:
                    iou_results = self.evaluate_ecc_model(model_info)
                    self.iou_results[model_info.name] = iou_results
                    
                    # Save intermediate results
                    self._save_intermediate_results(model_info.name, iou_results)
                    
                except Exception as e:
                    print(f"❌ Failed to evaluate {model_info.name}: {e}")
                    continue
            
            # Create visualizations
            if self.iou_results:
                self.create_model_comparison_charts()
                self.create_spatial_analysis_heatmaps()
                self.create_individual_model_analysis()
                self.generate_summary_report()
                self.create_comprehensive_pdf_report()
            
            # Print summary
            end_time = datetime.now()
            duration = end_time - start_time
            
            print("\n" + "=" * 60)
            print("🎉 ECC EVALUATION COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print(f"⏱️  Total Duration: {duration}")
            print(f"📊 ECC Models Evaluated: {len(self.iou_results)}")
            print(f"📁 Results Directory: {self.results_dir}")
            
            if self.iou_results:
                best_model = max(self.iou_results.items(), key=lambda x: x[1].mean_iou)
                print(f"🏆 Best ECC Model: {best_model[0]}")
                print(f"🎯 Best IoU: {best_model[1].mean_iou:.4f} ± {best_model[1].std_iou:.4f}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ ECC Evaluation failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function for ECC evaluation."""
    print("🔬 Comprehensive 2D IoU Evaluation System for ECC Models")
    print("=" * 80)
    
    # Configuration
    grid_config = GridConfig(
        resolution=0.1,  # 10cm grid cells
        padding=0.5,     # 50cm padding
        threshold=0.5    # 50% threshold for predictions
    )
    
    # Create ECC evaluator
    evaluator = ECCModelEvaluator(
        gnn_occupancy_dir=".",
        data_dir="../data/07_gnn_ready",
        results_dir="results/ecc_2d_iou_evaluation",
        grid_config=grid_config
    )
    
    # Run ECC evaluation
    success = evaluator.run_ecc_evaluation()
    
    if success:
        print("\n✅ ECC evaluation completed! Check the results directory for detailed analysis.")
    else:
        print("\n❌ ECC evaluation failed. Check the error messages above.")
    
    return success


if __name__ == "__main__":
    main()
