#!/usr/bin/env python3

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import (
    GATv2Conv,
    TransformerConv,
    global_mean_pool,
    global_max_pool,
    global_add_pool,
    BatchNorm,
    TopKPooling,
    SAGPooling
)
from torch_geometric.nn.dense.linear import Linear
from torch_geometric.typing import Adj, OptTensor
from typing import Dict, List, Optional, Tuple, Union
import yaml
import math


class MultiHeadSelfAttention(nn.Module):
    """Multi-head self-attention for graph nodes."""

    def __init__(self, hidden_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads

        assert hidden_dim % num_heads == 0, "hidden_dim must be divisible by num_heads"

        self.q_proj = nn.Linear(hidden_dim, hidden_dim)
        self.k_proj = nn.Linear(hidden_dim, hidden_dim)
        self.v_proj = nn.Linear(hidden_dim, hidden_dim)
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)

        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.head_dim)

    def forward(self, x: torch.Tensor, batch: torch.Tensor) -> torch.Tensor:
        batch_size = batch.max().item() + 1

        # Group nodes by batch
        outputs = []
        for i in range(batch_size):
            mask = batch == i
            if mask.sum() == 0:
                continue

            x_batch = x[mask]  # [num_nodes_in_batch, hidden_dim]

            # Compute Q, K, V
            q = self.q_proj(x_batch).view(-1, self.num_heads, self.head_dim)
            k = self.k_proj(x_batch).view(-1, self.num_heads, self.head_dim)
            v = self.v_proj(x_batch).view(-1, self.num_heads, self.head_dim)

            # Compute attention
            attn_weights = torch.matmul(q, k.transpose(-2, -1)) / self.scale
            attn_weights = F.softmax(attn_weights, dim=-1)
            attn_weights = self.dropout(attn_weights)

            # Apply attention to values
            attn_output = torch.matmul(attn_weights, v)
            attn_output = attn_output.view(-1, self.hidden_dim)
            attn_output = self.out_proj(attn_output)

            outputs.append(attn_output)

        return torch.cat(outputs, dim=0)


class ResidualGNNLayer(nn.Module):
    """GNN layer with residual connections and advanced normalization."""

    def __init__(
        self,
        hidden_dim: int,
        num_heads: int = 8,
        dropout: float = 0.1,
        use_transformer: bool = False
    ):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.use_transformer = use_transformer

        if use_transformer:
            self.gnn = TransformerConv(
                in_channels=hidden_dim,
                out_channels=hidden_dim,
                heads=num_heads,
                dropout=dropout,
                concat=False
            )
        else:
            self.gnn = GATv2Conv(
                in_channels=hidden_dim,
                out_channels=hidden_dim // num_heads,
                heads=num_heads,
                dropout=dropout,
                concat=True
            )

        # Normalization layers
        self.layer_norm1 = nn.LayerNorm(hidden_dim)
        self.layer_norm2 = nn.LayerNorm(hidden_dim)
        self.batch_norm = BatchNorm(hidden_dim)

        # Feed-forward network
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim),
            nn.Dropout(dropout)
        )

        # Self-attention
        self.self_attention = MultiHeadSelfAttention(hidden_dim, num_heads, dropout)

        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, batch: torch.Tensor) -> torch.Tensor:
        # GNN layer with residual connection
        residual = x
        x = self.layer_norm1(x)
        x = self.gnn(x, edge_index)
        x = self.dropout(x) + residual

        # Self-attention with residual connection
        residual = x
        x = self.layer_norm2(x)
        x = self.self_attention(x, batch)
        x = self.dropout(x) + residual

        # Feed-forward network with residual connection
        residual = x
        x = self.ffn(x)
        x = x + residual

        # Final batch normalization
        x = self.batch_norm(x)

        return x


class HierarchicalPooling(nn.Module):
    """Hierarchical pooling with multiple levels."""

    def __init__(self, hidden_dim: int, pool_ratios: List[float] = [0.8, 0.5]):
        super().__init__()
        self.pool_ratios = pool_ratios
        self.pools = nn.ModuleList()

        for ratio in pool_ratios:
            self.pools.append(TopKPooling(hidden_dim, ratio=ratio))

    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, batch: torch.Tensor) -> torch.Tensor:
        # Collect features from different pooling levels
        pooled_features = []

        # Original features
        pooled_features.append(global_mean_pool(x, batch))
        pooled_features.append(global_max_pool(x, batch))

        # Hierarchical pooling
        current_x, current_edge_index, current_batch = x, edge_index, batch

        for pool in self.pools:
            current_x, current_edge_index, _, current_batch, _, _ = pool(
                current_x, current_edge_index, batch=current_batch
            )
            pooled_features.append(global_mean_pool(current_x, current_batch))
            pooled_features.append(global_max_pool(current_x, current_batch))

        return torch.cat(pooled_features, dim=1)


class EnhancedOccupancyGNN(nn.Module):
    """
    Enhanced GNN model for occupancy prediction with advanced features:
    - Residual connections
    - Multi-head self-attention
    - Hierarchical pooling
    - Advanced normalization
    - Transformer-like architecture
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        output_dim: int = 1,
        num_layers: int = 4,
        num_heads: int = 8,
        dropout: float = 0.1,
        use_transformer: bool = True,
        pool_ratios: List[float] = [0.8, 0.5]
    ):
        super().__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.dropout = dropout

        # Input embedding with positional encoding
        self.input_embedding = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )

        # Positional encoding for spatial coordinates
        self.pos_encoding = nn.Sequential(
            nn.Linear(3, hidden_dim),  # x, y, z coordinates -> full hidden_dim
            nn.GELU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # GNN layers with residual connections
        self.gnn_layers = nn.ModuleList([
            ResidualGNNLayer(hidden_dim, num_heads, dropout, use_transformer)
            for _ in range(num_layers)
        ])

        # Hierarchical pooling
        self.hierarchical_pooling = HierarchicalPooling(hidden_dim, pool_ratios)

        # Calculate pooled feature dimension
        pooled_dim = hidden_dim * (2 + 2 * len(pool_ratios))  # mean + max for each level

        # Enhanced classifier with attention
        self.classifier = nn.Sequential(
            nn.Linear(pooled_dim, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim)
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize weights using Xavier/Glorot initialization."""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)

    def forward(
        self,
        x: torch.Tensor,
        edge_index: torch.Tensor,
        batch: torch.Tensor
    ) -> torch.Tensor:
        # Input embedding
        h = self.input_embedding(x)

        # Add positional encoding (assuming x, y, z are in positions 2, 3, 4)
        if x.size(1) >= 5:  # Ensure we have at least x, y, z coordinates
            pos_enc = self.pos_encoding(x[:, 2:5])  # x, y, z coordinates
            h = h + pos_enc

        # Apply GNN layers
        for layer in self.gnn_layers:
            h = layer(h, edge_index, batch)

        # Hierarchical pooling
        h_pooled = self.hierarchical_pooling(h, edge_index, batch)

        # Classification
        out = self.classifier(h_pooled)

        return out


def create_enhanced_model(config: Dict) -> EnhancedOccupancyGNN:
    """
    Create an enhanced model from configuration.

    Args:
        config: Configuration dictionary

    Returns:
        Enhanced model
    """
    model_config = config["model"]

    model = EnhancedOccupancyGNN(
        input_dim=model_config["input_dim"],
        hidden_dim=model_config["hidden_dim"],
        output_dim=model_config["output_dim"],
        num_layers=model_config["num_layers"],
        num_heads=model_config.get("attention_heads", 8),
        dropout=model_config["dropout"],
        use_transformer=model_config.get("use_transformer", True),
        pool_ratios=model_config.get("pool_ratios", [0.8, 0.5])
    )

    return model


if __name__ == "__main__":
    # Test the enhanced model
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)

    model = create_enhanced_model(config)
    print(f"Enhanced model created with {sum(p.numel() for p in model.parameters())} parameters")
    print(model)
