#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
from typing import Dict, List, Tuple, Optional, Union, Any

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    confusion_matrix,
    roc_curve,
    auc,
)
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D

from data import create_data_loaders
from model import create_model


class Evaluator:
    """
    Evaluator class for the occupancy prediction model.
    """

    def __init__(self, config: Dict):
        """
        Initialize the evaluator.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.device = torch.device(config["training"]["device"])

        # Create visualization directory
        os.makedirs(config["evaluation"]["visualization"]["save_dir"], exist_ok=True)

    def evaluate(
        self,
        model: torch.nn.Module,
        test_loader: torch.utils.data.DataLoader,
        temporal_window: int,
    ) -> Dict[str, float]:
        """
        Evaluate the model on the test set.

        Args:
            model: Model to evaluate
            test_loader: Test data loader
            temporal_window: Size of temporal window

        Returns:
            Dictionary of metrics
        """
        # Move model to device
        model = model.to(self.device)

        # Set model to evaluation mode
        model.eval()

        # Initialize metrics
        all_preds = []
        all_targets = []
        all_probs = []
        all_data = []

        # Evaluation loop
        with torch.no_grad():
            for batch in tqdm(test_loader, desc=f"Evaluating temporal_{temporal_window}"):
                # Move batch to device
                batch = batch.to(self.device)

                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)

                # Convert logits to predictions and probabilities
                probs = torch.sigmoid(logits).cpu().numpy()
                preds = probs > 0.5

                # Ensure targets match the logits shape
                if logits.size(0) != batch.y.size(0):
                    # This is a graph-level prediction, so we need one label per graph
                    # Use the majority vote of node labels as the graph label
                    graph_labels = []
                    for i in range(batch.num_graphs):
                        mask = batch.batch == i
                        graph_label = batch.y[mask].float().mean().round()
                        graph_labels.append(graph_label)
                    targets = torch.tensor(graph_labels).cpu().numpy()
                else:
                    targets = batch.y.cpu().numpy()

                all_probs.extend(probs.flatten())
                all_preds.extend(preds.flatten())
                all_targets.extend(targets.flatten())

                # Store data for visualization
                for i in range(batch.num_graphs):
                    mask = batch.batch.cpu() == i
                    data = {
                        "pos": batch.pos[mask].cpu().numpy(),
                        "y_true": batch.y[mask].cpu().numpy(),
                        "y_pred": preds[i],
                        "prob": probs[i],
                    }
                    all_data.append(data)

        # Convert to numpy arrays
        all_targets = np.array(all_targets)
        all_preds = np.array(all_preds)
        all_probs = np.array(all_probs)

        # Compute metrics
        metrics = self._compute_metrics(all_targets, all_preds, all_probs)

        # Print metrics
        print(f"\nEvaluation metrics for temporal_{temporal_window}:")
        for key, value in metrics.items():
            print(f"  {key}: {value:.4f}")

        # Generate visualizations
        self._generate_visualizations(all_targets, all_preds, all_probs, all_data, temporal_window)

        # Perform feature importance analysis
        if self.config["ablation"]["feature_importance"]:
            self._analyze_feature_importance(model, test_loader, temporal_window)

        return metrics

    def generate_visualizations(
        self,
        model: torch.nn.Module,
        test_loader: torch.utils.data.DataLoader,
        temporal_window: int,
    ):
        """
        Generate visualizations without computing metrics.

        Args:
            model: Model to evaluate
            test_loader: Test data loader
            temporal_window: Size of temporal window
        """
        # Move model to device
        model = model.to(self.device)

        # Set model to evaluation mode
        model.eval()

        # Initialize data collection
        all_preds = []
        all_targets = []
        all_probs = []
        all_data = []

        # Evaluation loop
        with torch.no_grad():
            for batch in tqdm(test_loader, desc=f"Visualizing temporal_{temporal_window}"):
                # Move batch to device
                batch = batch.to(self.device)

                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)

                # Convert logits to predictions and probabilities
                probs = torch.sigmoid(logits).cpu().numpy()
                preds = probs > 0.5

                # Ensure targets match the logits shape
                if logits.size(0) != batch.y.size(0):
                    # This is a graph-level prediction, so we need one label per graph
                    # Use the majority vote of node labels as the graph label
                    graph_labels = []
                    for i in range(batch.num_graphs):
                        mask = batch.batch == i
                        graph_label = batch.y[mask].float().mean().round()
                        graph_labels.append(graph_label)
                    targets = torch.tensor(graph_labels).cpu().numpy()
                else:
                    targets = batch.y.cpu().numpy()

                all_probs.extend(probs.flatten())
                all_preds.extend(preds.flatten())
                all_targets.extend(targets.flatten())

                # Store data for visualization
                for i in range(batch.num_graphs):
                    mask = batch.batch.cpu() == i
                    data = {
                        "pos": batch.pos[mask].cpu().numpy(),
                        "y_true": batch.y[mask].cpu().numpy(),
                        "y_pred": preds[i],
                        "prob": probs[i],
                    }
                    all_data.append(data)

                # Only process enough samples for visualization
                if len(all_data) >= self.config["evaluation"]["visualization"]["num_samples"]:
                    break

        # Convert to numpy arrays
        all_targets = np.array(all_targets)
        all_preds = np.array(all_preds)
        all_probs = np.array(all_probs)

        # Generate visualizations
        self._generate_visualizations(all_targets, all_preds, all_probs, all_data, temporal_window)

        # Perform feature importance analysis
        if self.config["ablation"]["feature_importance"]:
            self._analyze_feature_importance(model, test_loader, temporal_window)

    def _compute_metrics(
        self,
        targets: np.ndarray,
        preds: np.ndarray,
        probs: np.ndarray,
    ) -> Dict[str, float]:
        """
        Compute evaluation metrics.

        Args:
            targets: Ground truth labels
            preds: Predicted labels
            probs: Predicted probabilities

        Returns:
            Dictionary of metrics
        """
        metrics = {
            "accuracy": accuracy_score(targets, preds),
            "precision": precision_score(targets, preds, zero_division=0),
            "recall": recall_score(targets, preds, zero_division=0),
            "f1": f1_score(targets, preds, zero_division=0),
            "roc_auc": roc_auc_score(targets, probs) if len(np.unique(targets)) > 1 else 0.5,
        }

        return metrics

    def _generate_visualizations(
        self,
        targets: np.ndarray,
        preds: np.ndarray,
        probs: np.ndarray,
        data: List[Dict],
        temporal_window: int,
    ):
        """
        Generate visualizations.

        Args:
            targets: Ground truth labels
            preds: Predicted labels
            probs: Predicted probabilities
            data: List of data samples
            temporal_window: Size of temporal window
        """
        # Create confusion matrix
        cm = confusion_matrix(targets, preds)
        plt.figure(figsize=(8, 6))
        sns.heatmap(
            cm,
            annot=True,
            fmt="d",
            cmap="Blues",
            xticklabels=["Unoccupied", "Occupied"],
            yticklabels=["Unoccupied", "Occupied"],
        )
        plt.xlabel("Predicted")
        plt.ylabel("True")
        plt.title(f"Confusion Matrix (Temporal Window: {temporal_window})")
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            f"confusion_matrix_temporal_{temporal_window}.png"
        ))
        plt.close()

        # Create ROC curve
        fpr, tpr, _ = roc_curve(targets, probs)
        roc_auc = auc(fpr, tpr)
        plt.figure(figsize=(8, 6))
        plt.plot(
            fpr,
            tpr,
            color="darkorange",
            lw=2,
            label=f"ROC curve (area = {roc_auc:.2f})",
        )
        plt.plot([0, 1], [0, 1], color="navy", lw=2, linestyle="--")
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel("False Positive Rate")
        plt.ylabel("True Positive Rate")
        plt.title(f"ROC Curve (Temporal Window: {temporal_window})")
        plt.legend(loc="lower right")
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            f"roc_curve_temporal_{temporal_window}.png"
        ))
        plt.close()

        # Visualize sample point clouds with predictions
        num_samples = min(self.config["evaluation"]["visualization"]["num_samples"], len(data))
        for i in range(num_samples):
            sample = data[i]

            # Create 2D scatter plot (only X and Y)
            fig = plt.figure(figsize=(12, 8))
            ax = fig.add_subplot(111)

            # Plot points
            colors = ["blue" if label == 0 else "red" for label in sample["y_true"]]
            ax.scatter(
                sample["pos"][:, 0],  # X coordinate
                sample["pos"][:, 1],  # Y coordinate
                c=colors,
                alpha=0.5,
            )

            # Set labels
            ax.set_xlabel("X")
            ax.set_ylabel("Y")
            ax.set_title(f"Point Cloud Visualization (Sample {i+1})\n"
                         f"True: {'Occupied' if sample['y_true'][0] == 1 else 'Unoccupied'}, "
                         f"Predicted: {'Occupied' if sample['y_pred'] else 'Unoccupied'} "
                         f"(Prob: {sample['prob'][0]:.2f})")

            # Add legend
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], marker="o", color="w", markerfacecolor="blue", markersize=10, label="Unoccupied"),
                Line2D([0], [0], marker="o", color="w", markerfacecolor="red", markersize=10, label="Occupied"),
            ]
            ax.legend(handles=legend_elements, loc="upper right")

            # Save figure
            plt.tight_layout()
            plt.savefig(os.path.join(
                self.config["evaluation"]["visualization"]["save_dir"],
                f"point_cloud_temporal_{temporal_window}_sample_{i+1}.png"
            ))
            plt.close()

    def _analyze_feature_importance(
        self,
        model: torch.nn.Module,
        test_loader: torch.utils.data.DataLoader,
        temporal_window: int,
    ):
        """
        Analyze feature importance by feature perturbation.

        Args:
            model: Model to evaluate
            test_loader: Test data loader
            temporal_window: Size of temporal window
        """
        # Set model to evaluation mode
        model.eval()

        # Get a batch of data
        for batch in test_loader:
            batch = batch.to(self.device)
            break

        # Get baseline predictions
        with torch.no_grad():
            baseline_logits = model(batch.x, batch.edge_index, batch.batch)
            baseline_probs = torch.sigmoid(baseline_logits).cpu().numpy()

        # Initialize feature importance scores
        feature_names = [
            "Normalized X", "Normalized Y",
            "Raw X", "Raw Y",
            "Relative X", "Relative Y",
            "Origin X", "Origin Y",
            "Distance to Center"
        ]
        if temporal_window > 1:
            feature_names.append("Temporal Offset")

        importance_scores = []

        # Perturb each feature and measure the impact
        for i in range(batch.x.size(1)):
            # Create a copy of the batch
            perturbed_x = batch.x.clone()

            # Perturb the feature
            perturbed_x[:, i] = torch.randn_like(perturbed_x[:, i])

            # Get predictions with perturbed feature
            with torch.no_grad():
                perturbed_logits = model(perturbed_x, batch.edge_index, batch.batch)
                perturbed_probs = torch.sigmoid(perturbed_logits).cpu().numpy()

            # Compute the mean absolute difference in probabilities
            importance = np.mean(np.abs(perturbed_probs - baseline_probs))
            importance_scores.append(importance)

        # Normalize importance scores
        importance_scores = np.array(importance_scores)
        importance_scores = importance_scores / np.sum(importance_scores)

        # Plot feature importance
        plt.figure(figsize=(12, 6))
        plt.bar(feature_names, importance_scores)
        plt.xlabel("Feature")
        plt.ylabel("Importance")
        plt.title(f"Feature Importance (Temporal Window: {temporal_window})")
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            f"feature_importance_temporal_{temporal_window}.png"
        ))
        plt.close()


if __name__ == "__main__":
    # Load configuration
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)

    # Create data loaders
    data_loaders = create_data_loaders(config)

    # Create evaluator
    evaluator = Evaluator(config)

    # Evaluate models for each temporal window
    for temporal_window in config["data"]["temporal_windows"]:
        print(f"\nEvaluating model for temporal window {temporal_window}")

        # Adjust input dimension based on temporal window
        if temporal_window > 1:
            config["model"]["input_dim"] = 10  # Additional temporal feature (9 spatial features + 1 temporal)
        else:
            config["model"]["input_dim"] = 9   # Only spatial features (no temporal)

        # Create model
        model = create_model(config)

        # Load checkpoint
        checkpoint_path = os.path.join(
            config["training"]["checkpoint_dir"],
            f"model_temporal_{temporal_window}_best.pt"
        )

        if os.path.exists(checkpoint_path):
            checkpoint = torch.load(checkpoint_path)
            model.load_state_dict(checkpoint["model_state_dict"])
            print(f"Loaded checkpoint from {checkpoint_path}")

            # Evaluate model
            metrics = evaluator.evaluate(
                model,
                data_loaders[f"temporal_{temporal_window}"]["test"],
                temporal_window,
            )
        else:
            print(f"No checkpoint found at {checkpoint_path}. Skipping evaluation.")
