#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from typing import Dict, List, Tuple
import pandas as pd
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    confusion_matrix, roc_curve, auc, precision_recall_curve,
    classification_report, mean_squared_error, r2_score
)
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])

from data import create_data_loaders
from model import create_model


class EnhancedVisualizationGenerator:
    """Generate comprehensive visualizations for complex GNN models."""

    def __init__(self, config: Dict, model_name: str, checkpoint_path: str, viz_dir: str):
        self.config = config
        self.model_name = model_name
        self.checkpoint_path = checkpoint_path
        self.viz_dir = viz_dir
        self.device = torch.device(config["training"]["device"])

        # Create visualization directory
        os.makedirs(viz_dir, exist_ok=True)

        # Set style
        plt.style.use('default')
        sns.set_palette("husl")

    def load_model_and_data(self, temporal_window: int):
        """Load model and data for evaluation."""
        # Create data loaders
        data_loaders = create_data_loaders(self.config)
        test_loader = data_loaders[f"temporal_{temporal_window}"]["test"]

        # Create and load model
        model = create_model(self.config)
        checkpoint = torch.load(self.checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint["model_state_dict"])
        model = model.to(self.device)
        model.eval()

        return model, test_loader, checkpoint

    def extract_predictions_and_features(self, model, test_loader):
        """Extract predictions, features, and embeddings from the model."""
        all_preds = []
        all_targets = []
        all_probs = []
        all_features = []
        all_embeddings = []
        all_positions = []

        with torch.no_grad():
            for batch in tqdm(test_loader, desc="Extracting features"):
                batch = batch.to(self.device)

                # Forward pass with feature extraction
                logits = model(batch.x, batch.edge_index, batch.batch)

                # Get embeddings (use input features as embeddings for now)
                embeddings = batch.x

                # Convert logits to predictions and probabilities
                probs = torch.sigmoid(logits).cpu().numpy()
                preds = probs > 0.5

                # Handle graph-level predictions - aggregate to graph level
                graph_preds = []
                graph_targets = []
                graph_probs = []
                graph_features = []
                graph_embeddings = []
                graph_positions = []

                for i in range(batch.num_graphs):
                    mask = batch.batch == i

                    # Get graph-level prediction (mean of node predictions)
                    graph_prob = probs[mask].mean()
                    graph_pred = graph_prob > 0.5

                    # Get graph-level target (mean of node targets)
                    graph_target = batch.y[mask].float().mean().round()

                    # Get representative features (mean of node features)
                    graph_feature = batch.x[mask].mean(dim=0).cpu().numpy()
                    graph_embedding = embeddings[mask].mean(dim=0).cpu().numpy()
                    graph_position = batch.pos[mask].mean(dim=0).cpu().numpy()

                    graph_probs.append(graph_prob)
                    graph_preds.append(graph_pred)
                    graph_targets.append(graph_target)
                    graph_features.append(graph_feature)
                    graph_embeddings.append(graph_embedding)
                    graph_positions.append(graph_position)

                all_probs.extend(graph_probs)
                all_preds.extend(graph_preds)
                all_targets.extend(graph_targets)
                all_features.extend(graph_features)
                all_embeddings.extend(graph_embeddings)
                all_positions.extend(graph_positions)

        return {
            'predictions': np.array(all_preds),
            'targets': np.array(all_targets),
            'probabilities': np.array(all_probs),
            'features': np.array(all_features),
            'embeddings': np.array(all_embeddings),
            'positions': np.array(all_positions)
        }

    def create_performance_dashboard(self, data: Dict, temporal_window: int, checkpoint: Dict):
        """Create comprehensive performance dashboard."""
        fig = plt.figure(figsize=(20, 16))

        # 1. Confusion Matrix (Enhanced)
        ax1 = plt.subplot(3, 4, 1)
        cm = confusion_matrix(data['targets'], data['predictions'])
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]

        sns.heatmap(cm_normalized, annot=True, fmt='.3f', cmap='Blues',
                   xticklabels=['Unoccupied', 'Occupied'],
                   yticklabels=['Unoccupied', 'Occupied'],
                   cbar_kws={'label': 'Normalized Count'})
        plt.title('Normalized Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('True')

        # 2. ROC Curve with Confidence Intervals
        ax2 = plt.subplot(3, 4, 2)
        fpr, tpr, _ = roc_curve(data['targets'], data['probabilities'])
        roc_auc = auc(fpr, tpr)

        plt.plot(fpr, tpr, color='darkorange', lw=3,
                label=f'ROC Curve (AUC = {roc_auc:.3f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', alpha=0.8)
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curve Analysis')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)

        # 3. Precision-Recall Curve
        ax3 = plt.subplot(3, 4, 3)
        precision, recall, _ = precision_recall_curve(data['targets'], data['probabilities'])
        pr_auc = auc(recall, precision)

        plt.plot(recall, precision, color='darkgreen', lw=3,
                label=f'PR Curve (AUC = {pr_auc:.3f})')
        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.title('Precision-Recall Curve')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 4. Probability Distribution by Class
        ax4 = plt.subplot(3, 4, 4)
        plt.hist(data['probabilities'][data['targets'] == 0], bins=30, alpha=0.7,
                label='Unoccupied', color='blue', density=True)
        plt.hist(data['probabilities'][data['targets'] == 1], bins=30, alpha=0.7,
                label='Occupied', color='red', density=True)
        plt.xlabel('Predicted Probability')
        plt.ylabel('Density')
        plt.title('Probability Distribution by Class')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 5. Calibration Plot
        ax5 = plt.subplot(3, 4, 5)
        from sklearn.calibration import calibration_curve
        fraction_of_positives, mean_predicted_value = calibration_curve(
            data['targets'], data['probabilities'], n_bins=10)

        plt.plot(mean_predicted_value, fraction_of_positives, "s-",
                label="Model", color='red')
        plt.plot([0, 1], [0, 1], "k:", label="Perfectly calibrated")
        plt.xlabel('Mean Predicted Probability')
        plt.ylabel('Fraction of Positives')
        plt.title('Calibration Plot')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 6. Feature Importance (if available)
        ax6 = plt.subplot(3, 4, 6)
        feature_names = ['X', 'Y', 'Z', 'Intensity', 'Temp1', 'Temp2', 'Temp3', 'Temp4', 'Temp5', 'Label']
        feature_importance = np.abs(data['features']).mean(axis=0)

        bars = plt.bar(range(len(feature_importance)), feature_importance,
                      color='skyblue', alpha=0.7)
        plt.xlabel('Feature Index')
        plt.ylabel('Mean Absolute Value')
        plt.title('Feature Importance (Mean Abs Value)')
        plt.xticks(range(len(feature_importance)), feature_names, rotation=45)
        plt.grid(True, alpha=0.3)

        # 7. Error Analysis by Confidence
        ax7 = plt.subplot(3, 4, 7)
        confidence = np.abs(data['probabilities'] - 0.5) * 2  # Convert to 0-1 scale
        errors = np.abs(data['targets'] - data['predictions'])

        # Bin by confidence
        conf_bins = np.linspace(0, 1, 11)
        bin_centers = (conf_bins[:-1] + conf_bins[1:]) / 2
        error_rates = []

        for i in range(len(conf_bins) - 1):
            mask = (confidence >= conf_bins[i]) & (confidence < conf_bins[i+1])
            if mask.sum() > 0:
                error_rates.append(errors[mask].mean())
            else:
                error_rates.append(0)

        plt.plot(bin_centers, error_rates, 'o-', color='red', linewidth=2)
        plt.xlabel('Prediction Confidence')
        plt.ylabel('Error Rate')
        plt.title('Error Rate vs Confidence')
        plt.grid(True, alpha=0.3)

        # 8. Spatial Error Distribution
        ax8 = plt.subplot(3, 4, 8)
        positions = data['positions']
        errors = np.abs(data['targets'] - data['predictions'])

        scatter = plt.scatter(positions[:, 0], positions[:, 1], c=errors,
                            cmap='Reds', alpha=0.6, s=20)
        plt.colorbar(scatter, label='Prediction Error')
        plt.xlabel('X Position')
        plt.ylabel('Y Position')
        plt.title('Spatial Distribution of Errors')
        plt.grid(True, alpha=0.3)

        # 9. Training History (if available)
        ax9 = plt.subplot(3, 4, 9)
        if 'training_history' in checkpoint:
            history = checkpoint['training_history']
            epochs = range(1, len(history['train_loss']) + 1)
            plt.plot(epochs, history['train_loss'], label='Train Loss', color='blue')
            plt.plot(epochs, history['val_loss'], label='Val Loss', color='red')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.title('Training History')
            plt.legend()
            plt.grid(True, alpha=0.3)
        else:
            plt.text(0.5, 0.5, 'Training history\nnot available',
                    ha='center', va='center', transform=ax9.transAxes)
            plt.title('Training History')

        # 10. Class Balance Analysis
        ax10 = plt.subplot(3, 4, 10)
        unique, counts = np.unique(data['targets'], return_counts=True)
        colors = ['blue', 'red']
        plt.pie(counts, labels=['Unoccupied', 'Occupied'], colors=colors,
               autopct='%1.1f%%', startangle=90)
        plt.title('Class Distribution in Test Set')

        # 11. Metrics Summary
        ax11 = plt.subplot(3, 4, 11)
        metrics = {
            'Accuracy': accuracy_score(data['targets'], data['predictions']),
            'Precision': precision_score(data['targets'], data['predictions'], zero_division=0),
            'Recall': recall_score(data['targets'], data['predictions'], zero_division=0),
            'F1 Score': f1_score(data['targets'], data['predictions'], zero_division=0),
            'ROC AUC': roc_auc_score(data['targets'], data['probabilities'])
        }

        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())
        colors = ['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum']

        bars = plt.bar(metric_names, metric_values, color=colors)
        plt.ylim(0, 1)
        plt.ylabel('Score')
        plt.title('Performance Metrics Summary')
        plt.xticks(rotation=45)

        # Add value labels on bars
        for bar, value in zip(bars, metric_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=10)

        # 12. Model Information
        ax12 = plt.subplot(3, 4, 12)
        info_text = f"""
{self.model_name}
Temporal Window: {temporal_window}
Epoch: {checkpoint.get('epoch', 'N/A')}
Best F1: {checkpoint.get('best_f1', 'N/A'):.4f}
Parameters: {sum(p.numel() for p in create_model(self.config).parameters()):,}
Test Accuracy: {metrics['Accuracy']:.3f}
Test F1: {metrics['F1 Score']:.3f}
        """
        plt.text(0.1, 0.9, info_text, transform=ax12.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')
        plt.axis('off')
        plt.title('Model Information')

        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, f'enhanced_dashboard_temporal_{temporal_window}.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def create_embedding_analysis(self, data: Dict, temporal_window: int):
        """Create embedding and feature analysis visualizations."""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 1. t-SNE Embedding
        print("Computing t-SNE embedding...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=30)
        embeddings_2d = tsne.fit_transform(data['embeddings'][:1000])  # Sample for speed

        scatter = axes[0, 0].scatter(embeddings_2d[:, 0], embeddings_2d[:, 1],
                                   c=data['targets'][:1000], cmap='RdYlBu', alpha=0.7)
        axes[0, 0].set_title('t-SNE Embedding (by True Label)')
        axes[0, 0].set_xlabel('t-SNE 1')
        axes[0, 0].set_ylabel('t-SNE 2')
        plt.colorbar(scatter, ax=axes[0, 0])

        # 2. PCA Analysis
        pca = PCA(n_components=2)
        pca_embeddings = pca.fit_transform(data['embeddings'])

        scatter = axes[0, 1].scatter(pca_embeddings[:, 0], pca_embeddings[:, 1],
                                   c=data['targets'], cmap='RdYlBu', alpha=0.7)
        axes[0, 1].set_title(f'PCA Embedding (Var: {pca.explained_variance_ratio_.sum():.3f})')
        axes[0, 1].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.3f})')
        axes[0, 1].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.3f})')
        plt.colorbar(scatter, ax=axes[0, 1])

        # 3. Feature Correlation Matrix
        feature_names = ['X', 'Y', 'Z', 'Intensity', 'Temp1', 'Temp2', 'Temp3', 'Temp4', 'Temp5', 'Label']
        corr_matrix = np.corrcoef(data['features'].T)

        im = axes[0, 2].imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
        axes[0, 2].set_title('Feature Correlation Matrix')
        axes[0, 2].set_xticks(range(len(feature_names)))
        axes[0, 2].set_yticks(range(len(feature_names)))
        axes[0, 2].set_xticklabels(feature_names, rotation=45)
        axes[0, 2].set_yticklabels(feature_names)
        plt.colorbar(im, ax=axes[0, 2])

        # 4. Prediction Confidence by Position
        positions = data['positions']
        confidence = np.abs(data['probabilities'] - 0.5) * 2

        scatter = axes[1, 0].scatter(positions[:, 0], positions[:, 1],
                                   c=confidence, cmap='viridis', alpha=0.6, s=20)
        axes[1, 0].set_title('Prediction Confidence by Position')
        axes[1, 0].set_xlabel('X Position')
        axes[1, 0].set_ylabel('Y Position')
        plt.colorbar(scatter, ax=axes[1, 0], label='Confidence')

        # 5. Feature Distribution by Class
        feature_idx = 3  # Intensity feature
        axes[1, 1].hist(data['features'][data['targets'] == 0, feature_idx],
                       bins=30, alpha=0.7, label='Unoccupied', color='blue', density=True)
        axes[1, 1].hist(data['features'][data['targets'] == 1, feature_idx],
                       bins=30, alpha=0.7, label='Occupied', color='red', density=True)
        axes[1, 1].set_title(f'Feature Distribution: {feature_names[feature_idx]}')
        axes[1, 1].set_xlabel('Feature Value')
        axes[1, 1].set_ylabel('Density')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # 6. Embedding Clusters
        from sklearn.cluster import KMeans
        kmeans = KMeans(n_clusters=4, random_state=42)
        clusters = kmeans.fit_predict(data['embeddings'])

        scatter = axes[1, 2].scatter(pca_embeddings[:, 0], pca_embeddings[:, 1],
                                   c=clusters, cmap='tab10', alpha=0.7)
        axes[1, 2].set_title('K-Means Clustering (k=4)')
        axes[1, 2].set_xlabel('PC1')
        axes[1, 2].set_ylabel('PC2')
        plt.colorbar(scatter, ax=axes[1, 2], label='Cluster')

        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, f'embedding_analysis_temporal_{temporal_window}.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    def generate_all_visualizations(self, temporal_window: int):
        """Generate all enhanced visualizations for the model."""
        print(f"Generating enhanced visualizations for {self.model_name} (temporal {temporal_window})...")

        # Load model and data
        model, test_loader, checkpoint = self.load_model_and_data(temporal_window)

        # Extract predictions and features
        data = self.extract_predictions_and_features(model, test_loader)

        # Generate visualizations
        self.create_performance_dashboard(data, temporal_window, checkpoint)
        self.create_embedding_analysis(data, temporal_window)

        print(f"✅ Enhanced visualizations saved to: {self.viz_dir}")


def main():
    """Generate enhanced visualizations for complex models."""

    # Model configurations
    models_to_visualize = [
        {
            'name': 'Complex GATv2 (Temp 3)',
            'config_path': 'config.yaml',
            'checkpoint_path': 'checkpoints_gatv2_complex_4layers_temp3/model_temporal_3_best.pt',
            'viz_dir': 'enhanced_visualizations_complex_temp3',
            'temporal_window': 3
        },
        {
            'name': 'Complex GATv2 (Temp 5)',
            'config_path': 'config_complex_temp5.yaml',
            'checkpoint_path': 'checkpoints_gatv2_complex_temp5/model_temporal_5_best.pt',
            'viz_dir': 'enhanced_visualizations_complex_temp5',
            'temporal_window': 5
        }
    ]

    for model_config in models_to_visualize:
        try:
            # Load configuration
            with open(model_config['config_path'], 'r') as f:
                config = yaml.safe_load(f)

            # Adjust input dimension
            config['model']['input_dim'] = 10

            # Create visualizer
            visualizer = EnhancedVisualizationGenerator(
                config=config,
                model_name=model_config['name'],
                checkpoint_path=model_config['checkpoint_path'],
                viz_dir=model_config['viz_dir']
            )

            # Generate visualizations
            visualizer.generate_all_visualizations(model_config['temporal_window'])

        except Exception as e:
            print(f"❌ Error generating visualizations for {model_config['name']}: {e}")
            continue

    print("🎉 All enhanced visualizations completed!")


if __name__ == "__main__":
    main()
