#!/usr/bin/env python3

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
import os
from PIL import Image
import yaml

# Set style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

def create_complex_model_deep_dive():
    """Create detailed analysis for complex models."""
    
    # Complex models data
    complex_models = {
        'Model': ['Complex GATv2 (Temp 3)', 'Complex GATv2 (Temp 5)', 'Enhanced GATv2 (Temp 3)'],
        'Accuracy': [72.84, 70.03, 67.25],
        'Precision': [71.05, 66.93, 60.14],
        'Recall': [68.17, 69.08, 83.46],
        'F1_Score': [69.58, 67.99, 69.90],
        'ROC_AUC': [79.93, 77.61, 71.85],
        'Parameters': [169601, 169601, 6045313],
        'Training_Time': [4, 2.5, 6],
        'Epochs': [86, 59, 11],
        'Layers': [4, 4, 4],
        'Hidden_Dim': [128, 128, 192],
        'Attention_Heads': [8, 8, 8],
        'Temporal_Window': [3, 5, 3],
        'R2_Score': [0.2425, 0.2095, 0.1465],
        'MSE': [0.1879, 0.1964, 0.2117],
        'RMSE': [0.4335, 0.4432, 0.4601],
        'MAE': [0.3954, 0.4132, 0.4303]
    }
    
    df = pd.DataFrame(complex_models)
    
    # Create comprehensive analysis figure
    fig = plt.figure(figsize=(20, 16))
    
    # 1. Performance Comparison Radar Chart
    ax1 = plt.subplot(3, 4, 1, projection='polar')
    
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1_Score', 'ROC_AUC']
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]
    
    colors = ['red', 'blue', 'green']
    for i, (idx, model) in enumerate(df.iterrows()):
        values = [model[metric]/100 for metric in metrics]
        values += values[:1]
        
        ax1.plot(angles, values, 'o-', linewidth=3, label=model['Model'], color=colors[i])
        ax1.fill(angles, values, alpha=0.1, color=colors[i])
    
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(metrics)
    ax1.set_ylim(0, 1)
    ax1.set_title('Complex Models Performance Radar', size=12, weight='bold')
    ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    # 2. Architecture Comparison
    ax2 = plt.subplot(3, 4, 2)
    x = np.arange(len(df))
    width = 0.25
    
    bars1 = ax2.bar(x - width, df['Layers'], width, label='Layers', alpha=0.8)
    bars2 = ax2.bar(x, df['Hidden_Dim']/20, width, label='Hidden Dim (/20)', alpha=0.8)
    bars3 = ax2.bar(x + width, df['Attention_Heads'], width, label='Attention Heads', alpha=0.8)
    
    ax2.set_xlabel('Model')
    ax2.set_ylabel('Value')
    ax2.set_title('Architecture Comparison')
    ax2.set_xticks(x)
    ax2.set_xticklabels([m.split('(')[0].strip() for m in df['Model']], rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. Parameter Efficiency vs Performance
    ax3 = plt.subplot(3, 4, 3)
    param_efficiency = df['F1_Score'] / (df['Parameters'] / 1000)
    
    bars = ax3.bar(range(len(df)), param_efficiency, 
                  color=['darkred', 'darkblue', 'darkgreen'], alpha=0.7)
    ax3.set_xlabel('Model')
    ax3.set_ylabel('F1 Score per 1K Parameters')
    ax3.set_title('Parameter Efficiency Analysis')
    ax3.set_xticks(range(len(df)))
    ax3.set_xticklabels([m.split('(')[0].strip() for m in df['Model']], rotation=45)
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                f'{height:.3f}', ha='center', va='bottom', fontsize=10)
    
    # 4. Training Dynamics
    ax4 = plt.subplot(3, 4, 4)
    ax4.scatter(df['Epochs'], df['Accuracy'], s=df['Training_Time']*50, 
               c=['red', 'blue', 'green'], alpha=0.7)
    
    for i, model in enumerate(df['Model']):
        ax4.annotate(model.split('(')[0].strip(), 
                    (df['Epochs'].iloc[i], df['Accuracy'].iloc[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    ax4.set_xlabel('Epochs to Convergence')
    ax4.set_ylabel('Final Accuracy (%)')
    ax4.set_title('Training Convergence\n(Bubble size = Training Time)')
    ax4.grid(True, alpha=0.3)
    
    # 5. Regression Performance Comparison
    ax5 = plt.subplot(3, 4, 5)
    regression_metrics = ['R2_Score', 'MSE', 'RMSE', 'MAE']
    
    x = np.arange(len(regression_metrics))
    width = 0.25
    
    for i, (idx, model) in enumerate(df.iterrows()):
        values = [model[metric] for metric in regression_metrics]
        # Normalize MSE, RMSE, MAE (lower is better) by inverting
        values[1] = 1 - values[1]  # MSE
        values[2] = 1 - values[2]  # RMSE  
        values[3] = 1 - values[3]  # MAE
        
        bars = ax5.bar(x + i*width, values, width, 
                      label=model['Model'], alpha=0.8, color=colors[i])
    
    ax5.set_xlabel('Regression Metrics')
    ax5.set_ylabel('Normalized Score (Higher = Better)')
    ax5.set_title('Regression Performance Comparison')
    ax5.set_xticks(x + width)
    ax5.set_xticklabels(['R²', '1-MSE', '1-RMSE', '1-MAE'])
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. Temporal Window Impact
    ax6 = plt.subplot(3, 4, 6)
    temp3_models = df[df['Temporal_Window'] == 3]
    temp5_models = df[df['Temporal_Window'] == 5]
    
    metrics_comparison = ['Accuracy', 'F1_Score', 'ROC_AUC']
    temp3_avg = [temp3_models[metric].mean() for metric in metrics_comparison]
    temp5_avg = [temp5_models[metric].mean() for metric in metrics_comparison]
    
    x = np.arange(len(metrics_comparison))
    width = 0.35
    
    bars1 = ax6.bar(x - width/2, temp3_avg, width, label='Temporal 3', alpha=0.8)
    bars2 = ax6.bar(x + width/2, temp5_avg, width, label='Temporal 5', alpha=0.8)
    
    ax6.set_xlabel('Metrics')
    ax6.set_ylabel('Average Score (%)')
    ax6.set_title('Temporal Window Impact')
    ax6.set_xticks(x)
    ax6.set_xticklabels(metrics_comparison)
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # 7. Training Efficiency Analysis
    ax7 = plt.subplot(3, 4, 7)
    efficiency = df['Accuracy'] / df['Training_Time']
    
    bars = ax7.bar(range(len(df)), efficiency, 
                  color=['darkred', 'darkblue', 'darkgreen'], alpha=0.7)
    ax7.set_xlabel('Model')
    ax7.set_ylabel('Accuracy per Hour')
    ax7.set_title('Training Time Efficiency')
    ax7.set_xticks(range(len(df)))
    ax7.set_xticklabels([m.split('(')[0].strip() for m in df['Model']], rotation=45)
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax7.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}', ha='center', va='bottom', fontsize=10)
    
    # 8. Precision vs Recall Trade-off
    ax8 = plt.subplot(3, 4, 8)
    ax8.scatter(df['Recall'], df['Precision'], s=200, 
               c=['red', 'blue', 'green'], alpha=0.7)
    
    for i, model in enumerate(df['Model']):
        ax8.annotate(model.split('(')[0].strip(), 
                    (df['Recall'].iloc[i], df['Precision'].iloc[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    ax8.set_xlabel('Recall (%)')
    ax8.set_ylabel('Precision (%)')
    ax8.set_title('Precision vs Recall Trade-off')
    ax8.grid(True, alpha=0.3)
    
    # Add diagonal line for F1 contours
    x_line = np.linspace(50, 90, 100)
    for f1 in [0.6, 0.65, 0.7, 0.75]:
        y_line = (f1 * x_line) / (2 * f1 - x_line)
        ax8.plot(x_line, y_line, '--', alpha=0.3, color='gray')
        ax8.text(85, y_line[-1], f'F1={f1}', fontsize=8, alpha=0.7)
    
    # 9. Model Complexity vs Performance
    ax9 = plt.subplot(3, 4, 9)
    ax9.scatter(df['Parameters'], df['F1_Score'], s=200, 
               c=['red', 'blue', 'green'], alpha=0.7)
    
    for i, model in enumerate(df['Model']):
        ax9.annotate(model.split('(')[0].strip(), 
                    (df['Parameters'].iloc[i], df['F1_Score'].iloc[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    ax9.set_xscale('log')
    ax9.set_xlabel('Parameters (log scale)')
    ax9.set_ylabel('F1 Score (%)')
    ax9.set_title('Complexity vs Performance')
    ax9.grid(True, alpha=0.3)
    
    # 10. ROC AUC Comparison
    ax10 = plt.subplot(3, 4, 10)
    bars = ax10.bar(range(len(df)), df['ROC_AUC'], 
                   color=['darkred', 'darkblue', 'darkgreen'], alpha=0.7)
    ax10.set_xlabel('Model')
    ax10.set_ylabel('ROC AUC (%)')
    ax10.set_title('Discrimination Capability')
    ax10.set_xticks(range(len(df)))
    ax10.set_xticklabels([m.split('(')[0].strip() for m in df['Model']], rotation=45)
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax10.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                 f'{height:.1f}%', ha='center', va='bottom', fontsize=10)
    
    # 11. Training Convergence Speed
    ax11 = plt.subplot(3, 4, 11)
    convergence_speed = 100 / df['Epochs']  # Convergence speed (higher = faster)
    
    bars = ax11.bar(range(len(df)), convergence_speed, 
                   color=['darkred', 'darkblue', 'darkgreen'], alpha=0.7)
    ax11.set_xlabel('Model')
    ax11.set_ylabel('Convergence Speed (100/Epochs)')
    ax11.set_title('Training Convergence Speed')
    ax11.set_xticks(range(len(df)))
    ax11.set_xticklabels([m.split('(')[0].strip() for m in df['Model']], rotation=45)
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax11.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                 f'{height:.2f}', ha='center', va='bottom', fontsize=10)
    
    # 12. Model Summary and Recommendations
    ax12 = plt.subplot(3, 4, 12)
    
    # Find best model for each metric
    best_accuracy = df.loc[df['Accuracy'].idxmax(), 'Model']
    best_f1 = df.loc[df['F1_Score'].idxmax(), 'Model']
    best_roc = df.loc[df['ROC_AUC'].idxmax(), 'Model']
    best_efficiency = df.loc[(df['F1_Score'] / (df['Parameters'] / 1000)).idxmax(), 'Model']
    fastest_training = df.loc[df['Training_Time'].idxmin(), 'Model']
    
    summary_text = f"""
COMPLEX MODELS ANALYSIS SUMMARY

🏆 PERFORMANCE LEADERS:
• Best Accuracy: {best_accuracy.split('(')[0].strip()}
  ({df['Accuracy'].max():.1f}%)
• Best F1 Score: {best_f1.split('(')[0].strip()}
  ({df['F1_Score'].max():.1f}%)
• Best ROC AUC: {best_roc.split('(')[0].strip()}
  ({df['ROC_AUC'].max():.1f}%)

⚡ EFFICIENCY ANALYSIS:
• Most Parameter Efficient: 
  {best_efficiency.split('(')[0].strip()}
• Fastest Training: {fastest_training.split('(')[0].strip()}
  ({df['Training_Time'].min():.1f} hours)

🔍 KEY INSIGHTS:
• Complex GATv2 (Temp 3) leads in accuracy & ROC AUC
• Enhanced GATv2 has highest recall (83.46%)
• Temporal window 5 trains 31% faster than 3
• Enhanced model has 35x more parameters
• All models show strong discrimination (ROC > 70%)

📊 REGRESSION PERFORMANCE:
• Best R²: {df['R2_Score'].max():.3f}
• Lowest RMSE: {df['RMSE'].min():.3f}
• Models explain 14-24% of variance
    """
    
    ax12.text(0.05, 0.95, summary_text, transform=ax12.transAxes, fontsize=8,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
    ax12.axis('off')
    
    plt.suptitle('Complex GNN Models - Deep Dive Analysis', fontsize=16, weight='bold', y=0.98)
    plt.tight_layout()
    plt.savefig('complex_models_deep_dive_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Complex models deep dive analysis created!")

def create_architecture_comparison():
    """Create detailed architecture comparison visualization."""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Architecture specifications
    models = {
        'Complex GATv2 (Temp 3)': {
            'layers': 4, 'hidden': 128, 'heads': 8, 'params': 169601,
            'features': ['Layer Norm', 'Batch Norm', 'Skip Connections', 'Dropout 0.3']
        },
        'Complex GATv2 (Temp 5)': {
            'layers': 4, 'hidden': 128, 'heads': 8, 'params': 169601,
            'features': ['Layer Norm', 'Batch Norm', 'Skip Connections', 'Dropout 0.3']
        },
        'Enhanced GATv2': {
            'layers': 4, 'hidden': 192, 'heads': 8, 'params': 6045313,
            'features': ['Residual Connections', 'Self-Attention', 'Hierarchical Pooling', 'Transformer Components']
        }
    }
    
    # 1. Architecture Components Comparison
    ax1 = axes[0, 0]
    components = ['Layers', 'Hidden Dim', 'Attention Heads', 'Parameters (K)']
    
    x = np.arange(len(components))
    width = 0.25
    
    for i, (model_name, specs) in enumerate(models.items()):
        values = [specs['layers'], specs['hidden']/10, specs['heads'], specs['params']/1000]
        bars = ax1.bar(x + i*width, values, width, label=model_name.split('(')[0].strip(), alpha=0.8)
    
    ax1.set_xlabel('Architecture Components')
    ax1.set_ylabel('Value (Normalized)')
    ax1.set_title('Architecture Components Comparison')
    ax1.set_xticks(x + width)
    ax1.set_xticklabels(components)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Feature Complexity Heatmap
    ax2 = axes[0, 1]
    
    all_features = ['Layer Norm', 'Batch Norm', 'Skip Connections', 'Dropout 0.3',
                   'Residual Connections', 'Self-Attention', 'Hierarchical Pooling', 'Transformer Components']
    
    feature_matrix = []
    model_names = []
    
    for model_name, specs in models.items():
        model_names.append(model_name.split('(')[0].strip())
        row = [1 if feature in specs['features'] else 0 for feature in all_features]
        feature_matrix.append(row)
    
    im = ax2.imshow(feature_matrix, cmap='RdYlBu', aspect='auto')
    ax2.set_xticks(range(len(all_features)))
    ax2.set_xticklabels(all_features, rotation=45, ha='right')
    ax2.set_yticks(range(len(model_names)))
    ax2.set_yticklabels(model_names)
    ax2.set_title('Architecture Features Matrix')
    
    # Add text annotations
    for i in range(len(model_names)):
        for j in range(len(all_features)):
            text = ax2.text(j, i, '✓' if feature_matrix[i][j] else '✗',
                           ha="center", va="center", color="black", fontsize=12, weight='bold')
    
    # 3. Performance vs Complexity Scatter
    ax3 = axes[1, 0]
    
    performance_data = {
        'Complex GATv2 (Temp 3)': {'f1': 69.58, 'params': 169601, 'time': 4},
        'Complex GATv2 (Temp 5)': {'f1': 67.99, 'params': 169601, 'time': 2.5},
        'Enhanced GATv2': {'f1': 69.90, 'params': 6045313, 'time': 6}
    }
    
    for i, (model_name, data) in enumerate(performance_data.items()):
        ax3.scatter(data['params'], data['f1'], s=data['time']*50, 
                   alpha=0.7, label=model_name.split('(')[0].strip())
        ax3.annotate(model_name.split('(')[0].strip(), 
                    (data['params'], data['f1']),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    ax3.set_xscale('log')
    ax3.set_xlabel('Parameters (log scale)')
    ax3.set_ylabel('F1 Score (%)')
    ax3.set_title('Performance vs Complexity\n(Bubble size = Training Time)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. Training Characteristics
    ax4 = axes[1, 1]
    
    training_data = {
        'Model': ['Complex GATv2\n(Temp 3)', 'Complex GATv2\n(Temp 5)', 'Enhanced GATv2'],
        'Epochs': [86, 59, 11],
        'Time_Hours': [4, 2.5, 6],
        'Final_Accuracy': [72.84, 70.03, 67.25]
    }
    
    # Create bubble chart
    for i, model in enumerate(training_data['Model']):
        ax4.scatter(training_data['Epochs'][i], training_data['Time_Hours'][i], 
                   s=training_data['Final_Accuracy'][i]*5, alpha=0.7,
                   label=model)
        ax4.annotate(f"{training_data['Final_Accuracy'][i]:.1f}%", 
                    (training_data['Epochs'][i], training_data['Time_Hours'][i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    ax4.set_xlabel('Epochs to Convergence')
    ax4.set_ylabel('Training Time (Hours)')
    ax4.set_title('Training Characteristics\n(Bubble size = Final Accuracy)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.suptitle('Complex Models Architecture & Training Analysis', fontsize=14, weight='bold')
    plt.tight_layout()
    plt.savefig('complex_models_architecture_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Architecture comparison analysis created!")

def main():
    """Generate all complex model visualizations."""
    print("🔬 Creating detailed complex model analysis...")
    
    # Create deep dive analysis
    create_complex_model_deep_dive()
    
    # Create architecture comparison
    create_architecture_comparison()
    
    print("🎉 All complex model analyses completed!")

if __name__ == "__main__":
    main()
