#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
from typing import Dict, List, Tuple, Optional, Union, Any

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])

from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    confusion_matrix,
    roc_curve,
    auc,
    mean_squared_error,
    mean_absolute_error,
    r2_score,
    explained_variance_score,
    max_error,
    median_absolute_error
)
import seaborn as sns

from data import create_data_loaders
from model import create_model


class ComprehensiveEvaluator:
    """
    Comprehensive evaluator class for the occupancy prediction model.
    Includes both classification and regression metrics.
    """

    def __init__(self, config: Dict):
        """
        Initialize the evaluator.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.device = torch.device(config["training"]["device"])

        # Create visualization directory
        os.makedirs(config["evaluation"]["visualization"]["save_dir"], exist_ok=True)

    def evaluate(
        self,
        model: torch.nn.Module,
        test_loader: torch.utils.data.DataLoader,
        temporal_window: int,
    ) -> Dict[str, float]:
        """
        Comprehensive evaluation of the model on the test set.

        Args:
            model: Model to evaluate
            test_loader: Test data loader
            temporal_window: Size of temporal window

        Returns:
            Dictionary of comprehensive metrics
        """
        # Move model to device
        model = model.to(self.device)

        # Set model to evaluation mode
        model.eval()

        # Initialize metrics
        all_preds = []
        all_targets = []
        all_probs = []
        all_logits = []

        # Evaluation loop
        with torch.no_grad():
            for batch in tqdm(test_loader, desc=f"Evaluating temporal_{temporal_window}"):
                # Move batch to device
                batch = batch.to(self.device)

                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)

                # Convert logits to predictions and probabilities
                probs = torch.sigmoid(logits).cpu().numpy()
                preds = probs > 0.5

                # Ensure targets match the logits shape
                if logits.size(0) != batch.y.size(0):
                    # This is a graph-level prediction, so we need one label per graph
                    # Use the majority vote of node labels as the graph label
                    graph_labels = []
                    for i in range(batch.num_graphs):
                        mask = batch.batch == i
                        graph_label = batch.y[mask].float().mean().round()
                        graph_labels.append(graph_label)
                    targets = torch.tensor(graph_labels).cpu().numpy()
                else:
                    targets = batch.y.cpu().numpy()

                all_probs.extend(probs.flatten())
                all_preds.extend(preds.flatten())
                all_targets.extend(targets.flatten())
                all_logits.extend(logits.cpu().numpy().flatten())

        # Convert to numpy arrays
        all_targets = np.array(all_targets)
        all_preds = np.array(all_preds)
        all_probs = np.array(all_probs)
        all_logits = np.array(all_logits)

        # Compute comprehensive metrics
        metrics = self._compute_comprehensive_metrics(all_targets, all_preds, all_probs, all_logits)

        # Print metrics
        print(f"\nComprehensive evaluation metrics for temporal_{temporal_window}:")
        print("=" * 60)
        
        print("\nCLASSIFICATION METRICS:")
        print("-" * 30)
        for key in ['accuracy', 'precision', 'recall', 'f1', 'roc_auc']:
            if key in metrics:
                print(f"  {key.upper()}: {metrics[key]:.4f}")
        
        print("\nREGRESSION METRICS (treating as continuous):")
        print("-" * 45)
        for key in ['r2_score', 'mse', 'rmse', 'mae', 'explained_variance', 'max_error', 'median_ae']:
            if key in metrics:
                print(f"  {key.upper()}: {metrics[key]:.4f}")

        print("\nADDITIONAL STATISTICS:")
        print("-" * 25)
        for key in ['mean_prob', 'std_prob', 'mean_target', 'std_target']:
            if key in metrics:
                print(f"  {key.upper()}: {metrics[key]:.4f}")

        # Generate comprehensive visualizations
        self._generate_comprehensive_visualizations(
            all_targets, all_preds, all_probs, all_logits, temporal_window, metrics
        )

        return metrics

    def _compute_comprehensive_metrics(
        self,
        targets: np.ndarray,
        preds: np.ndarray,
        probs: np.ndarray,
        logits: np.ndarray,
    ) -> Dict[str, float]:
        """
        Compute comprehensive evaluation metrics including both classification and regression.

        Args:
            targets: Ground truth labels
            preds: Predicted labels
            probs: Predicted probabilities
            logits: Raw model outputs (logits)

        Returns:
            Dictionary of comprehensive metrics
        """
        metrics = {}
        
        # Classification metrics
        metrics["accuracy"] = accuracy_score(targets, preds)
        metrics["precision"] = precision_score(targets, preds, zero_division=0)
        metrics["recall"] = recall_score(targets, preds, zero_division=0)
        metrics["f1"] = f1_score(targets, preds, zero_division=0)
        
        # ROC AUC (handle edge case where all targets are the same)
        if len(np.unique(targets)) > 1:
            metrics["roc_auc"] = roc_auc_score(targets, probs)
        else:
            metrics["roc_auc"] = 0.5
        
        # Regression metrics (treating probabilities as continuous predictions)
        metrics["r2_score"] = r2_score(targets, probs)
        metrics["mse"] = mean_squared_error(targets, probs)
        metrics["rmse"] = np.sqrt(metrics["mse"])
        metrics["mae"] = mean_absolute_error(targets, probs)
        metrics["explained_variance"] = explained_variance_score(targets, probs)
        metrics["max_error"] = max_error(targets, probs)
        metrics["median_ae"] = median_absolute_error(targets, probs)
        
        # Additional statistics
        metrics["mean_prob"] = np.mean(probs)
        metrics["std_prob"] = np.std(probs)
        metrics["mean_target"] = np.mean(targets)
        metrics["std_target"] = np.std(targets)
        
        # Logit-based regression metrics
        metrics["r2_logits"] = r2_score(targets, logits)
        metrics["mse_logits"] = mean_squared_error(targets, logits)
        metrics["mae_logits"] = mean_absolute_error(targets, logits)

        return metrics

    def _generate_comprehensive_visualizations(
        self,
        targets: np.ndarray,
        preds: np.ndarray,
        probs: np.ndarray,
        logits: np.ndarray,
        temporal_window: int,
        metrics: Dict[str, float],
    ):
        """
        Generate comprehensive visualizations including regression plots.

        Args:
            targets: Ground truth labels
            preds: Predicted labels
            probs: Predicted probabilities
            logits: Raw model outputs
            temporal_window: Size of temporal window
            metrics: Computed metrics
        """
        save_dir = self.config["evaluation"]["visualization"]["save_dir"]
        
        # 1. Confusion Matrix
        cm = confusion_matrix(targets, preds)
        plt.figure(figsize=(8, 6))
        sns.heatmap(
            cm,
            annot=True,
            fmt="d",
            cmap="Blues",
            xticklabels=["Unoccupied", "Occupied"],
            yticklabels=["Unoccupied", "Occupied"],
        )
        plt.xlabel("Predicted")
        plt.ylabel("True")
        plt.title(f"Confusion Matrix (Temporal Window: {temporal_window})")
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f"confusion_matrix_temporal_{temporal_window}.png"))
        plt.close()

        # 2. ROC Curve
        if len(np.unique(targets)) > 1:
            fpr, tpr, _ = roc_curve(targets, probs)
            roc_auc = auc(fpr, tpr)
            plt.figure(figsize=(8, 6))
            plt.plot(
                fpr,
                tpr,
                color="darkorange",
                lw=2,
                label=f"ROC curve (area = {roc_auc:.3f})",
            )
            plt.plot([0, 1], [0, 1], color="navy", lw=2, linestyle="--")
            plt.xlim([0.0, 1.0])
            plt.ylim([0.0, 1.05])
            plt.xlabel("False Positive Rate")
            plt.ylabel("True Positive Rate")
            plt.title(f"ROC Curve (Temporal Window: {temporal_window})")
            plt.legend(loc="lower right")
            plt.tight_layout()
            plt.savefig(os.path.join(save_dir, f"roc_curve_temporal_{temporal_window}.png"))
            plt.close()

        # 3. Regression Analysis Plot
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # Predicted vs True (Probabilities)
        ax1.scatter(targets, probs, alpha=0.6, color='blue')
        ax1.plot([0, 1], [0, 1], 'r--', lw=2)
        ax1.set_xlabel('True Labels')
        ax1.set_ylabel('Predicted Probabilities')
        ax1.set_title(f'Predicted vs True\nR² = {metrics["r2_score"]:.4f}')
        ax1.grid(True, alpha=0.3)
        
        # Residuals plot
        residuals = probs - targets
        ax2.scatter(probs, residuals, alpha=0.6, color='green')
        ax2.axhline(y=0, color='r', linestyle='--')
        ax2.set_xlabel('Predicted Probabilities')
        ax2.set_ylabel('Residuals')
        ax2.set_title('Residuals Plot')
        ax2.grid(True, alpha=0.3)
        
        # Distribution of predictions
        ax3.hist(probs, bins=50, alpha=0.7, color='blue', label='Predictions')
        ax3.hist(targets, bins=50, alpha=0.7, color='red', label='True Labels')
        ax3.set_xlabel('Value')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Distribution Comparison')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Logits vs True
        ax4.scatter(targets, logits, alpha=0.6, color='purple')
        ax4.set_xlabel('True Labels')
        ax4.set_ylabel('Logits')
        ax4.set_title(f'Logits vs True\nR² = {metrics["r2_logits"]:.4f}')
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle(f'Comprehensive Regression Analysis (Temporal Window: {temporal_window})', fontsize=16)
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f"regression_analysis_temporal_{temporal_window}.png"))
        plt.close()

        # 4. Metrics Summary Plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Classification metrics
        class_metrics = ['accuracy', 'precision', 'recall', 'f1', 'roc_auc']
        class_values = [metrics[m] for m in class_metrics]
        
        ax1.bar(class_metrics, class_values, color=['blue', 'green', 'orange', 'red', 'purple'])
        ax1.set_ylabel('Score')
        ax1.set_title('Classification Metrics')
        ax1.set_ylim(0, 1)
        for i, v in enumerate(class_values):
            ax1.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        # Regression metrics (normalized for visualization)
        reg_metrics = ['r2_score', 'explained_variance']
        reg_values = [metrics[m] for m in reg_metrics]
        
        ax2.bar(reg_metrics, reg_values, color=['cyan', 'magenta'])
        ax2.set_ylabel('Score')
        ax2.set_title('Regression Metrics')
        for i, v in enumerate(reg_values):
            ax2.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        plt.suptitle(f'Metrics Summary (Temporal Window: {temporal_window})', fontsize=16)
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f"metrics_summary_temporal_{temporal_window}.png"))
        plt.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Comprehensive evaluation of GNN occupancy model')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--checkpoint_dir', type=str, required=True, help='Path to checkpoint directory')
    parser.add_argument('--temporal_window', type=int, required=True, help='Temporal window to evaluate')
    
    args = parser.parse_args()
    
    # Load configuration
    with open(args.config, "r") as f:
        config = yaml.safe_load(f)
    
    # Override checkpoint directory and visualization directory
    config["training"]["checkpoint_dir"] = args.checkpoint_dir
    config["evaluation"]["visualization"]["save_dir"] = args.checkpoint_dir.replace("checkpoints", "visualizations")
    
    # Create data loaders
    data_loaders = create_data_loaders(config)
    
    # Create evaluator
    evaluator = ComprehensiveEvaluator(config)
    
    # Adjust input dimension based on temporal window
    if args.temporal_window > 1:
        config["model"]["input_dim"] = 10  # Additional temporal feature
    else:
        config["model"]["input_dim"] = 9   # Only spatial features
    
    # Create model
    model = create_model(config)
    
    # Load checkpoint
    checkpoint_path = os.path.join(
        args.checkpoint_dir,
        f"model_temporal_{args.temporal_window}_best.pt"
    )
    
    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path)
        model.load_state_dict(checkpoint["model_state_dict"])
        print(f"Loaded checkpoint from {checkpoint_path}")
        
        # Evaluate model
        metrics = evaluator.evaluate(
            model,
            data_loaders[f"temporal_{args.temporal_window}"]["test"],
            args.temporal_window,
        )
        
        # Save metrics to file
        metrics_file = os.path.join(args.checkpoint_dir, f"comprehensive_metrics_temporal_{args.temporal_window}.yaml")
        with open(metrics_file, 'w') as f:
            yaml.dump(metrics, f, default_flow_style=False)
        print(f"\nMetrics saved to: {metrics_file}")
        
    else:
        print(f"No checkpoint found at {checkpoint_path}")
