#!/bin/bash

# Training script for Complex GATv2 with 4 layers and temporal window 3
# Author: <PERSON><PERSON><PERSON><PERSON><PERSON> (2025)

echo "Starting Complex GATv2 training with 4 layers for temporal window 3..."
echo "Configuration:"
echo "  - GNN Type: GATv2"
echo "  - Number of layers: 4"
echo "  - Hidden dimensions: 128"
echo "  - Attention heads: 8"
echo "  - Temporal window: 3"
echo "  - Dropout: 0.3"
echo "  - Layer normalization: enabled"
echo "  - Batch normalization: enabled"
echo ""

# Set working directory
cd /home/<USER>/ma_yugi/gnn_occupancy

# Check if data directory exists
if [ ! -d "/home/<USER>/ma_yugi/data/07_gnn_ready/" ]; then
    echo "Error: Data directory not found at /home/<USER>/ma_yugi/data/07_gnn_ready/"
    echo "Please ensure the GNN-ready data is available."
    exit 1
fi

# Create output directories
mkdir -p checkpoints_gatv2_complex_4layers_temp3
mkdir -p visualizations_gatv2_complex_4layers_temp3

# Run training
echo "Starting training..."
python3 main.py --config config.yaml --mode train --temporal_window 3 --gnn_type gatv2

# Check if training was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "Training completed successfully!"
    echo "Checkpoint saved to: checkpoints_gatv2_complex_4layers_temp3/"
    
    # Run evaluation
    echo ""
    echo "Starting evaluation..."
    python3 main.py --config config.yaml --mode evaluate --temporal_window 3 --gnn_type gatv2
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "Evaluation completed successfully!"
        echo "Visualizations saved to: visualizations_gatv2_complex_4layers_temp3/"
        echo ""
        echo "Training and evaluation summary:"
        echo "================================"
        echo "Model: Complex GATv2 with 4 layers"
        echo "Hidden dimensions: 128"
        echo "Attention heads: 8"
        echo "Temporal window: 3"
        echo "Dropout: 0.3"
        echo "Layer normalization: enabled"
        echo "Batch normalization: enabled"
        echo "Checkpoints: checkpoints_gatv2_complex_4layers_temp3/"
        echo "Visualizations: visualizations_gatv2_complex_4layers_temp3/"
    else
        echo "Evaluation failed!"
        exit 1
    fi
else
    echo "Training failed!"
    exit 1
fi

echo ""
echo "All tasks completed successfully!"
