#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])

from data import create_data_loaders
from model import create_model


class ExtendedFrameAnalyzer:
    """Extended frame-by-frame analysis with more frames and detailed comparisons."""
    
    def __init__(self, config: Dict, model_name: str, checkpoint_path: str, output_dir: str):
        self.config = config
        self.model_name = model_name
        self.checkpoint_path = checkpoint_path
        self.output_dir = output_dir
        self.device = torch.device(config["training"]["device"])
        
        os.makedirs(output_dir, exist_ok=True)
        
        plt.style.use('default')
        sns.set_palette("husl")
        plt.rcParams['figure.dpi'] = 300
        plt.rcParams['savefig.dpi'] = 300
    
    def load_model_and_data(self, temporal_window: int):
        """Load model and test data."""
        print(f"Loading model and test data for temporal window {temporal_window}...")
        
        data_loaders = create_data_loaders(self.config)
        test_loader = data_loaders[f"temporal_{temporal_window}"]["test"]
        
        model = create_model(self.config)
        checkpoint = torch.load(self.checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint["model_state_dict"])
        model = model.to(self.device)
        model.eval()
        
        return model, test_loader, checkpoint
    
    def extract_extended_frame_data(self, model, test_loader, temporal_window: int, max_frames: int = 100):
        """Extract data for many frames with detailed analysis."""
        print(f"Extracting extended frame data for {max_frames} frames...")
        
        frame_data = []
        frames_collected = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="Processing batches")):
                if frames_collected >= max_frames:
                    break
                    
                batch = batch.to(self.device)
                
                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)
                probabilities = torch.sigmoid(logits)
                predictions = probabilities > 0.5
                
                # Process each graph in the batch
                for graph_idx in range(batch.num_graphs):
                    if frames_collected >= max_frames:
                        break
                        
                    node_mask = batch.batch == graph_idx
                    
                    # Extract graph data
                    graph_positions = batch.pos[node_mask].cpu().numpy()
                    graph_targets = batch.y[node_mask].cpu().numpy()
                    graph_features = batch.x[node_mask].cpu().numpy()
                    
                    # Get predictions for this graph
                    if logits.size(0) == batch.y.size(0):
                        graph_probs = probabilities[node_mask].cpu().numpy()
                        graph_preds = predictions[node_mask].cpu().numpy()
                    else:
                        if graph_idx < logits.size(0):
                            single_prob = probabilities[graph_idx].item()
                            single_pred = predictions[graph_idx].item()
                        else:
                            single_prob = probabilities.mean().item()
                            single_pred = (single_prob > 0.5)
                        
                        graph_probs = np.full(graph_targets.shape, single_prob)
                        graph_preds = np.full(graph_targets.shape, single_pred)
                    
                    # Calculate detailed metrics
                    errors = np.abs(graph_targets - graph_preds.astype(int))
                    confidences = np.abs(graph_probs - 0.5) * 2
                    
                    # Spatial statistics
                    x_range = graph_positions[:, 0].max() - graph_positions[:, 0].min()
                    y_range = graph_positions[:, 1].max() - graph_positions[:, 1].min()
                    spatial_spread = np.sqrt(x_range**2 + y_range**2)
                    
                    # Class distribution
                    true_occupied_ratio = np.mean(graph_targets)
                    pred_occupied_ratio = np.mean(graph_preds.astype(int))
                    
                    frame_info = {
                        'frame_id': frames_collected,
                        'batch_id': batch_idx,
                        'graph_id': graph_idx,
                        'positions': graph_positions,
                        'true_labels': graph_targets,
                        'predicted_labels': graph_preds.astype(int),
                        'probabilities': graph_probs,
                        'errors': errors,
                        'confidences': confidences,
                        'features': graph_features,
                        'num_points': len(graph_positions),
                        'accuracy': np.mean(graph_targets == graph_preds.astype(int)),
                        'mean_confidence': confidences.mean(),
                        'spatial_spread': spatial_spread,
                        'true_occupied_ratio': true_occupied_ratio,
                        'pred_occupied_ratio': pred_occupied_ratio,
                        'center_x': graph_positions[:, 0].mean(),
                        'center_y': graph_positions[:, 1].mean(),
                    }
                    
                    frame_data.append(frame_info)
                    frames_collected += 1
        
        print(f"Collected data for {len(frame_data)} frames")
        return frame_data
    
    def create_large_frame_grid(self, frame_data: List[Dict], temporal_window: int, 
                               frames_to_show: int = 80, grid_cols: int = 10):
        """Create large grid showing many frames."""
        print(f"Creating large frame grid for {frames_to_show} frames...")
        
        selected_frames = frame_data[:frames_to_show]
        grid_rows = (frames_to_show + grid_cols - 1) // grid_cols
        
        # Create figure for true labels
        fig_true, axes_true = plt.subplots(grid_rows, grid_cols, figsize=(grid_cols * 3, grid_rows * 3))
        if grid_rows == 1:
            axes_true = axes_true.reshape(1, -1)
        elif grid_cols == 1:
            axes_true = axes_true.reshape(-1, 1)
        
        # Create figure for predictions
        fig_pred, axes_pred = plt.subplots(grid_rows, grid_cols, figsize=(grid_cols * 3, grid_rows * 3))
        if grid_rows == 1:
            axes_pred = axes_pred.reshape(1, -1)
        elif grid_cols == 1:
            axes_pred = axes_pred.reshape(-1, 1)
        
        # Get global bounds
        all_positions = np.vstack([frame['positions'] for frame in selected_frames])
        x_min, x_max = all_positions[:, 0].min(), all_positions[:, 0].max()
        y_min, y_max = all_positions[:, 1].min(), all_positions[:, 1].max()
        
        for frame_idx, frame in enumerate(selected_frames):
            row = frame_idx // grid_cols
            col = frame_idx % grid_cols
            
            positions = frame['positions']
            true_labels = frame['true_labels']
            pred_labels = frame['predicted_labels']
            
            # True labels plot
            if row < axes_true.shape[0] and col < axes_true.shape[1]:
                ax_true = axes_true[row, col]
                ax_true.scatter(positions[:, 0], positions[:, 1], 
                               c=true_labels, cmap='RdYlBu', s=20, alpha=0.8, vmin=0, vmax=1)
                ax_true.set_xlim(x_min, x_max)
                ax_true.set_ylim(y_min, y_max)
                ax_true.set_title(f'F{frame["frame_id"]}\nAcc:{frame["accuracy"]:.2f}', fontsize=8)
                ax_true.set_xticks([])
                ax_true.set_yticks([])
            
            # Predictions plot
            if row < axes_pred.shape[0] and col < axes_pred.shape[1]:
                ax_pred = axes_pred[row, col]
                ax_pred.scatter(positions[:, 0], positions[:, 1], 
                               c=pred_labels, cmap='RdYlBu', s=20, alpha=0.8, vmin=0, vmax=1)
                ax_pred.set_xlim(x_min, x_max)
                ax_pred.set_ylim(y_min, y_max)
                ax_pred.set_title(f'F{frame["frame_id"]}\nConf:{frame["mean_confidence"]:.2f}', fontsize=8)
                ax_pred.set_xticks([])
                ax_pred.set_yticks([])
        
        # Hide unused subplots
        for frame_idx in range(frames_to_show, grid_rows * grid_cols):
            row = frame_idx // grid_cols
            col = frame_idx % grid_cols
            if row < axes_true.shape[0] and col < axes_true.shape[1]:
                axes_true[row, col].axis('off')
            if row < axes_pred.shape[0] and col < axes_pred.shape[1]:
                axes_pred[row, col].axis('off')
        
        # Save true labels grid
        fig_true.suptitle(f'{self.model_name} - True Labels Grid\n'
                         f'Temporal Window: {temporal_window}, Frames: {frames_to_show}', 
                         fontsize=16, weight='bold')
        fig_true.tight_layout()
        fig_true.savefig(os.path.join(self.output_dir, 
                                     f'true_labels_grid_temporal_{temporal_window}.png'), 
                        dpi=300, bbox_inches='tight')
        plt.close(fig_true)
        
        # Save predictions grid
        fig_pred.suptitle(f'{self.model_name} - Predictions Grid\n'
                         f'Temporal Window: {temporal_window}, Frames: {frames_to_show}', 
                         fontsize=16, weight='bold')
        fig_pred.tight_layout()
        fig_pred.savefig(os.path.join(self.output_dir, 
                                     f'predictions_grid_temporal_{temporal_window}.png'), 
                        dpi=300, bbox_inches='tight')
        plt.close(fig_pred)
        
        print("✅ Large frame grids saved")
    
    def create_temporal_evolution_analysis(self, frame_data: List[Dict], temporal_window: int):
        """Analyze how spatial patterns evolve over time."""
        print("Creating temporal evolution analysis...")
        
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        
        # Extract time series data
        frame_ids = [frame['frame_id'] for frame in frame_data]
        accuracies = [frame['accuracy'] for frame in frame_data]
        confidences = [frame['mean_confidence'] for frame in frame_data]
        spatial_spreads = [frame['spatial_spread'] for frame in frame_data]
        true_occupied_ratios = [frame['true_occupied_ratio'] for frame in frame_data]
        pred_occupied_ratios = [frame['pred_occupied_ratio'] for frame in frame_data]
        center_x = [frame['center_x'] for frame in frame_data]
        center_y = [frame['center_y'] for frame in frame_data]
        num_points = [frame['num_points'] for frame in frame_data]
        
        # 1. Accuracy evolution
        axes[0, 0].plot(frame_ids, accuracies, 'o-', linewidth=2, markersize=4, alpha=0.7)
        axes[0, 0].axhline(y=np.mean(accuracies), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(accuracies):.3f}')
        axes[0, 0].set_xlabel('Frame ID')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].set_title('Accuracy Evolution Over Frames')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Confidence evolution
        axes[0, 1].plot(frame_ids, confidences, 'o-', color='green', linewidth=2, markersize=4, alpha=0.7)
        axes[0, 1].axhline(y=np.mean(confidences), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(confidences):.3f}')
        axes[0, 1].set_xlabel('Frame ID')
        axes[0, 1].set_ylabel('Mean Confidence')
        axes[0, 1].set_title('Confidence Evolution Over Frames')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Spatial spread evolution
        axes[0, 2].plot(frame_ids, spatial_spreads, 'o-', color='purple', linewidth=2, markersize=4, alpha=0.7)
        axes[0, 2].axhline(y=np.mean(spatial_spreads), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(spatial_spreads):.2f}')
        axes[0, 2].set_xlabel('Frame ID')
        axes[0, 2].set_ylabel('Spatial Spread')
        axes[0, 2].set_title('Spatial Spread Evolution')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. Occupancy ratio comparison
        axes[1, 0].plot(frame_ids, true_occupied_ratios, 'o-', label='True', linewidth=2, markersize=4, alpha=0.7)
        axes[1, 0].plot(frame_ids, pred_occupied_ratios, 'o-', label='Predicted', linewidth=2, markersize=4, alpha=0.7)
        axes[1, 0].set_xlabel('Frame ID')
        axes[1, 0].set_ylabel('Occupied Ratio')
        axes[1, 0].set_title('Occupancy Ratio Evolution')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. Spatial center movement
        axes[1, 1].plot(center_x, center_y, 'o-', linewidth=2, markersize=4, alpha=0.7)
        axes[1, 1].set_xlabel('Center X')
        axes[1, 1].set_ylabel('Center Y')
        axes[1, 1].set_title('Spatial Center Movement')
        axes[1, 1].grid(True, alpha=0.3)
        
        # Add frame numbers as annotations for every 10th frame
        for i in range(0, len(center_x), 10):
            axes[1, 1].annotate(f'{frame_ids[i]}', (center_x[i], center_y[i]), 
                               xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        # 6. Points per frame evolution
        axes[1, 2].plot(frame_ids, num_points, 'o-', color='orange', linewidth=2, markersize=4, alpha=0.7)
        axes[1, 2].axhline(y=np.mean(num_points), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(num_points):.1f}')
        axes[1, 2].set_xlabel('Frame ID')
        axes[1, 2].set_ylabel('Number of Points')
        axes[1, 2].set_title('Points per Frame Evolution')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
        
        # 7. Accuracy vs spatial characteristics
        axes[2, 0].scatter(spatial_spreads, accuracies, alpha=0.7, s=40)
        axes[2, 0].set_xlabel('Spatial Spread')
        axes[2, 0].set_ylabel('Accuracy')
        axes[2, 0].set_title('Accuracy vs Spatial Spread')
        axes[2, 0].grid(True, alpha=0.3)
        
        # 8. Accuracy vs occupancy ratio
        axes[2, 1].scatter(true_occupied_ratios, accuracies, alpha=0.7, s=40, label='vs True')
        axes[2, 1].scatter(pred_occupied_ratios, accuracies, alpha=0.7, s=40, label='vs Pred')
        axes[2, 1].set_xlabel('Occupied Ratio')
        axes[2, 1].set_ylabel('Accuracy')
        axes[2, 1].set_title('Accuracy vs Occupancy')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)
        
        # 9. Summary statistics
        axes[2, 2].axis('off')
        stats_text = f"""
TEMPORAL EVOLUTION STATISTICS

Total Frames: {len(frame_data)}
Temporal Window: {temporal_window}

PERFORMANCE METRICS:
• Mean Accuracy: {np.mean(accuracies):.4f} ± {np.std(accuracies):.4f}
• Mean Confidence: {np.mean(confidences):.4f} ± {np.std(confidences):.4f}
• Accuracy Range: {np.min(accuracies):.3f} - {np.max(accuracies):.3f}

SPATIAL CHARACTERISTICS:
• Mean Spatial Spread: {np.mean(spatial_spreads):.2f} ± {np.std(spatial_spreads):.2f}
• Mean Points/Frame: {np.mean(num_points):.1f} ± {np.std(num_points):.1f}
• Spatial Center Range:
  X: {np.min(center_x):.2f} - {np.max(center_x):.2f}
  Y: {np.min(center_y):.2f} - {np.max(center_y):.2f}

OCCUPANCY PATTERNS:
• True Occupied: {np.mean(true_occupied_ratios):.3f} ± {np.std(true_occupied_ratios):.3f}
• Pred Occupied: {np.mean(pred_occupied_ratios):.3f} ± {np.std(pred_occupied_ratios):.3f}
• Occupancy Error: {np.mean(np.abs(np.array(true_occupied_ratios) - np.array(pred_occupied_ratios))):.3f}

CORRELATIONS:
• Accuracy-Confidence: {np.corrcoef(accuracies, confidences)[0,1]:.3f}
• Accuracy-Spread: {np.corrcoef(accuracies, spatial_spreads)[0,1]:.3f}
• Accuracy-Points: {np.corrcoef(accuracies, num_points)[0,1]:.3f}
        """
        
        axes[2, 2].text(0.05, 0.95, stats_text, transform=axes[2, 2].transAxes, 
                        fontsize=9, verticalalignment='top', fontfamily='monospace',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
        
        plt.suptitle(f'{self.model_name} - Temporal Evolution Analysis', 
                    fontsize=16, weight='bold')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 
                                f'temporal_evolution_temporal_{temporal_window}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Temporal evolution analysis saved")
    
    def run_extended_analysis(self, temporal_window: int, max_frames: int = 100):
        """Run extended frame-by-frame analysis."""
        print(f"\n🎬 Starting extended frame analysis for {self.model_name}")
        
        # Load model and data
        model, test_loader, checkpoint = self.load_model_and_data(temporal_window)
        
        # Extract extended frame data
        frame_data = self.extract_extended_frame_data(model, test_loader, temporal_window, max_frames)
        
        # Create visualizations
        self.create_large_frame_grid(frame_data, temporal_window, frames_to_show=80)
        self.create_temporal_evolution_analysis(frame_data, temporal_window)
        
        print(f"✅ Extended analysis completed for {len(frame_data)} frames")
        return frame_data


def main():
    """Run extended frame analysis for complex models."""
    
    models_to_analyze = [
        {
            'name': 'Complex GATv2 (Temp 3)',
            'config_path': 'config.yaml',
            'checkpoint_path': 'checkpoints_gatv2_complex_4layers_temp3/model_temporal_3_best.pt',
            'output_dir': 'extended_frame_analysis_complex_temp3',
            'temporal_window': 3
        },
        {
            'name': 'Complex GATv2 (Temp 5)',
            'config_path': 'config_complex_temp5.yaml',
            'checkpoint_path': 'checkpoints_gatv2_complex_temp5/model_temporal_5_best.pt',
            'output_dir': 'extended_frame_analysis_complex_temp5',
            'temporal_window': 5
        }
    ]
    
    for model_config in models_to_analyze:
        try:
            with open(model_config['config_path'], 'r') as f:
                config = yaml.safe_load(f)
            
            config['model']['input_dim'] = 10
            
            analyzer = ExtendedFrameAnalyzer(
                config=config,
                model_name=model_config['name'],
                checkpoint_path=model_config['checkpoint_path'],
                output_dir=model_config['output_dir']
            )
            
            # Run analysis with 100 frames
            frame_data = analyzer.run_extended_analysis(
                temporal_window=model_config['temporal_window'], 
                max_frames=100
            )
            
        except Exception as e:
            print(f"❌ Error analyzing {model_config['name']}: {e}")
            continue
    
    print("\n🎉 Extended frame analysis completed for all models!")


if __name__ == "__main__":
    main()
