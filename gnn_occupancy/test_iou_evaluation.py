#!/usr/bin/env python3
"""
Test script for the Comprehensive 2D IoU Evaluation System

This script provides a quick test to verify the evaluation system works correctly
before running the full evaluation on all models.

Usage:
    python test_iou_evaluation.py
"""

import sys
import os
from pathlib import Path
import torch
import numpy as np
from comprehensive_2d_iou_evaluation import (
    Comprehensive2DIoUEvaluator, 
    GridConfig, 
    ModelInfo, 
    IoUResults
)

def test_grid_conversion():
    """Test the 2D grid conversion functionality."""
    print("🧪 Testing 2D grid conversion...")
    
    # Create test evaluator
    evaluator = Comprehensive2DIoUEvaluator()
    
    # Create synthetic test data
    np.random.seed(42)
    n_points = 1000
    
    # Generate random positions in a 10x10 meter area
    positions = np.random.uniform(-5, 5, (n_points, 2))
    
    # Create synthetic labels (occupied if distance from center < 2)
    distances = np.linalg.norm(positions, axis=1)
    labels = (distances < 2.0).astype(float)
    
    # Create synthetic predictions (with some noise)
    predictions = labels + np.random.normal(0, 0.1, n_points)
    predictions = np.clip(predictions, 0, 1)
    
    # Convert to grids
    gt_grid, pred_grid, grid_info = evaluator.points_to_2d_grid(
        positions, labels, predictions
    )
    
    # Calculate IoU
    iou = evaluator.calculate_2d_iou(gt_grid, pred_grid)
    
    print(f"  ✅ Grid conversion successful")
    print(f"  📊 Grid size: {grid_info['grid_height']} x {grid_info['grid_width']}")
    print(f"  🎯 Test IoU: {iou:.4f}")
    print(f"  📍 Points processed: {grid_info['total_points']}")
    print(f"  🏠 Occupied points: {grid_info['occupied_points']}")
    
    return iou > 0.5  # Should have reasonable IoU for synthetic data

def test_model_discovery():
    """Test model discovery functionality."""
    print("🔍 Testing model discovery...")
    
    evaluator = Comprehensive2DIoUEvaluator()
    models = evaluator.discover_models()
    
    print(f"  ✅ Model discovery completed")
    print(f"  📊 Found {len(models)} models")
    
    for i, model in enumerate(models[:3]):  # Show first 3 models
        print(f"  🤖 Model {i+1}: {model.name}")
        print(f"      Architecture: {model.architecture}")
        print(f"      Temporal Window: {model.temporal_window}")
        print(f"      Parameters: {model.num_parameters:,}")
    
    if len(models) > 3:
        print(f"  ... and {len(models) - 3} more models")
    
    return len(models) > 0

def test_data_loading():
    """Test data loading functionality."""
    print("📁 Testing data loading...")
    
    evaluator = Comprehensive2DIoUEvaluator()
    
    # Test loading temporal_3 data
    try:
        test_loader_3 = evaluator.load_test_data(temporal_window=3)
        print(f"  ✅ Temporal-3 data loaded: {len(test_loader_3)} batches")
    except Exception as e:
        print(f"  ⚠️ Temporal-3 data loading failed: {e}")
        return False
    
    # Test loading temporal_5 data
    try:
        test_loader_5 = evaluator.load_test_data(temporal_window=5)
        print(f"  ✅ Temporal-5 data loaded: {len(test_loader_5)} batches")
    except Exception as e:
        print(f"  ⚠️ Temporal-5 data loading failed: {e}")
        return False
    
    # Test loading a single batch
    try:
        batch = next(iter(test_loader_3))
        print(f"  📊 Sample batch shape: {batch.x.shape}")
        print(f"  🎯 Sample labels shape: {batch.y.shape}")
        print(f"  📍 Sample positions shape: {batch.pos.shape}")
        return True
    except Exception as e:
        print(f"  ❌ Batch loading failed: {e}")
        return False

def test_visualization_setup():
    """Test visualization setup."""
    print("📈 Testing visualization setup...")
    
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        # Test basic plotting
        fig, ax = plt.subplots(1, 1, figsize=(6, 4))
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title("Test Plot")
        plt.close()
        
        print(f"  ✅ Matplotlib working correctly")
        print(f"  ✅ Seaborn available")
        return True
        
    except Exception as e:
        print(f"  ❌ Visualization setup failed: {e}")
        return False

def run_quick_evaluation_test():
    """Run a quick evaluation test on one model."""
    print("⚡ Running quick evaluation test...")
    
    evaluator = Comprehensive2DIoUEvaluator()
    models = evaluator.discover_models()
    
    if not models:
        print("  ❌ No models found for testing")
        return False
    
    # Test with the first model
    test_model = models[0]
    print(f"  🤖 Testing with model: {test_model.name}")
    
    try:
        # Limit to just a few samples for quick test
        original_load_method = evaluator.load_test_data
        
        def limited_load_test_data(temporal_window):
            loader = original_load_method(temporal_window)
            # Create a limited dataset with just 3 samples
            limited_data = []
            for i, batch in enumerate(loader):
                limited_data.append(batch)
                if i >= 2:  # Only take first 3 batches
                    break
            
            from torch_geometric.data import DataLoader
            return DataLoader(limited_data, batch_size=1, shuffle=False)
        
        evaluator.load_test_data = limited_load_test_data
        
        # Run evaluation
        results = evaluator.evaluate_model_iou(test_model)
        
        print(f"  ✅ Quick evaluation completed")
        print(f"  🎯 Mean IoU: {results.mean_iou:.4f}")
        print(f"  📊 Samples processed: {results.total_samples}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Quick evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Comprehensive 2D IoU Evaluation System - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Grid Conversion", test_grid_conversion),
        ("Model Discovery", test_model_discovery),
        ("Data Loading", test_data_loading),
        ("Visualization Setup", test_visualization_setup),
        ("Quick Evaluation", run_quick_evaluation_test),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"  ✅ {test_name} test PASSED")
            else:
                print(f"  ❌ {test_name} test FAILED")
        except Exception as e:
            print(f"  💥 {test_name} test CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The evaluation system is ready to use.")
        print("💡 Run 'python comprehensive_2d_iou_evaluation.py' for full evaluation.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        print("🔧 Fix the issues before running the full evaluation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
