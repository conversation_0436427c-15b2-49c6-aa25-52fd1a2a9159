# Configuration for GNN-based Occupancy Prediction System

# Data Configuration
data:
  data_dir: "/home/<USER>/ma_yugi/data/07_gnn_ready_old/"
  batch_size: 32
  num_workers: 4
  temporal_windows: [5]
  augmentation:
    rotation_angle: 15  # degrees (±15°)
    scaling_range: [0.9, 1.1]  # 90-110%
  binary_mapping:
    occupied: [1, 2, 3, 4]  # Workstation, Robot, Boundary, KLT
    unoccupied: [0]  # Unknown

# Model Configuration
model:
  name: "OccupancyGNN"
  input_dim: 10  # For temporal_5 with temporal encoding
  hidden_dim: 64
  output_dim: 1  # Binary classification
  num_layers: 3
  dropout: 0.2
  gnn_type: "gatv2"  # Options: "graphsage", "gatv2", "ecc"
  skip_connections: true
  batch_norm: true
  pooling: "mean_max"  # Options: "mean", "max", "mean_max"

# Training Configuration
training:
  epochs: 100
  learning_rate: 0.001
  weight_decay: 0.0001
  lr_scheduler:
    patience: 10
    factor: 0.5
    min_lr: 0.00001
  early_stopping:
    patience: 20
    min_delta: 0.001
  checkpoint_dir: "/home/<USER>/ma_yugi/gnn_occupancy/checkpoints/"
  device: "cpu"  # Options: "cuda", "cpu"
  seed: 42

# Evaluation Configuration
evaluation:
  metrics: ["accuracy", "precision", "recall", "f1", "roc_auc"]
  visualization:
    num_samples: 5
    save_dir: "/home/<USER>/ma_yugi/gnn_occupancy/visualizations/"

# Ablation Studies
ablation:
  gnn_types: ["graphsage", "gatv2", "ecc"]
  temporal_windows: [1, 3, 5]
  feature_importance: true
