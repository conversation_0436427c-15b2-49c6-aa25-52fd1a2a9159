#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from tqdm import tqdm
from typing import Dict, List, Tuple, Optional, Union, Any
from copy import deepcopy

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])

from data import create_data_loaders
from model import create_model
from train import Trainer
from evaluate import Evaluator


class AblationStudy:
    """
    Ablation study for the occupancy prediction model.
    """

    def __init__(self, config: Dict):
        """
        Initialize the ablation study.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.device = torch.device(config["training"]["device"])

        # Create visualization directory
        os.makedirs(config["evaluation"]["visualization"]["save_dir"], exist_ok=True)

        # Initialize results
        self.results = {
            "gnn_type": {},
            "temporal_window": {},
        }

    def run_gnn_type_ablation(self):
        """
        Run ablation study for different GNN types.
        """
        print("\nRunning GNN type ablation study...")

        # Create data loaders
        data_loaders = create_data_loaders(self.config)

        # Fix temporal window for this ablation
        temporal_window = 3  # Use temporal_3 for GNN type comparison

        # Adjust input dimension based on temporal window
        if temporal_window > 1:
            self.config["model"]["input_dim"] = 14  # Additional temporal feature
        else:
            self.config["model"]["input_dim"] = 13

        # Run ablation for each GNN type
        for gnn_type in self.config["ablation"]["gnn_types"]:
            print(f"\nTesting GNN type: {gnn_type}")

            # Update config
            ablation_config = deepcopy(self.config)
            ablation_config["model"]["gnn_type"] = gnn_type

            # Create model
            model = create_model(ablation_config)

            # Create trainer
            trainer = Trainer(ablation_config)

            # Train model
            model = trainer.train(
                model,
                data_loaders[f"temporal_{temporal_window}"]["train"],
                data_loaders[f"temporal_{temporal_window}"]["val"],
                temporal_window,
            )

            # Create evaluator
            evaluator = Evaluator(ablation_config)

            # Evaluate model
            metrics = evaluator.evaluate(
                model,
                data_loaders[f"temporal_{temporal_window}"]["test"],
                temporal_window,
            )

            # Store results
            self.results["gnn_type"][gnn_type] = metrics

        # Plot results
        self._plot_gnn_type_results()

    def run_temporal_window_ablation(self):
        """
        Run ablation study for different temporal window sizes.
        """
        print("\nRunning temporal window ablation study...")

        # Create data loaders
        data_loaders = create_data_loaders(self.config)

        # Fix GNN type for this ablation
        gnn_type = "graphsage"  # Use GraphSAGE for temporal window comparison

        # Run ablation for each temporal window
        for temporal_window in self.config["ablation"]["temporal_windows"]:
            print(f"\nTesting temporal window: {temporal_window}")

            # Update config
            ablation_config = deepcopy(self.config)
            ablation_config["model"]["gnn_type"] = gnn_type

            # Adjust input dimension based on temporal window
            if temporal_window > 1:
                ablation_config["model"]["input_dim"] = 10  # Additional temporal feature (9 spatial features + 1 temporal)
            else:
                ablation_config["model"]["input_dim"] = 9   # Only spatial features (no temporal)

            # Create model
            model = create_model(ablation_config)

            # Create trainer
            trainer = Trainer(ablation_config)

            # Train model
            model = trainer.train(
                model,
                data_loaders[f"temporal_{temporal_window}"]["train"],
                data_loaders[f"temporal_{temporal_window}"]["val"],
                temporal_window,
            )

            # Create evaluator
            evaluator = Evaluator(ablation_config)

            # Evaluate model
            metrics = evaluator.evaluate(
                model,
                data_loaders[f"temporal_{temporal_window}"]["test"],
                temporal_window,
            )

            # Store results
            self.results["temporal_window"][temporal_window] = metrics

        # Plot results
        self._plot_temporal_window_results()

    def _plot_gnn_type_results(self):
        """
        Plot results of GNN type ablation study.
        """
        # Convert results to DataFrame
        data = []
        for gnn_type, metrics in self.results["gnn_type"].items():
            for metric_name, metric_value in metrics.items():
                data.append({
                    "GNN Type": gnn_type,
                    "Metric": metric_name,
                    "Value": metric_value,
                })
        df = pd.DataFrame(data)

        # Create grouped bar plot
        plt.figure(figsize=(12, 8))
        sns.barplot(x="Metric", y="Value", hue="GNN Type", data=df)
        plt.title("Comparison of GNN Types")
        plt.xlabel("Metric")
        plt.ylabel("Value")
        plt.ylim(0, 1)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            "gnn_type_ablation.png"
        ))
        plt.close()

        # Save results to CSV
        df.to_csv(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            "gnn_type_ablation.csv"
        ), index=False)

    def _plot_temporal_window_results(self):
        """
        Plot results of temporal window ablation study.
        """
        # Convert results to DataFrame
        data = []
        for window_size, metrics in self.results["temporal_window"].items():
            for metric_name, metric_value in metrics.items():
                data.append({
                    "Temporal Window": f"temporal_{window_size}",
                    "Metric": metric_name,
                    "Value": metric_value,
                })
        df = pd.DataFrame(data)

        # Create grouped bar plot
        plt.figure(figsize=(12, 8))
        sns.barplot(x="Metric", y="Value", hue="Temporal Window", data=df)
        plt.title("Comparison of Temporal Window Sizes")
        plt.xlabel("Metric")
        plt.ylabel("Value")
        plt.ylim(0, 1)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            "temporal_window_ablation.png"
        ))
        plt.close()

        # Save results to CSV
        df.to_csv(os.path.join(
            self.config["evaluation"]["visualization"]["save_dir"],
            "temporal_window_ablation.csv"
        ), index=False)


if __name__ == "__main__":
    # Load configuration
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)

    # Create ablation study
    ablation = AblationStudy(config)

    # Run ablation studies
    ablation.run_gnn_type_ablation()
    ablation.run_temporal_window_ablation()
