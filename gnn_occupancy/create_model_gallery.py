#!/usr/bin/env python3

import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from PIL import Image
import os
import glob

def create_model_evaluation_gallery():
    """Create organized gallery of model evaluation results."""
    
    # Define model directories and their display names
    model_configs = [
        {
            'name': 'Complex GATv2 (Temporal 3)',
            'dir': 'checkpoints_gatv2_complex_4layers_temp3/visualizations_gatv2_complex_4layers_temp3',
            'color': 'red'
        },
        {
            'name': 'Complex GATv2 (Temporal 5)', 
            'dir': 'checkpoints_gatv2_complex_temp5/visualizations_gatv2_complex_temp5',
            'color': 'blue'
        },
        {
            'name': 'Enhanced GATv2 (Temporal 3)',
            'dir': 'checkpoints_enhanced_temp3/visualizations_enhanced_temp3', 
            'color': 'green'
        }
    ]
    
    # Create figure with custom layout
    fig = plt.figure(figsize=(24, 18))
    gs = gridspec.GridSpec(3, 6, figure=fig, hspace=0.3, wspace=0.2)
    
    for model_idx, model_config in enumerate(model_configs):
        model_name = model_config['name']
        viz_dir = model_config['dir']
        color = model_config['color']
        
        # Find visualization files
        confusion_matrix_files = glob.glob(f"{viz_dir}/*confusion_matrix*.png")
        roc_curve_files = glob.glob(f"{viz_dir}/*roc_curve*.png")
        comprehensive_files = glob.glob(f"{viz_dir}/*comprehensive*.png")
        
        # Model title
        title_ax = fig.add_subplot(gs[model_idx, :])
        title_ax.text(0.5, 0.5, model_name, ha='center', va='center', 
                     fontsize=16, weight='bold', color=color,
                     bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.1))
        title_ax.axis('off')
        
        # Confusion Matrix
        if confusion_matrix_files:
            ax_cm = fig.add_subplot(gs[model_idx, 0:2])
            try:
                img = Image.open(confusion_matrix_files[0])
                ax_cm.imshow(img)
                ax_cm.set_title('Confusion Matrix', fontsize=12, weight='bold')
                ax_cm.axis('off')
            except Exception as e:
                ax_cm.text(0.5, 0.5, f'Error loading\nConfusion Matrix', 
                          ha='center', va='center', transform=ax_cm.transAxes)
                ax_cm.axis('off')
        
        # ROC Curve
        if roc_curve_files:
            ax_roc = fig.add_subplot(gs[model_idx, 2:4])
            try:
                img = Image.open(roc_curve_files[0])
                ax_roc.imshow(img)
                ax_roc.set_title('ROC Curve', fontsize=12, weight='bold')
                ax_roc.axis('off')
            except Exception as e:
                ax_roc.text(0.5, 0.5, f'Error loading\nROC Curve', 
                           ha='center', va='center', transform=ax_roc.transAxes)
                ax_roc.axis('off')
        
        # Comprehensive Analysis
        if comprehensive_files:
            ax_comp = fig.add_subplot(gs[model_idx, 4:6])
            try:
                img = Image.open(comprehensive_files[0])
                ax_comp.imshow(img)
                ax_comp.set_title('Comprehensive Analysis', fontsize=12, weight='bold')
                ax_comp.axis('off')
            except Exception as e:
                ax_comp.text(0.5, 0.5, f'Error loading\nComprehensive Analysis', 
                            ha='center', va='center', transform=ax_comp.transAxes)
                ax_comp.axis('off')
    
    plt.suptitle('Complex GNN Models - Evaluation Results Gallery', 
                fontsize=20, weight='bold', y=0.98)
    plt.savefig('complex_models_evaluation_gallery.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Model evaluation gallery created!")

def create_performance_summary_infographic():
    """Create an infographic-style performance summary."""
    
    fig, ax = plt.subplots(figsize=(16, 12))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # Title
    ax.text(5, 9.5, 'GNN Occupancy Prediction Models', 
           ha='center', va='center', fontsize=24, weight='bold')
    ax.text(5, 9, 'Performance Summary & Analysis', 
           ha='center', va='center', fontsize=18, style='italic')
    
    # Model performance boxes
    models = [
        {
            'name': 'Complex GATv2\n(Temporal 3)',
            'accuracy': '72.84%',
            'f1': '69.58%',
            'roc_auc': '79.93%',
            'params': '169K',
            'time': '4h',
            'color': 'lightcoral',
            'pos': (1.5, 7)
        },
        {
            'name': 'Complex GATv2\n(Temporal 5)',
            'accuracy': '70.03%',
            'f1': '67.99%', 
            'roc_auc': '77.61%',
            'params': '169K',
            'time': '2.5h',
            'color': 'lightblue',
            'pos': (5, 7)
        },
        {
            'name': 'Enhanced GATv2\n(Temporal 3)',
            'accuracy': '67.25%',
            'f1': '69.90%',
            'roc_auc': '71.85%',
            'params': '6.0M',
            'time': '6h',
            'color': 'lightgreen',
            'pos': (8.5, 7)
        }
    ]
    
    for model in models:
        x, y = model['pos']
        
        # Model box
        box = plt.Rectangle((x-0.7, y-1), 1.4, 2, 
                           facecolor=model['color'], alpha=0.3, 
                           edgecolor='black', linewidth=2)
        ax.add_patch(box)
        
        # Model name
        ax.text(x, y+0.7, model['name'], ha='center', va='center', 
               fontsize=12, weight='bold')
        
        # Metrics
        ax.text(x, y+0.3, f"Accuracy: {model['accuracy']}", ha='center', va='center', fontsize=10)
        ax.text(x, y+0.1, f"F1 Score: {model['f1']}", ha='center', va='center', fontsize=10)
        ax.text(x, y-0.1, f"ROC AUC: {model['roc_auc']}", ha='center', va='center', fontsize=10)
        ax.text(x, y-0.3, f"Params: {model['params']}", ha='center', va='center', fontsize=10)
        ax.text(x, y-0.5, f"Time: {model['time']}", ha='center', va='center', fontsize=10)
    
    # Key insights section
    insights_box = plt.Rectangle((0.5, 3.5), 9, 2, 
                                facecolor='lightyellow', alpha=0.5, 
                                edgecolor='orange', linewidth=2)
    ax.add_patch(insights_box)
    
    ax.text(5, 5.2, 'Key Research Insights', ha='center', va='center', 
           fontsize=16, weight='bold')
    
    insights = [
        "• Complex GATv2 (Temp 3) achieves best accuracy (72.84%) and ROC AUC (79.93%)",
        "• Enhanced GATv2 has highest F1 score (69.90%) despite lower accuracy", 
        "• Temporal window 5 reduces training time by 31% but decreases accuracy by 2.8%",
        "• Enhanced model uses 35x more parameters but shows diminishing returns",
        "• All complex models demonstrate strong discrimination capabilities (ROC AUC > 70%)"
    ]
    
    for i, insight in enumerate(insights):
        ax.text(0.7, 4.8 - i*0.2, insight, ha='left', va='center', fontsize=11)
    
    # Recommendations section
    rec_box = plt.Rectangle((0.5, 1), 9, 2, 
                           facecolor='lightcyan', alpha=0.5, 
                           edgecolor='blue', linewidth=2)
    ax.add_patch(rec_box)
    
    ax.text(5, 2.7, 'Model Selection Recommendations', ha='center', va='center', 
           fontsize=16, weight='bold')
    
    recommendations = [
        "🎯 Production Deployment: Complex GATv2 (Temp 3) - Best overall performance",
        "⚡ Fast Training: Complex GATv2 (Temp 5) - 31% faster convergence", 
        "🔬 Research Applications: Enhanced GATv2 - Advanced architectural features",
        "📊 Balanced Performance: All models suitable for occupancy prediction tasks",
        "💡 Future Work: Hybrid approaches combining efficiency with performance"
    ]
    
    for i, rec in enumerate(recommendations):
        ax.text(0.7, 2.4 - i*0.2, rec, ha='left', va='center', fontsize=11)
    
    # Footer
    ax.text(5, 0.3, 'Analysis Date: January 25, 2025 | Total Training Time: ~12.5 hours | Total Parameters Evaluated: ~12.4M', 
           ha='center', va='center', fontsize=10, style='italic', alpha=0.7)
    
    plt.savefig('complex_models_performance_infographic.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Performance summary infographic created!")

def main():
    """Generate all gallery visualizations."""
    print("🖼️ Creating model evaluation gallery...")
    
    # Create evaluation gallery
    create_model_evaluation_gallery()
    
    # Create performance infographic
    create_performance_summary_infographic()
    
    print("🎉 All gallery visualizations completed!")

if __name__ == "__main__":
    main()
