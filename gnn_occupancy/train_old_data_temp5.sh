#!/bin/bash

# Default values
GNN_TYPE="gatv2"
TEMPORAL_WINDOW=5
EPOCHS=100
BATCH_SIZE=32
LEARNING_RATE=0.001
MODE="train"
EVAL_AFTER_TRAIN=true
VIZ_ONLY=false
MODEL_DIR=""
NUM_VIZ_SAMPLES=5

# Set up directories and config
# Create timestamp for folder name
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="output/${GNN_TYPE}_temp${TEMPORAL_WINDOW}_old_data_${TIMESTAMP}"

# Create output directories
mkdir -p "${OUTPUT_DIR}/checkpoints"
mkdir -p "${OUTPUT_DIR}/visualizations"

# Copy config file
cp /home/<USER>/ma_yugi/gnn_occupancy/config_old_data_temp5.yaml "${OUTPUT_DIR}/config.yaml"

# Update config file with parameters
sed -i "s/gnn_type: \".*\"/gnn_type: \"${GNN_TYPE}\"/g" "${OUTPUT_DIR}/config.yaml"
sed -i "s/temporal_windows: \[.*\]/temporal_windows: \[${TEMPORAL_WINDOW}\]/g" "${OUTPUT_DIR}/config.yaml"
sed -i "s/batch_size: [0-9]*/batch_size: ${BATCH_SIZE}/g" "${OUTPUT_DIR}/config.yaml"
sed -i "s/learning_rate: [0-9.e-]*/learning_rate: ${LEARNING_RATE}/g" "${OUTPUT_DIR}/config.yaml"
sed -i "s/epochs: [0-9]*/epochs: ${EPOCHS}/g" "${OUTPUT_DIR}/config.yaml"
sed -i "s|checkpoint_dir: \".*\"|checkpoint_dir: \"${OUTPUT_DIR}/checkpoints\"|g" "${OUTPUT_DIR}/config.yaml"
sed -i "s|save_dir: \".*\"|save_dir: \"${OUTPUT_DIR}/visualizations\"|g" "${OUTPUT_DIR}/config.yaml"
sed -i "s/num_samples: [0-9]*/num_samples: ${NUM_VIZ_SAMPLES}/g" "${OUTPUT_DIR}/config.yaml"

# Adjust input dimension based on temporal window
if [[ "$TEMPORAL_WINDOW" -gt 1 ]]; then
    sed -i "s/input_dim: [0-9]*/input_dim: 10/g" "${OUTPUT_DIR}/config.yaml"
else
    sed -i "s/input_dim: [0-9]*/input_dim: 9/g" "${OUTPUT_DIR}/config.yaml"
fi

# Copy config to checkpoints directory
cp "${OUTPUT_DIR}/config.yaml" "${OUTPUT_DIR}/checkpoints/config.yaml"

echo "Created output directory: ${OUTPUT_DIR}"
echo "Updated configuration saved to: ${OUTPUT_DIR}/config.yaml"

CONFIG_PATH="${OUTPUT_DIR}/config.yaml"

# Execute the training
echo "Starting training with GNN type: ${GNN_TYPE}, temporal window: ${TEMPORAL_WINDOW} on old data"
python3 /home/<USER>/ma_yugi/gnn_occupancy/main.py --mode train --temporal_window "${TEMPORAL_WINDOW}" --gnn_type "${GNN_TYPE}" --config "${CONFIG_PATH}"

# Run evaluation after training
echo "Running evaluation after training"
python3 /home/<USER>/ma_yugi/gnn_occupancy/main.py --mode evaluate --temporal_window "${TEMPORAL_WINDOW}" --gnn_type "${GNN_TYPE}" --config "${CONFIG_PATH}"

# Run visualization
echo "Running visualization"
python3 /home/<USER>/ma_yugi/gnn_occupancy/main.py --mode evaluate --temporal_window "${TEMPORAL_WINDOW}" --gnn_type "${GNN_TYPE}" --config "${CONFIG_PATH}" --visualize-only

echo "Process completed. Results saved to: ${OUTPUT_DIR}"
