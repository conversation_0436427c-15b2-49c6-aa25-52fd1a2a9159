#!/usr/bin/env python3

import os
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import <PERSON>O_SHAPE
from pptx.enum.dml import MSO_THEME_COLOR

# Define modern color palette globally
PRIMARY_BLUE = RGBColor(30, 58, 138)      # Deep blue
ACCENT_BLUE = RGBColor(59, 130, 246)      # Bright blue
LIGHT_BLUE = RGBColor(219, 234, 254)      # Very light blue
SUCCESS_GREEN = RGBColor(34, 197, 94)     # Modern green
WARNING_ORANGE = RGBColor(251, 146, 60)   # Modern orange
DANGER_RED = RGBColor(239, 68, 68)        # Modern red
DARK_GRAY = RGBColor(55, 65, 81)          # Dark gray
LIGHT_GRAY = RGBColor(243, 244, 246)      # Light gray
WHITE = RGBColor(255, 255, 255)           # Pure white

def add_gradient_background(slide, color1, color2):
    """Add a subtle gradient background to slide."""
    try:
        # Create gradient background
        background = slide.background
        fill = background.fill
        fill.gradient()
        fill.gradient_stops[0].color.rgb = color1
        fill.gradient_stops[1].color.rgb = color2
    except:
        # Fallback to solid color if gradient fails
        slide.background.fill.solid()
        slide.background.fill.fore_color.rgb = LIGHT_GRAY

def create_styled_textbox(slide, left, top, width, height, text, font_size=14, bold=False, color=None, alignment=PP_ALIGN.LEFT):
    """Create a properly styled text box."""
    textbox = slide.shapes.add_textbox(left, top, width, height)
    text_frame = textbox.text_frame
    text_frame.text = text
    text_frame.margin_left = Inches(0.1)
    text_frame.margin_right = Inches(0.1)
    text_frame.margin_top = Inches(0.05)
    text_frame.margin_bottom = Inches(0.05)
    text_frame.word_wrap = True

    paragraph = text_frame.paragraphs[0]
    paragraph.alignment = alignment
    paragraph.font.size = Pt(font_size)
    paragraph.font.bold = bold
    if color:
        paragraph.font.color.rgb = color

    return textbox

def create_modern_card(slide, left, top, width, height, title, content, accent_color):
    """Create a modern card-style container."""
    # Main card background
    card = slide.shapes.add_shape(MSO_SHAPE.ROUNDED_RECTANGLE, left, top, width, height)
    card.fill.solid()
    card.fill.fore_color.rgb = WHITE
    card.line.color.rgb = LIGHT_GRAY
    card.line.width = Pt(1)

    # Accent bar on the left
    accent_bar = slide.shapes.add_shape(
        MSO_SHAPE.RECTANGLE,
        left, top, Inches(0.1), height
    )
    accent_bar.fill.solid()
    accent_bar.fill.fore_color.rgb = accent_color
    accent_bar.line.fill.background()

    # Title
    title_box = create_styled_textbox(
        slide, left + Inches(0.2), top + Inches(0.1),
        width - Inches(0.3), Inches(0.4),
        title, font_size=16, bold=True, color=PRIMARY_BLUE
    )

    # Content
    content_box = create_styled_textbox(
        slide, left + Inches(0.2), top + Inches(0.5),
        width - Inches(0.3), height - Inches(0.6),
        content, font_size=12, color=DARK_GRAY
    )

    return card

def create_metric_card(slide, left, top, width, height, metric_name, value, color, description=""):
    """Create a metric display card."""
    # Main card
    card = slide.shapes.add_shape(MSO_SHAPE.ROUNDED_RECTANGLE, left, top, width, height)
    card.fill.solid()
    card.fill.fore_color.rgb = WHITE
    card.line.color.rgb = LIGHT_GRAY
    card.line.width = Pt(1)

    # Metric value (large)
    value_box = create_styled_textbox(
        slide, left + Inches(0.1), top + Inches(0.1),
        width - Inches(0.2), Inches(0.6),
        value, font_size=24, bold=True, color=color, alignment=PP_ALIGN.CENTER
    )

    # Metric name
    name_box = create_styled_textbox(
        slide, left + Inches(0.1), top + Inches(0.7),
        width - Inches(0.2), Inches(0.3),
        metric_name, font_size=12, bold=True, color=DARK_GRAY, alignment=PP_ALIGN.CENTER
    )

    # Description if provided
    if description:
        desc_box = create_styled_textbox(
            slide, left + Inches(0.1), top + Inches(1.0),
            width - Inches(0.2), Inches(0.3),
            description, font_size=10, color=DARK_GRAY, alignment=PP_ALIGN.CENTER
        )

    return card

def create_progress_presentation():
    """Create a comprehensive PowerPoint presentation showing project progress."""

    # Create presentation
    prs = Presentation()

    # Set slide size to 16:9
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)

    # Slide 1: Modern Title Slide
    slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout for custom design
    add_gradient_background(slide, WHITE, LIGHT_BLUE)

    # Main title
    title_box = create_styled_textbox(
        slide, Inches(1), Inches(1.5), Inches(11.33), Inches(1.5),
        "Graph Neural Networks for Occupancy Prediction in Robotics",
        font_size=36, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Subtitle
    subtitle_box = create_styled_textbox(
        slide, Inches(1), Inches(3), Inches(11.33), Inches(0.8),
        "From Point Cloud Annotation to Model Training",
        font_size=20, color=ACCENT_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Author and details
    author_box = create_styled_textbox(
        slide, Inches(1), Inches(5), Inches(11.33), Inches(1.5),
        "Thiyanayugi Mariraj\nMaster's Thesis Progress Presentation\nJanuary 2025",
        font_size=16, color=DARK_GRAY, alignment=PP_ALIGN.CENTER
    )

    # Slide 2: Project Overview with Cards
    slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
    add_gradient_background(slide, WHITE, LIGHT_GRAY)

    # Title
    title_box = create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Project Overview",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Objective card
    create_modern_card(
        slide, Inches(0.5), Inches(1.3), Inches(12.33), Inches(1.2),
        "🎯 Objective",
        "Develop a Graph Neural Network (GNN) system for binary occupancy prediction in robotics environments using radar point cloud data.",
        ACCENT_BLUE
    )

    # Key Components card
    create_modern_card(
        slide, Inches(0.5), Inches(2.7), Inches(6), Inches(2.3),
        "🔧 Key Components",
        "• Point cloud annotation system\n• GNN frame conversion pipeline\n• Multiple GNN architectures\n• Comprehensive evaluation framework",
        SUCCESS_GREEN
    )

    # Data Pipeline card
    create_modern_card(
        slide, Inches(6.8), Inches(2.7), Inches(5.83), Inches(2.3),
        "📊 Data Processing Pipeline",
        "1. Raw rosbag data extraction\n2. Point cloud annotation\n3. GNN frame conversion\n4. Model training & evaluation",
        WARNING_ORANGE
    )

    # Slide 3: Modern Pipeline Visualization
    slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
    add_gradient_background(slide, WHITE, LIGHT_BLUE)

    # Title
    title_box = create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Complete Data Processing Pipeline",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Pipeline steps with modern design
    steps = [
        ("📁", "Raw Rosbags", ACCENT_BLUE),
        ("🔄", "Data Extraction", SUCCESS_GREEN),
        ("⏱️", "Synchronization", WARNING_ORANGE),
        ("🔄", "Coordinate Transform", ACCENT_BLUE),
        ("🧹", "Data Cleaning", SUCCESS_GREEN),
        ("🏷️", "Point Cloud Annotation", WARNING_ORANGE),
        ("🔗", "GNN Frame Conversion", ACCENT_BLUE),
        ("🤖", "Model Training", SUCCESS_GREEN)
    ]

    # Create pipeline in a grid layout
    cols = 4
    rows = 2
    card_width = Inches(2.8)
    card_height = Inches(1.2)
    start_x = Inches(1)
    start_y = Inches(1.5)
    spacing_x = Inches(3.1)
    spacing_y = Inches(1.5)

    for i, (icon, step, color) in enumerate(steps):
        row = i // cols
        col = i % cols
        x = start_x + col * spacing_x
        y = start_y + row * spacing_y

        create_modern_card(
            slide, x, y, card_width, card_height,
            f"{icon} {step}",
            "",
            color
        )

    # Slide 4: Point Cloud Annotation Process
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Point Cloud Annotation Process"
    content.text = """annotate_point_clouds.py: Automated annotation system that labels point cloud data with semantic categories.

Annotation Categories:
• Workstations (AS_1, AS_3, AS_4, AS_5, AS_6)
• Other Robot (collaborative robots)
• Arena Boundary (environment limits)
• KLT Objects (transport containers)
• Unknown (unclassified points)

Key Features:
• Geometric tolerance-based labeling
• Layout-aware positioning (Layout 2 & 3)
• Temporal timestamp handling
• Visualization generation
• Batch processing capability

Tolerance Parameters:
• Workstation tolerance: 20cm
• Robot tolerance: 12cm
• Boundary tolerance: 25cm"""

    # Slide 5: Annotation Algorithm
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Annotation Algorithm"
    content.text = """Core Algorithm - For each point cloud frame at timestamp t:

1. Load workstation positions from layout files (Layout 2/3 based on timestamp)
2. Extract robot positions from Vicon data
3. For each point (x, y, z):
   • Check proximity to workstations using geometric tolerances
   • Check proximity to robot positions
   • Check proximity to arena boundaries
   • Assign label based on closest match or "unknown"
4. Generate visualizations (optional)
5. Save annotated CSV with additional columns: annotation_specific, annotation_general

Key Innovation:
Layout-aware annotation: Automatically switches between Layout 2 and Layout 3 based on dataset timestamps (16:38 threshold)."""

    # Slide 6: GNN Frame Conversion Process
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "GNN Frame Conversion Process"
    content.text = """direct_annotated_to_gnn.py: Converts annotated point clouds into PyTorch Geometric graph structures for GNN training.

Conversion Steps:
1. Voxelization (0.1m voxel size)
2. Feature extraction (13 features per node)
3. Graph construction (fully connected)
4. Temporal aggregation (1, 3, 5 frame windows)
5. Label mapping (5-class to binary)

Node Features (13D):
• Normalized position (x, y, z)
• Raw position (x, y, z)
• Position relative to center (x, y, z)
• Position relative to origin (x, y, z)
• Distance to center (1D)

Label Mapping:
{unknown: 0, workstation: 1, robot: 1, boundary: 1, KLT: 1} → {unoccupied: 0, occupied: 1}"""

    # Slide 7: Model Architectures
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "GNN Model Architectures"
    content.text = """Seven Different Models Evaluated - Comprehensive comparison across different architectures and complexity levels.

Models:
• GraphSAGE (Old Data) - 3 layers, 15K parameters, Temporal 3
• Complex GATv2 (Temp 3) - 4 layers, 169K parameters, Temporal 3
• Complex GATv2 (Temp 5) - 4 layers, 169K parameters, Temporal 5
• Enhanced GATv2 - 4 layers, 6.0M parameters, Temporal 3
• GATv2 Standard - 3 layers, 25K parameters, Temporal 3
• GATv2 (Temp 5) - 3 layers, 30K parameters, Temporal 5
• GATv2 5-Layer - 5 layers, 52K parameters, Temporal 3

Key Architectural Features:
• Attention mechanisms (GATv2) vs aggregation (GraphSAGE)
• Normalization layers (Batch + Layer normalization)
• Skip connections and dropout regularization
• Multiple pooling strategies (mean, max, mean+max)"""

    return prs

def add_performance_slides(prs):
    """Add performance comparison slides."""

    dark_blue = RGBColor(0, 51, 102)
    green = RGBColor(0, 128, 0)
    red = RGBColor(220, 20, 60)

    # Slide 8: Performance Comparison
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Performance Comparison - All Models"
    content.text = """Model Performance Rankings:

1. GraphSAGE (Old Data): 73.04% Acc, 78.72% F1, 76.13% ROC AUC
2. Complex GATv2 (Temp 3): 72.84% Acc, 69.58% F1, 79.93% ROC AUC
3. Complex GATv2 (Temp 5): 70.03% Acc, 67.99% F1, 77.61% ROC AUC
4. Enhanced GATv2 (Temp 3): 67.25% Acc, 69.90% F1, 71.85% ROC AUC
5. GATv2 Standard (Temp 3): 66.17% Acc, 69.31% F1, 69.48% ROC AUC
6. GATv2 (Temp 5): 63.85% Acc, 68.06% F1, 68.96% ROC AUC
7. GATv2 5-Layer (Temp 3): 57.83% Acc, 66.79% F1, 64.89% ROC AUC

Key Findings:
✓ GraphSAGE achieves highest overall performance despite simplicity
✓ Complex GATv2 (Temp 3) best among current data models
⚠ Parameter count doesn't correlate with performance
✓ Attention mechanisms improve discrimination (ROC AUC)"""

    # Slide 9: Parameter Efficiency Analysis
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Parameter Efficiency Analysis"
    content.text = """Parameter Efficiency (F1 Score per 1K parameters):

1. GraphSAGE (Old Data): 15K params → 78.72% F1 → 5.25 efficiency
2. GATv2 Standard: 25K params → 69.31% F1 → 2.77 efficiency
3. GATv2 (Temp 5): 30K params → 68.06% F1 → 2.27 efficiency
4. GATv2 5-Layer: 52K params → 66.79% F1 → 1.29 efficiency
5. Complex GATv2 (Temp 3): 169K params → 69.58% F1 → 0.41 efficiency
6. Complex GATv2 (Temp 5): 169K params → 67.99% F1 → 0.40 efficiency
7. Enhanced GATv2: 6.0M params → 69.90% F1 → 0.012 efficiency

Efficiency Insights:
✓ GraphSAGE: Most parameter-efficient (5.25 F1 points per 1K parameters)
⚠ Enhanced GATv2: Least efficient (0.012 F1 points per 1K parameters)
✓ Sweet spot: 15K-169K parameters for optimal performance/efficiency trade-off"""

    # Slide 10: Training Efficiency
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Training Efficiency Comparison"
    content.text = """Training Time and Convergence Analysis:

1. GATv2 Standard: 1h, 22 epochs, 66.17% acc → 0.91 min/%
2. GATv2 5-Layer: 1h, 21 epochs, 57.83% acc → 1.04 min/%
3. GATv2 (Temp 5): 1.5h, 32 epochs, 63.85% acc → 1.41 min/%
4. GraphSAGE: 2h, 46 epochs, 73.04% acc → 1.64 min/%
5. Complex GATv2 (Temp 5): 2.5h, 59 epochs, 70.03% acc → 2.14 min/%
6. Complex GATv2 (Temp 3): 4h, 86 epochs, 72.84% acc → 3.30 min/%
7. Enhanced GATv2: 6h, 11 epochs*, 67.25% acc → 5.35 min/%

*Early stopping applied

Training Insights:
✓ Faster convergence with temporal window 5 vs 3 (59 vs 86 epochs)
⚠ Complex models require longer training times
✓ Early stopping essential for Enhanced GATv2 to prevent overfitting"""

    return prs

def add_complex_models_slides(prs):
    """Add complex models analysis slides."""

    dark_blue = RGBColor(0, 51, 102)
    light_blue = RGBColor(173, 216, 230)
    green = RGBColor(0, 128, 0)

    # Slide 11: Focus on Complex Models
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Focus on Complex Models"
    content.text = """Top Performing Current Data Models - Detailed analysis of the three best-performing models on current dataset.

Performance Comparison:
• Complex GATv2 (Temp 3): 72.84% Acc, 69.58% F1, 79.93% ROC AUC, 4h training → Best Accuracy
• Complex GATv2 (Temp 5): 70.03% Acc, 67.99% F1, 77.61% ROC AUC, 2.5h training → Temporal Context
• Enhanced GATv2 (Temp 3): 67.25% Acc, 69.90% F1, 71.85% ROC AUC, 6h training → Advanced Features

Complex GATv2 Architecture:
• 4 layers with 8 attention heads each
• 128 hidden dimensions with comprehensive normalization
• Layer normalization + Batch normalization
• Skip connections and dropout (0.3)

Enhanced GATv2 Features:
• Residual connections and self-attention mechanisms
• Hierarchical pooling and 6M parameters
• Transformer-inspired architectural components"""

    # Slide 12: Temporal Window Analysis
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Temporal Window Analysis"
    content.text = """Impact of Temporal Context - Comparison between 3-frame and 5-frame temporal windows.

Temporal Window 3:
✓ Higher accuracy (72.84%)
✓ Better ROC AUC (79.93%)
⚠ Longer training (86 epochs)
• Focused temporal context

Temporal Window 5:
• Extended context (5 frames)
✓ Faster convergence (59 epochs)
⚠ Lower accuracy (70.03%)
• Richer temporal information

Trade-off Analysis:
Accuracy vs Context: Extending temporal window from 3 to 5 frames provides more context but reduces accuracy by 2.81%.
Training Efficiency: 5-frame window converges 31% faster."""

    # Slide 13: Key Findings
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Major Research Insights"
    content.text = """Architectural Design Principles - What works and what doesn't in GNN occupancy prediction.

What Works:
✓ Attention mechanisms improve discrimination
✓ Normalization layers boost performance
✓ Skip connections help complex architectures
✓ Moderate depth (3-4 layers) optimal
✓ Balanced parameters (15K-169K) sweet spot

What Doesn't Work:
⚠ Excessive depth (5+ layers) hurts performance
⚠ Too many parameters (6M) lead to overfitting
⚠ Complex temporal windows may reduce accuracy
⚠ Insufficient regularization in complex models

Surprising Finding:
Simplicity wins: GraphSAGE (15K parameters) outperforms Enhanced GATv2 (6M parameters) by 5.82% in accuracy."""

    # Slide 14: Model Selection Framework
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Performance vs Complexity Trade-offs"
    content.text = """Model Selection Framework - Choose the right model based on application requirements.

Use Case Recommendations:
• Production Deployment → Complex GATv2 (Temp 3): 72.84% Acc → Best current data performance
• Resource Constrained → GraphSAGE: 5.25 Efficiency → Highest parameter efficiency
• Safety Critical → GATv2 5-Layer: 93.06% Recall → Maximum recall for safety
• Research Applications → Enhanced GATv2: 6M Params → Advanced architectural features
• Fast Deployment → GATv2 Standard: 1h Training → Quick training time
• Temporal Analysis → Complex GATv2 (Temp 5): 5 Frames → Extended temporal context

Decision Criteria:
• Accuracy priority → Complex GATv2 (Temp 3)
• Efficiency priority → GraphSAGE
• Safety priority → GATv2 5-Layer (highest recall)"""

    return prs

def add_visualization_slides(prs):
    """Add visualization slides with images."""

    dark_blue = RGBColor(0, 51, 102)

    # Slide 15: Model Performance Visualizations
    slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(12), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = "Model Performance Visualizations"
    title_frame.paragraphs[0].font.size = Pt(32)
    title_frame.paragraphs[0].font.bold = True
    title_frame.paragraphs[0].font.color.rgb = dark_blue

    # Add image if it exists
    img_path = "comprehensive_model_comparison_dashboard.png"
    if os.path.exists(img_path):
        slide.shapes.add_picture(img_path, Inches(0.5), Inches(1.2), Inches(12), Inches(5.8))
    else:
        # Add placeholder text
        placeholder = slide.shapes.add_textbox(Inches(2), Inches(3), Inches(9), Inches(2))
        placeholder.text_frame.text = "Comprehensive model comparison dashboard showing performance metrics, parameter efficiency, and training dynamics across all seven models."
        placeholder.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        placeholder.text_frame.paragraphs[0].font.size = Pt(16)

    # Slide 16: Complex Models Deep Dive
    slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(12), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = "Complex Models Deep Dive"
    title_frame.paragraphs[0].font.size = Pt(32)
    title_frame.paragraphs[0].font.bold = True
    title_frame.paragraphs[0].font.color.rgb = dark_blue

    # Add image if it exists
    img_path = "complex_models_deep_dive_analysis.png"
    if os.path.exists(img_path):
        slide.shapes.add_picture(img_path, Inches(0.5), Inches(1.2), Inches(12), Inches(5.8))
    else:
        # Add placeholder text
        placeholder = slide.shapes.add_textbox(Inches(2), Inches(3), Inches(9), Inches(2))
        placeholder.text_frame.text = "Detailed analysis of the three complex models focusing on architecture comparison, temporal window effects, and performance trade-offs."
        placeholder.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        placeholder.text_frame.paragraphs[0].font.size = Pt(16)

    # Slide 17: Model Evaluation Results
    slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(12), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = "Model Evaluation Results"
    title_frame.paragraphs[0].font.size = Pt(32)
    title_frame.paragraphs[0].font.bold = True
    title_frame.paragraphs[0].font.color.rgb = dark_blue

    # Add image if it exists
    img_path = "complex_models_evaluation_gallery.png"
    if os.path.exists(img_path):
        slide.shapes.add_picture(img_path, Inches(0.5), Inches(1.2), Inches(12), Inches(5.8))
    else:
        # Add placeholder text
        placeholder = slide.shapes.add_textbox(Inches(2), Inches(3), Inches(9), Inches(2))
        placeholder.text_frame.text = "Gallery of evaluation results including confusion matrices, ROC curves, and comprehensive analysis plots for the top-performing complex models."
        placeholder.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        placeholder.text_frame.paragraphs[0].font.size = Pt(16)

    return prs

def add_future_work_slides(prs):
    """Add future work and conclusion slides."""

    dark_blue = RGBColor(0, 51, 102)
    green = RGBColor(0, 128, 0)

    # Slide 18: Future Research Directions
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Future Research Directions"
    content.text = """Next Steps and Improvements - Opportunities for advancing GNN-based occupancy prediction.

Architectural Enhancements:
• Hybrid models combining GraphSAGE efficiency with GATv2 attention
• Adaptive complexity based on scene complexity
• Efficient attention for resource-constrained environments
• Progressive training methodologies

Temporal Modeling:
• Adaptive temporal windows based on motion patterns
• Cross-temporal attention mechanisms
• Multi-scale temporal processing
• Temporal consistency regularization

Training Optimization:
• Knowledge distillation from complex to efficient models
• Meta-learning for rapid adaptation to new environments
• Multi-task learning integration"""

    # Slide 19: Implementation Roadmap
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Implementation Roadmap"
    content.text = """Immediate Next Steps - Concrete actions for continued development.

Short-term (1-2 months):
1. Deploy Complex GATv2 (Temp 3) for production testing
2. Implement real-time inference pipeline
3. Collect additional validation data
4. Optimize model checkpointing and deployment

Medium-term (3-6 months):
1. Develop hybrid architectures combining best features
2. Implement adaptive temporal window selection
3. Create model ensemble strategies
4. Extend to multi-robot scenarios

Long-term (6+ months):
1. Real-world deployment and validation
2. Integration with robot control systems
3. Scalability testing for larger environments
4. Publication and dissemination of results"""

    # Slide 20: Summary and Conclusions
    slide = prs.slides.add_slide(prs.slide_layouts[1])
    title = slide.shapes.title
    content = slide.placeholders[1]

    title.text = "Summary and Conclusions"
    content.text = """Project Achievements - Successfully developed and evaluated a comprehensive GNN system for occupancy prediction.

Key Accomplishments:
✓ Complete pipeline from raw data to trained models
✓ Seven different architectures evaluated comprehensively
✓ Best model achieves 72.84% accuracy on current data
✓ Comprehensive analysis of performance trade-offs
✓ Production-ready system with deployment guidelines

Scientific Contributions:
• Systematic comparison of GNN architectures for occupancy prediction
• Temporal window analysis showing trade-offs between context and accuracy
• Parameter efficiency insights challenging the "bigger is better" paradigm
• Practical guidelines for model selection based on application requirements

Main Finding:
Architectural simplicity often outperforms complexity in GNN-based occupancy prediction, with GraphSAGE achieving the best overall performance despite having 400x fewer parameters than the most complex model."""

    # Slide 21: Thank You
    slide = prs.slides.add_slide(prs.slide_layouts[5])  # Blank layout

    # Add centered thank you text
    thank_you = slide.shapes.add_textbox(Inches(2), Inches(2.5), Inches(9.33), Inches(2))
    thank_you_frame = thank_you.text_frame
    thank_you_frame.text = "Thank You\n\nQuestions & Discussion"
    thank_you_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    thank_you_frame.paragraphs[0].font.size = Pt(48)
    thank_you_frame.paragraphs[0].font.bold = True
    thank_you_frame.paragraphs[0].font.color.rgb = dark_blue

    thank_you_frame.paragraphs[1].alignment = PP_ALIGN.CENTER
    thank_you_frame.paragraphs[1].font.size = Pt(24)
    thank_you_frame.paragraphs[1].font.color.rgb = dark_blue

    # Add contact information
    contact = slide.shapes.add_textbox(Inches(2), Inches(5.5), Inches(9.33), Inches(1.5))
    contact_frame = contact.text_frame
    contact_frame.text = "Contact: <EMAIL>\nGitHub: github.com/thiyanayugi\nProject Repository: GNN Occupancy Prediction System"
    contact_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    contact_frame.paragraphs[0].font.size = Pt(14)

    return prs

if __name__ == "__main__":
    print("🎨 Creating PowerPoint presentation...")

    # Create the presentation
    prs = create_progress_presentation()
    prs = add_performance_slides(prs)
    prs = add_complex_models_slides(prs)
    prs = add_visualization_slides(prs)
    prs = add_future_work_slides(prs)

    # Save the presentation
    output_file = "gnn_progress_presentation.pptx"
    prs.save(output_file)

    print(f"✅ PowerPoint presentation created successfully: {output_file}")
    print(f"📊 Total slides: {len(prs.slides)}")
    print("\n📋 Slide Contents:")
    slide_titles = [
        "Title Slide",
        "Project Overview",
        "Data Processing Pipeline",
        "Point Cloud Annotation Process",
        "Annotation Algorithm",
        "GNN Frame Conversion Process",
        "Model Architectures",
        "Performance Comparison - All Models",
        "Parameter Efficiency Analysis",
        "Training Efficiency Comparison",
        "Focus on Complex Models",
        "Temporal Window Analysis",
        "Major Research Insights",
        "Performance vs Complexity Trade-offs",
        "Model Performance Visualizations",
        "Complex Models Deep Dive",
        "Model Evaluation Results",
        "Future Research Directions",
        "Implementation Roadmap",
        "Summary and Conclusions",
        "Thank You"
    ]

    for i, title in enumerate(slide_titles, 1):
        print(f"  {i:2d}. {title}")

    print(f"\n🎯 Presentation ready for your professor meeting!")
    print(f"📁 File location: {os.path.abspath(output_file)}")
