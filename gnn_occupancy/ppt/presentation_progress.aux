\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{1}{1/1}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {1}{1}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{2}{2/2}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {2}{2}}}
\@writefile{toc}{\beamer@sectionintoc {1}{Project Overview}{3}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\sectionentry {1}{Project Overview}{3}{Project Overview}{0}}}
\@writefile{nav}{\headcommand {\slideentry {1}{0}{1}{3/3}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {3}{3}}}
\@writefile{toc}{\beamer@sectionintoc {2}{Data Processing Pipeline}{4}{0}{2}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {3}{3}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {3}{3}}}
\@writefile{nav}{\headcommand {\sectionentry {2}{Data Processing Pipeline}{4}{Data Processing Pipeline}{0}}}
\@writefile{nav}{\headcommand {\slideentry {2}{0}{1}{4/4}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {4}{4}}}
\@writefile{toc}{\beamer@sectionintoc {3}{Point Cloud Annotation}{5}{0}{3}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {4}{4}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {4}{4}}}
\@writefile{nav}{\headcommand {\sectionentry {3}{Point Cloud Annotation}{5}{Point Cloud Annotation}{0}}}
\@writefile{nav}{\headcommand {\slideentry {3}{0}{1}{5/5}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {5}{5}}}
\@writefile{nav}{\headcommand {\slideentry {3}{0}{2}{6/6}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {6}{6}}}
\@writefile{toc}{\beamer@sectionintoc {4}{GNN Frame Conversion}{7}{0}{4}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {5}{6}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {5}{6}}}
\@writefile{nav}{\headcommand {\sectionentry {4}{GNN Frame Conversion}{7}{GNN Frame Conversion}{0}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{1}{7/7}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {7}{7}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{2}{8/8}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {8}{8}}}
\@writefile{toc}{\beamer@sectionintoc {5}{Model Architectures}{9}{0}{5}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {7}{8}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {7}{8}}}
\@writefile{nav}{\headcommand {\sectionentry {5}{Model Architectures}{9}{Model Architectures}{0}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{1}{9/9}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {9}{9}}}
\@writefile{toc}{\beamer@sectionintoc {6}{Training Results}{10}{0}{6}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {9}{9}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {9}{9}}}
\@writefile{nav}{\headcommand {\sectionentry {6}{Training Results}{10}{Training Results}{0}}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{1}{10/10}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {10}{10}}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{2}{11/11}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {11}{11}}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{3}{12/12}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {12}{12}}}
\@writefile{toc}{\beamer@sectionintoc {7}{Complex Models Analysis}{13}{0}{7}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {10}{12}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {10}{12}}}
\@writefile{nav}{\headcommand {\sectionentry {7}{Complex Models Analysis}{13}{Complex Models Analysis}{0}}}
\@writefile{nav}{\headcommand {\slideentry {7}{0}{1}{13/13}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {13}{13}}}
\@writefile{nav}{\headcommand {\slideentry {7}{0}{2}{14/14}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {14}{14}}}
\@writefile{toc}{\beamer@sectionintoc {8}{Visualizations}{15}{0}{8}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {13}{14}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {13}{14}}}
\@writefile{nav}{\headcommand {\sectionentry {8}{Visualizations}{15}{Visualizations}{0}}}
\@writefile{nav}{\headcommand {\slideentry {8}{0}{1}{15/15}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {15}{15}}}
\@writefile{nav}{\headcommand {\slideentry {8}{0}{2}{16/16}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {16}{16}}}
\@writefile{nav}{\headcommand {\slideentry {8}{0}{3}{17/17}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {17}{17}}}
\@writefile{toc}{\beamer@sectionintoc {9}{Key Findings}{18}{0}{9}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {15}{17}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {15}{17}}}
\@writefile{nav}{\headcommand {\sectionentry {9}{Key Findings}{18}{Key Findings}{0}}}
\@writefile{nav}{\headcommand {\slideentry {9}{0}{1}{18/18}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {18}{18}}}
\@writefile{nav}{\headcommand {\slideentry {9}{0}{2}{19/19}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {19}{19}}}
\@writefile{nav}{\headcommand {\slideentry {9}{0}{3}{20/20}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {20}{20}}}
\@writefile{toc}{\beamer@sectionintoc {10}{Future Work}{21}{0}{10}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {18}{20}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {18}{20}}}
\@writefile{nav}{\headcommand {\sectionentry {10}{Future Work}{21}{Future Work}{0}}}
\@writefile{nav}{\headcommand {\slideentry {10}{0}{1}{21/21}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {21}{21}}}
\@writefile{nav}{\headcommand {\slideentry {10}{0}{2}{22/22}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {22}{22}}}
\@writefile{toc}{\beamer@sectionintoc {11}{Conclusion}{23}{0}{11}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {21}{22}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {21}{22}}}
\@writefile{nav}{\headcommand {\sectionentry {11}{Conclusion}{23}{Conclusion}{0}}}
\@writefile{nav}{\headcommand {\slideentry {11}{0}{1}{23/23}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {23}{23}}}
\@writefile{nav}{\headcommand {\slideentry {11}{0}{2}{24/24}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {24}{24}}}
\@writefile{nav}{\headcommand {\beamer@partpages {1}{24}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {23}{24}}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {23}{24}}}
\@writefile{nav}{\headcommand {\beamer@documentpages {24}}}
\@writefile{nav}{\headcommand {\gdef \inserttotalframenumber {24}}}
\gdef \@abspage@last{24}
