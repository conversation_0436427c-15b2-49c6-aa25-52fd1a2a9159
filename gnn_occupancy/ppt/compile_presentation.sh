#!/bin/bash

# LaTeX Beamer Presentation Compilation Script
# Compiles the GNN model comparison presentation

echo "🚀 Compiling LaTeX Beamer Presentation..."

# Check if pdflatex is available
if ! command -v pdflatex &> /dev/null; then
    echo "❌ Error: pdflatex not found. Please install LaTeX distribution."
    echo "   Ubuntu/Debian: sudo apt-get install texlive-full"
    echo "   macOS: brew install --cask mactex"
    exit 1
fi

# Create output directory
mkdir -p output

# Compile the presentation (run twice for proper references)
echo "📄 First compilation pass..."
pdflatex -output-directory=output presentation.tex

echo "📄 Second compilation pass..."
pdflatex -output-directory=output presentation.tex

# Check if compilation was successful
if [ -f "output/presentation.pdf" ]; then
    echo "✅ Presentation compiled successfully!"
    echo "📁 Output: output/presentation.pdf"
    
    # Copy to main directory for easy access
    cp output/presentation.pdf ./GNN_Model_Comparison_Presentation.pdf
    echo "📋 Also saved as: GNN_Model_Comparison_Presentation.pdf"
    
    # Display file size
    size=$(du -h GNN_Model_Comparison_Presentation.pdf | cut -f1)
    echo "📊 File size: $size"
    
else
    echo "❌ Compilation failed. Check the LaTeX log for errors."
    echo "📋 Log file: output/presentation.log"
    exit 1
fi

echo "🎉 Done! Your presentation is ready."
