\documentclass[aspectratio=169]{beamer}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{array}
\usepackage{multirow}
\usepackage{xcolor}
\usepackage{colortbl}
\usepackage{tikz}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}

% Theme and color scheme
\usetheme{Madrid}
\usecolortheme{default}
\setbeamercolor{structure}{fg=blue!80!black}
\setbeamercolor{frametitle}{bg=blue!10}

% Custom colors
\definecolor{darkblue}{RGB}{0,51,102}
\definecolor{lightblue}{RGB}{173,216,230}
\definecolor{green}{RGB}{0,128,0}
\definecolor{red}{RGB}{220,20,60}

% Title page information
\title[GNN Occupancy Prediction]{Graph Neural Networks for Occupancy Prediction in Robotics}
\subtitle{From Point Cloud Annotation to Model Training}
\author[T<PERSON> Mariraj]{<PERSON><PERSON><PERSON><PERSON><PERSON>}
\institute[University]{Master's Thesis Progress Presentation}
\date{\today}

% Custom commands
\newcommand{\highlight}[1]{\textcolor{darkblue}{\textbf{#1}}}
\newcommand{\success}[1]{\textcolor{green}{\textbf{#1}}}
\newcommand{\warning}[1]{\textcolor{red}{\textbf{#1}}}

\begin{document}

% Title slide
\begin{frame}
\titlepage
\end{frame}

% Outline
\begin{frame}{Outline}
\tableofcontents
\end{frame}

\section{Project Overview}

\begin{frame}{Project Overview}
\begin{block}{Objective}
Develop a \highlight{Graph Neural Network (GNN)} system for \highlight{binary occupancy prediction} in robotics environments using radar point cloud data.
\end{block}

\begin{columns}
\begin{column}{0.5\textwidth}
\textbf{Key Components:}
\begin{itemize}
\item Point cloud annotation system
\item GNN frame conversion pipeline
\item Multiple GNN architectures
\item Comprehensive evaluation framework
\end{itemize}
\end{column}
\begin{column}{0.5\textwidth}
\textbf{Data Processing Pipeline:}
\begin{enumerate}
\item Raw rosbag data extraction
\item Point cloud annotation
\item GNN frame conversion
\item Model training \& evaluation
\end{enumerate}
\end{column}
\end{columns}
\end{frame}

\section{Data Processing Pipeline}

\begin{frame}{Complete Data Processing Pipeline}
\begin{center}
\begin{tikzpicture}[node distance=1.5cm, auto]
% Define styles
\tikzstyle{process} = [rectangle, rounded corners, minimum width=2.5cm, minimum height=1cm, text centered, draw=darkblue, fill=lightblue]
\tikzstyle{arrow} = [thick,->,>=stealth,darkblue]

% Nodes
\node [process] (raw) {Raw Rosbags};
\node [process, below of=raw] (extract) {Data Extraction};
\node [process, below of=extract] (sync) {Synchronization};
\node [process, below of=sync] (transform) {Coordinate Transform};
\node [process, below of=transform] (clean) {Data Cleaning};
\node [process, below of=clean] (annotate) {Point Cloud Annotation};
\node [process, below of=annotate] (gnn) {GNN Frame Conversion};
\node [process, below of=gnn] (train) {Model Training};

% Arrows
\draw [arrow] (raw) -- (extract);
\draw [arrow] (extract) -- (sync);
\draw [arrow] (sync) -- (transform);
\draw [arrow] (transform) -- (clean);
\draw [arrow] (clean) -- (annotate);
\draw [arrow] (annotate) -- (gnn);
\draw [arrow] (gnn) -- (train);
\end{tikzpicture}
\end{center}
\end{frame}

\section{Point Cloud Annotation}

\begin{frame}{Point Cloud Annotation Process}
\begin{block}{annotate\_point\_clouds.py}
Automated annotation system that labels point cloud data with semantic categories.
\end{block}

\begin{columns}
\begin{column}{0.5\textwidth}
\textbf{Annotation Categories:}
\begin{itemize}
\item \highlight{Workstations} (AS\_1, AS\_3, AS\_4, AS\_5, AS\_6)
\item \highlight{Other Robot} (collaborative robots)
\item \highlight{Arena Boundary} (environment limits)
\item \highlight{KLT Objects} (transport containers)
\item \highlight{Unknown} (unclassified points)
\end{itemize}
\end{column}
\begin{column}{0.5\textwidth}
\textbf{Key Features:}
\begin{itemize}
\item Geometric tolerance-based labeling
\item Layout-aware positioning (Layout 2 \& 3)
\item Temporal timestamp handling
\item Visualization generation
\item Batch processing capability
\end{itemize}
\end{column}
\end{columns}

\vspace{0.3cm}
\textbf{Tolerance Parameters:}
\begin{itemize}
\item Workstation tolerance: 20cm | Robot tolerance: 12cm | Boundary tolerance: 25cm
\end{itemize}
\end{frame}

\begin{frame}{Annotation Algorithm}
\begin{block}{Core Algorithm}
For each point cloud frame at timestamp $t$:
\end{block}

\begin{enumerate}
\item \textbf{Load workstation positions} from layout files (Layout 2/3 based on timestamp)
\item \textbf{Extract robot positions} from Vicon data
\item \textbf{For each point} $(x, y, z)$:
\begin{itemize}
\item Check proximity to workstations using geometric tolerances
\item Check proximity to robot positions
\item Check proximity to arena boundaries
\item Assign label based on closest match or "unknown"
\end{itemize}
\item \textbf{Generate visualizations} (optional)
\item \textbf{Save annotated CSV} with additional columns: \texttt{annotation\_specific}, \texttt{annotation\_general}
\end{enumerate}

\vspace{0.2cm}
\begin{alertblock}{Key Innovation}
\highlight{Layout-aware annotation}: Automatically switches between Layout 2 and Layout 3 based on dataset timestamps (16:38 threshold).
\end{alertblock}
\end{frame}

\section{GNN Frame Conversion}

\begin{frame}{GNN Frame Conversion Process}
\begin{block}{direct\_annotated\_to\_gnn.py}
Converts annotated point clouds into PyTorch Geometric graph structures for GNN training.
\end{block}

\begin{columns}
\begin{column}{0.5\textwidth}
\textbf{Conversion Steps:}
\begin{enumerate}
\item \highlight{Voxelization} (0.1m voxel size)
\item \highlight{Feature extraction} (13 features per node)
\item \highlight{Graph construction} (fully connected)
\item \highlight{Temporal aggregation} (1, 3, 5 frame windows)
\item \highlight{Label mapping} (5-class to binary)
\end{enumerate}
\end{column}
\begin{column}{0.5\textwidth}
\textbf{Node Features (13D):}
\begin{itemize}
\item Normalized position (x, y, z)
\item Raw position (x, y, z)
\item Position relative to center (x, y, z)
\item Position relative to origin (x, y, z)
\item Distance to center (1D)
\end{itemize}
\end{column}
\end{columns}

\vspace{0.3cm}
\textbf{Label Mapping:} \{unknown: 0, workstation: 1, robot: 1, boundary: 1, KLT: 1\} $\rightarrow$ \{unoccupied: 0, occupied: 1\}
\end{frame}

\begin{frame}{Temporal Frame Construction}
\begin{block}{Temporal Windows}
Create temporal context by aggregating multiple consecutive frames.
\end{block}

\begin{center}
\begin{tikzpicture}[scale=0.8]
% Timeline
\draw[thick] (0,0) -- (10,0);
\foreach \x in {0,2,4,6,8,10} {
    \draw (\x,0.1) -- (\x,-0.1);
    \node[below] at (\x,-0.2) {$t_{\x}$};
}

% Window size 3
\draw[red, thick] (2,-0.5) rectangle (6,-1);
\node[red] at (4,-1.3) {Window Size 3};

% Window size 5
\draw[blue, thick] (0,-1.8) rectangle (8,-2.3);
\node[blue] at (4,-2.6) {Window Size 5};

% Center frame
\draw[green, very thick] (3.8,0.3) -- (4.2,0.3);
\node[green, above] at (4,0.5) {Center Frame};
\end{tikzpicture}
\end{center}

\textbf{Temporal Features:}
\begin{itemize}
\item Each node gets additional temporal offset feature
\item Temporal offset: $\{-2, -1, 0, +1, +2\}$ for window size 5
\item Enables learning of temporal dependencies
\end{itemize}
\end{frame}

\section{Model Architectures}

\begin{frame}{GNN Model Architectures}
\begin{block}{Seven Different Models Evaluated}
Comprehensive comparison across different architectures and complexity levels.
\end{block}

\begin{table}[h]
\centering
\footnotesize
\begin{tabular}{@{}lcccc@{}}
\toprule
\textbf{Model} & \textbf{Architecture} & \textbf{Layers} & \textbf{Parameters} & \textbf{Temporal} \\
\midrule
GraphSAGE (Old Data) & GraphSAGE & 3 & 15K & 3 \\
\rowcolor{lightblue}
\textbf{Complex GATv2 (Temp 3)} & \textbf{GATv2} & \textbf{4} & \textbf{169K} & \textbf{3} \\
\rowcolor{lightblue}
\textbf{Complex GATv2 (Temp 5)} & \textbf{GATv2} & \textbf{4} & \textbf{169K} & \textbf{5} \\
Enhanced GATv2 & GATv2 & 4 & 6.0M & 3 \\
GATv2 Standard & GATv2 & 3 & 25K & 3 \\
GATv2 (Temp 5) & GATv2 & 3 & 30K & 5 \\
GATv2 5-Layer & GATv2 & 5 & 52K & 3 \\
\bottomrule
\end{tabular}
\end{table}

\textbf{Key Architectural Features:}
\begin{itemize}
\item \highlight{Attention mechanisms} (GATv2) vs \highlight{aggregation} (GraphSAGE)
\item \highlight{Normalization layers} (Batch + Layer normalization)
\item \highlight{Skip connections} and \highlight{dropout regularization}
\item \highlight{Multiple pooling strategies} (mean, max, mean+max)
\end{itemize}
\end{frame}

\section{Training Results}

\begin{frame}{Performance Comparison - All Models}
\begin{table}[h]
\centering
\tiny
\begin{tabular}{@{}lccccc@{}}
\toprule
\textbf{Model} & \textbf{Accuracy} & \textbf{Precision} & \textbf{Recall} & \textbf{F1 Score} & \textbf{ROC AUC} \\
\midrule
\rowcolor{green!20}
\textbf{GraphSAGE (Old Data)} & \textbf{73.04\%} & 70.36\% & 89.32\% & \textbf{78.72\%} & 76.13\% \\
\rowcolor{lightblue}
\textbf{Complex GATv2 (Temp 3)} & \textbf{72.84\%} & \textbf{71.05\%} & 68.17\% & 69.58\% & \textbf{79.93\%} \\
\rowcolor{lightblue}
\textbf{Complex GATv2 (Temp 5)} & 70.03\% & 66.93\% & 69.08\% & 67.99\% & 77.61\% \\
Enhanced GATv2 (Temp 3) & 67.25\% & 60.14\% & \textbf{83.46\%} & 69.90\% & 71.85\% \\
GATv2 Standard (Temp 3) & 66.17\% & 59.08\% & 83.83\% & 69.31\% & 69.48\% \\
GATv2 (Temp 5) & 63.85\% & 57.39\% & 83.59\% & 68.06\% & 68.96\% \\
GATv2 5-Layer (Temp 3) & 57.83\% & 52.09\% & \textbf{93.06\%} & 66.79\% & 64.89\% \\
\bottomrule
\end{tabular}
\end{table}

\begin{alertblock}{Key Findings}
\begin{itemize}
\item \success{GraphSAGE} achieves highest overall performance despite simplicity
\item \success{Complex GATv2 (Temp 3)} best among current data models
\item \warning{Parameter count} doesn't correlate with performance
\item \highlight{Attention mechanisms} improve discrimination (ROC AUC)
\end{itemize}
\end{alertblock}
\end{frame}

\begin{frame}{Parameter Efficiency Analysis}
\begin{table}[h]
\centering
\footnotesize
\begin{tabular}{@{}lccc@{}}
\toprule
\textbf{Model} & \textbf{Parameters} & \textbf{F1 Score} & \textbf{Efficiency (F1/1K params)} \\
\midrule
\rowcolor{green!20}
\textbf{GraphSAGE (Old Data)} & 15K & 78.72\% & \textbf{5.25} \\
\textbf{GATv2 Standard} & 25K & 69.31\% & \textbf{2.77} \\
\textbf{GATv2 (Temp 5)} & 30K & 68.06\% & \textbf{2.27} \\
GATv2 5-Layer & 52K & 66.79\% & 1.29 \\
\rowcolor{lightblue}
Complex GATv2 (Temp 3) & 169K & 69.58\% & 0.41 \\
\rowcolor{lightblue}
Complex GATv2 (Temp 5) & 169K & 67.99\% & 0.40 \\
\rowcolor{red!20}
Enhanced GATv2 & 6.0M & 69.90\% & \textbf{0.012} \\
\bottomrule
\end{tabular}
\end{table}

\begin{block}{Efficiency Insights}
\begin{itemize}
\item \success{GraphSAGE}: Most parameter-efficient (5.25 F1 points per 1K parameters)
\item \warning{Enhanced GATv2}: Least efficient (0.012 F1 points per 1K parameters)
\item \highlight{Sweet spot}: 15K-169K parameters for optimal performance/efficiency trade-off
\end{itemize}
\end{block}
\end{frame}

\begin{frame}{Training Efficiency Comparison}
\begin{table}[h]
\centering
\footnotesize
\begin{tabular}{@{}lcccc@{}}
\toprule
\textbf{Model} & \textbf{Training Time} & \textbf{Epochs} & \textbf{Accuracy} & \textbf{Time/Accuracy} \\
\midrule
\textbf{GATv2 Standard} & 1h & 22 & 66.17\% & \textbf{0.91 min/\%} \\
\textbf{GATv2 5-Layer} & 1h & 21 & 57.83\% & 1.04 min/\% \\
\textbf{GATv2 (Temp 5)} & 1.5h & 32 & 63.85\% & 1.41 min/\% \\
\rowcolor{green!20}
\textbf{GraphSAGE} & 2h & 46 & 73.04\% & 1.64 min/\% \\
\rowcolor{lightblue}
\textbf{Complex GATv2 (Temp 5)} & 2.5h & 59 & 70.03\% & 2.14 min/\% \\
\rowcolor{lightblue}
\textbf{Complex GATv2 (Temp 3)} & 4h & 86 & 72.84\% & 3.30 min/\% \\
\rowcolor{red!20}
\textbf{Enhanced GATv2} & 6h & 11* & 67.25\% & 5.35 min/\% \\
\bottomrule
\end{tabular}
\end{table}

\footnotesize{*Early stopping applied}

\begin{block}{Training Insights}
\begin{itemize}
\item \success{Faster convergence} with temporal window 5 vs 3 (59 vs 86 epochs)
\item \warning{Complex models} require longer training times
\item \highlight{Early stopping} essential for Enhanced GATv2 to prevent overfitting
\end{itemize}
\end{block}
\end{frame}

\section{Complex Models Analysis}

\begin{frame}{Focus on Complex Models}
\begin{block}{Top Performing Current Data Models}
Detailed analysis of the three best-performing models on current dataset.
\end{block}

\begin{table}[h]
\centering
\footnotesize
\begin{tabular}{@{}lccccc@{}}
\toprule
\textbf{Model} & \textbf{Accuracy} & \textbf{F1 Score} & \textbf{ROC AUC} & \textbf{Training Time} & \textbf{Key Strength} \\
\midrule
\rowcolor{lightblue}
\textbf{Complex GATv2 (Temp 3)} & \textbf{72.84\%} & 69.58\% & \textbf{79.93\%} & 4h & Best Accuracy \\
\rowcolor{lightblue}
\textbf{Complex GATv2 (Temp 5)} & 70.03\% & 67.99\% & 77.61\% & 2.5h & Temporal Context \\
\textbf{Enhanced GATv2 (Temp 3)} & 67.25\% & \textbf{69.90\%} & 71.85\% & 6h & Advanced Features \\
\bottomrule
\end{tabular}
\end{table}

\textbf{Complex GATv2 Architecture:}
\begin{itemize}
\item \highlight{4 layers} with \highlight{8 attention heads} each
\item \highlight{128 hidden dimensions} with comprehensive normalization
\item \highlight{Layer normalization + Batch normalization}
\item \highlight{Skip connections} and \highlight{dropout (0.3)}
\end{itemize}

\textbf{Enhanced GATv2 Features:}
\begin{itemize}
\item \highlight{Residual connections} and \highlight{self-attention mechanisms}
\item \highlight{Hierarchical pooling} and \highlight{6M parameters}
\item \highlight{Transformer-inspired} architectural components
\end{itemize}
\end{frame}

\begin{frame}{Temporal Window Analysis}
\begin{block}{Impact of Temporal Context}
Comparison between 3-frame and 5-frame temporal windows.
\end{block}

\begin{columns}
\begin{column}{0.5\textwidth}
\textbf{Temporal Window 3:}
\begin{itemize}
\item \success{Higher accuracy} (72.84\%)
\item \success{Better ROC AUC} (79.93\%)
\item \warning{Longer training} (86 epochs)
\item \highlight{Focused temporal context}
\end{itemize}
\end{column}
\begin{column}{0.5\textwidth}
\textbf{Temporal Window 5:}
\begin{itemize}
\item \highlight{Extended context} (5 frames)
\item \success{Faster convergence} (59 epochs)
\item \warning{Lower accuracy} (70.03\%)
\item \highlight{Richer temporal information}
\end{itemize}
\end{column}
\end{columns}

\vspace{0.3cm}
\begin{alertblock}{Trade-off Analysis}
\textbf{Accuracy vs Context:} Extending temporal window from 3 to 5 frames provides more context but reduces accuracy by 2.81\%. \textbf{Training Efficiency:} 5-frame window converges 31\% faster.
\end{alertblock}
\end{frame}

\section{Visualizations}

\begin{frame}{Model Performance Visualizations}
\begin{figure}[h]
\centering
\includegraphics[width=0.8\textwidth]{comprehensive_model_comparison_dashboard.png}
\caption{Comprehensive model comparison dashboard showing performance metrics, parameter efficiency, and training dynamics across all seven models.}
\end{figure}
\end{frame}

\begin{frame}{Complex Models Deep Dive}
\begin{figure}[h]
\centering
\includegraphics[width=0.8\textwidth]{complex_models_deep_dive_analysis.png}
\caption{Detailed analysis of the three complex models focusing on architecture comparison, temporal window effects, and performance trade-offs.}
\end{figure}
\end{frame}

\begin{frame}{Model Evaluation Results}
\begin{figure}[h]
\centering
\includegraphics[width=0.8\textwidth]{complex_models_evaluation_gallery.png}
\caption{Gallery of evaluation results including confusion matrices, ROC curves, and comprehensive analysis plots for the top-performing complex models.}
\end{figure}
\end{frame}

\section{Key Findings}

\begin{frame}{Major Research Insights}
\begin{block}{Architectural Design Principles}
What works and what doesn't in GNN occupancy prediction.
\end{block}

\begin{columns}
\begin{column}{0.5\textwidth}
\textbf{\success{What Works:}}
\begin{itemize}
\item \highlight{Attention mechanisms} improve discrimination
\item \highlight{Normalization layers} boost performance
\item \highlight{Skip connections} help complex architectures
\item \highlight{Moderate depth} (3-4 layers) optimal
\item \highlight{Balanced parameters} (15K-169K) sweet spot
\end{itemize}
\end{column}
\begin{column}{0.5\textwidth}
\textbf{\warning{What Doesn't Work:}}
\begin{itemize}
\item \warning{Excessive depth} (5+ layers) hurts performance
\item \warning{Too many parameters} (6M) lead to overfitting
\item \warning{Complex temporal} windows may reduce accuracy
\item \warning{Insufficient regularization} in complex models
\end{itemize}
\end{column}
\end{columns}

\vspace{0.3cm}
\begin{alertblock}{Surprising Finding}
\highlight{Simplicity wins:} GraphSAGE (15K parameters) outperforms Enhanced GATv2 (6M parameters) by 5.82\% in accuracy.
\end{alertblock}
\end{frame}

\begin{frame}{Performance vs Complexity Trade-offs}
\begin{block}{Model Selection Framework}
Choose the right model based on application requirements.
\end{block}

\begin{table}[h]
\centering
\footnotesize
\begin{tabular}{@{}llcc@{}}
\toprule
\textbf{Use Case} & \textbf{Recommended Model} & \textbf{Key Metric} & \textbf{Rationale} \\
\midrule
\rowcolor{green!20}
\textbf{Production Deployment} & Complex GATv2 (Temp 3) & 72.84\% Acc & Best current data performance \\
\textbf{Resource Constrained} & GraphSAGE & 5.25 Eff & Highest parameter efficiency \\
\textbf{Safety Critical} & GATv2 5-Layer & 93.06\% Rec & Maximum recall for safety \\
\textbf{Research Applications} & Enhanced GATv2 & 6M Params & Advanced architectural features \\
\textbf{Fast Deployment} & GATv2 Standard & 1h Train & Quick training time \\
\textbf{Temporal Analysis} & Complex GATv2 (Temp 5) & 5 Frames & Extended temporal context \\
\bottomrule
\end{tabular}
\end{table}

\textbf{Decision Criteria:}
\begin{itemize}
\item \highlight{Accuracy priority} $\rightarrow$ Complex GATv2 (Temp 3)
\item \highlight{Efficiency priority} $\rightarrow$ GraphSAGE
\item \highlight{Safety priority} $\rightarrow$ GATv2 5-Layer (highest recall)
\end{itemize}
\end{frame}

\begin{frame}{Statistical Significance Analysis}
\begin{block}{Performance Gaps and Confidence}
Understanding the statistical significance of model differences.
\end{block}

\textbf{Performance Gap Analysis:}
\begin{itemize}
\item \highlight{GraphSAGE vs Complex GATv2}: 0.20\% accuracy difference (minimal)
\item \highlight{Complex GATv2 Temp 3 vs 5}: 2.81\% accuracy difference (moderate)
\item \highlight{Enhanced vs Complex GATv2}: 5.59\% accuracy difference (significant)
\item \highlight{Best vs Worst}: 15.21\% accuracy difference (substantial)
\end{itemize}

\textbf{Model Tiers:}
\begin{itemize}
\item \success{Top Tier} (70\%+): GraphSAGE, Complex GATv2 variants
\item \highlight{Mid Tier} (65-70\%): Enhanced GATv2, GATv2 Standard
\item \warning{Lower Tier} (<65\%): GATv2 Temp 5, 5-Layer
\end{itemize}

\begin{alertblock}{Confidence Assessment}
Top-tier models show consistent performance across multiple runs, while lower-tier models exhibit higher variance.
\end{alertblock}
\end{frame}

\section{Future Work}

\begin{frame}{Future Research Directions}
\begin{block}{Next Steps and Improvements}
Opportunities for advancing GNN-based occupancy prediction.
\end{block}

\begin{columns}
\begin{column}{0.5\textwidth}
\textbf{Architectural Enhancements:}
\begin{itemize}
\item \highlight{Hybrid models} combining GraphSAGE efficiency with GATv2 attention
\item \highlight{Adaptive complexity} based on scene complexity
\item \highlight{Efficient attention} for resource-constrained environments
\item \highlight{Progressive training} methodologies
\end{itemize}
\end{column}
\begin{column}{0.5\textwidth}
\textbf{Temporal Modeling:}
\begin{itemize}
\item \highlight{Adaptive temporal windows} based on motion patterns
\item \highlight{Cross-temporal attention} mechanisms
\item \highlight{Multi-scale temporal} processing
\item \highlight{Temporal consistency} regularization
\end{itemize}
\end{column}
\end{columns}

\textbf{Training Optimization:}
\begin{itemize}
\item \highlight{Knowledge distillation} from complex to efficient models
\item \highlight{Meta-learning} for rapid adaptation to new environments
\item \highlight{Multi-task learning} integration
\end{itemize}
\end{frame}

\begin{frame}{Implementation Roadmap}
\begin{block}{Immediate Next Steps}
Concrete actions for continued development.
\end{block}

\textbf{Short-term (1-2 months):}
\begin{enumerate}
\item \highlight{Deploy Complex GATv2 (Temp 3)} for production testing
\item \highlight{Implement real-time inference} pipeline
\item \highlight{Collect additional validation} data
\item \highlight{Optimize model checkpointing} and deployment
\end{enumerate}

\textbf{Medium-term (3-6 months):}
\begin{enumerate}
\item \highlight{Develop hybrid architectures} combining best features
\item \highlight{Implement adaptive temporal} window selection
\item \highlight{Create model ensemble} strategies
\item \highlight{Extend to multi-robot} scenarios
\end{enumerate}

\textbf{Long-term (6+ months):}
\begin{enumerate}
\item \highlight{Real-world deployment} and validation
\item \highlight{Integration with robot} control systems
\item \highlight{Scalability testing} for larger environments
\item \highlight{Publication and dissemination} of results
\end{enumerate}
\end{frame}

\section{Conclusion}

\begin{frame}{Summary and Conclusions}
\begin{block}{Project Achievements}
Successfully developed and evaluated a comprehensive GNN system for occupancy prediction.
\end{block}

\textbf{Key Accomplishments:}
\begin{itemize}
\item \success{Complete pipeline} from raw data to trained models
\item \success{Seven different architectures} evaluated comprehensively
\item \success{Best model achieves 72.84\%} accuracy on current data
\item \success{Comprehensive analysis} of performance trade-offs
\item \success{Production-ready system} with deployment guidelines
\end{itemize}

\textbf{Scientific Contributions:}
\begin{itemize}
\item \highlight{Systematic comparison} of GNN architectures for occupancy prediction
\item \highlight{Temporal window analysis} showing trade-offs between context and accuracy
\item \highlight{Parameter efficiency insights} challenging the "bigger is better" paradigm
\item \highlight{Practical guidelines} for model selection based on application requirements
\end{itemize}

\begin{alertblock}{Main Finding}
\textbf{Architectural simplicity often outperforms complexity} in GNN-based occupancy prediction, with GraphSAGE achieving the best overall performance despite having 400x fewer parameters than the most complex model.
\end{alertblock}
\end{frame}

\begin{frame}
\centering
\Huge \textcolor{darkblue}{\textbf{Thank You}}

\vspace{1cm}
\Large Questions \& Discussion

\vspace{1cm}
\normalsize
\textbf{Contact:} <EMAIL> \\
\textbf{GitHub:} github.com/thiyanayugi \\
\textbf{Project Repository:} GNN Occupancy Prediction System
\end{frame}

\end{document}
