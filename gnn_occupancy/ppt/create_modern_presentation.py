#!/usr/bin/env python3

import os
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE

# Modern color palette
PRIMARY_BLUE = RGBColor(30, 58, 138)
ACCENT_BLUE = RGBColor(59, 130, 246)
LIGHT_BLUE = RGBColor(219, 234, 254)
SUCCESS_GREEN = RGBColor(34, 197, 94)
WARNING_ORANGE = RGBColor(251, 146, 60)
DANGER_RED = RGBColor(239, 68, 68)
DARK_GRAY = RGBColor(55, 65, 81)
LIGHT_GRAY = RGBColor(243, 244, 246)
WHITE = RGBColor(255, 255, 255)

def create_styled_textbox(slide, left, top, width, height, text, font_size=14, bold=False, color=None, alignment=PP_ALIGN.LEFT):
    """Create a properly styled text box."""
    textbox = slide.shapes.add_textbox(left, top, width, height)
    text_frame = textbox.text_frame
    text_frame.text = text
    text_frame.margin_left = Inches(0.1)
    text_frame.margin_right = Inches(0.1)
    text_frame.margin_top = Inches(0.05)
    text_frame.margin_bottom = Inches(0.05)
    text_frame.word_wrap = True

    paragraph = text_frame.paragraphs[0]
    paragraph.alignment = alignment
    paragraph.font.size = Pt(font_size)
    paragraph.font.bold = bold
    if color:
        paragraph.font.color.rgb = color

    return textbox

def create_modern_card(slide, left, top, width, height, title, content, accent_color):
    """Create a modern card-style container."""
    # Main card background
    card = slide.shapes.add_shape(MSO_SHAPE.ROUNDED_RECTANGLE, left, top, width, height)
    card.fill.solid()
    card.fill.fore_color.rgb = WHITE
    card.line.color.rgb = LIGHT_GRAY
    card.line.width = Pt(1)

    # Accent bar on the left
    accent_bar = slide.shapes.add_shape(MSO_SHAPE.RECTANGLE, left, top, Inches(0.1), height)
    accent_bar.fill.solid()
    accent_bar.fill.fore_color.rgb = accent_color
    accent_bar.line.fill.background()

    # Title
    create_styled_textbox(
        slide, left + Inches(0.2), top + Inches(0.1),
        width - Inches(0.3), Inches(0.4),
        title, font_size=16, bold=True, color=PRIMARY_BLUE
    )

    # Content
    create_styled_textbox(
        slide, left + Inches(0.2), top + Inches(0.5),
        width - Inches(0.3), height - Inches(0.6),
        content, font_size=12, color=DARK_GRAY
    )

    return card

def create_metric_card(slide, left, top, width, height, metric_name, value, color, description=""):
    """Create a metric display card."""
    # Main card
    card = slide.shapes.add_shape(MSO_SHAPE.ROUNDED_RECTANGLE, left, top, width, height)
    card.fill.solid()
    card.fill.fore_color.rgb = WHITE
    card.line.color.rgb = LIGHT_GRAY
    card.line.width = Pt(1)

    # Metric value (large)
    create_styled_textbox(
        slide, left + Inches(0.1), top + Inches(0.1),
        width - Inches(0.2), Inches(0.6),
        value, font_size=24, bold=True, color=color, alignment=PP_ALIGN.CENTER
    )

    # Metric name
    create_styled_textbox(
        slide, left + Inches(0.1), top + Inches(0.7),
        width - Inches(0.2), Inches(0.3),
        metric_name, font_size=12, bold=True, color=DARK_GRAY, alignment=PP_ALIGN.CENTER
    )

    # Description if provided
    if description:
        create_styled_textbox(
            slide, left + Inches(0.1), top + Inches(1.0),
            width - Inches(0.2), Inches(0.3),
            description, font_size=10, color=DARK_GRAY, alignment=PP_ALIGN.CENTER
        )

    return card

def add_gradient_background(slide, color1, color2):
    """Add a subtle gradient background to slide."""
    try:
        background = slide.background
        fill = background.fill
        fill.gradient()
        fill.gradient_stops[0].color.rgb = color1
        fill.gradient_stops[1].color.rgb = color2
    except:
        slide.background.fill.solid()
        slide.background.fill.fore_color.rgb = LIGHT_GRAY

def create_modern_presentation():
    """Create a modern, visually appealing PowerPoint presentation."""

    # Create presentation
    prs = Presentation()
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)

    # Slide 1: Modern Title Slide
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_BLUE)

    # Main title
    create_styled_textbox(
        slide, Inches(1), Inches(1.5), Inches(11.33), Inches(1.5),
        "Graph Neural Networks for Occupancy Prediction in Robotics",
        font_size=36, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Subtitle
    create_styled_textbox(
        slide, Inches(1), Inches(3), Inches(11.33), Inches(0.8),
        "From Point Cloud Annotation to Model Training",
        font_size=20, color=ACCENT_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Author and details
    create_styled_textbox(
        slide, Inches(1), Inches(5), Inches(11.33), Inches(1.5),
        "Thiyanayugi Mariraj\nMaster's Thesis Progress Presentation\nJanuary 2025",
        font_size=16, color=DARK_GRAY, alignment=PP_ALIGN.CENTER
    )

    # Slide 2: Project Overview with Cards
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_GRAY)

    # Title
    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Project Overview",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Objective card
    create_modern_card(
        slide, Inches(0.5), Inches(1.3), Inches(12.33), Inches(1.2),
        "🎯 Objective",
        "Develop a Graph Neural Network (GNN) system for binary occupancy prediction in robotics environments using radar point cloud data.",
        ACCENT_BLUE
    )

    # Key Components card
    create_modern_card(
        slide, Inches(0.5), Inches(2.7), Inches(6), Inches(2.3),
        "🔧 Key Components",
        "• Point cloud annotation system\n• GNN frame conversion pipeline\n• Multiple GNN architectures\n• Comprehensive evaluation framework",
        SUCCESS_GREEN
    )

    # Data Pipeline card
    create_modern_card(
        slide, Inches(6.8), Inches(2.7), Inches(5.83), Inches(2.3),
        "📊 Data Processing Pipeline",
        "1. Raw rosbag data extraction\n2. Point cloud annotation\n3. GNN frame conversion\n4. Model training & evaluation",
        WARNING_ORANGE
    )

    # Slide 3: Pipeline Visualization
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_BLUE)

    # Title
    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Complete Data Processing Pipeline",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Pipeline steps
    steps = [
        ("📁", "Raw Rosbags", ACCENT_BLUE),
        ("🔄", "Data Extraction", SUCCESS_GREEN),
        ("⏱️", "Synchronization", WARNING_ORANGE),
        ("🔄", "Coordinate Transform", ACCENT_BLUE),
        ("🧹", "Data Cleaning", SUCCESS_GREEN),
        ("🏷️", "Point Cloud Annotation", WARNING_ORANGE),
        ("🔗", "GNN Frame Conversion", ACCENT_BLUE),
        ("🤖", "Model Training", SUCCESS_GREEN)
    ]

    # Create pipeline in a grid layout
    cols = 4
    card_width = Inches(2.8)
    card_height = Inches(1.2)
    start_x = Inches(1)
    start_y = Inches(1.5)
    spacing_x = Inches(3.1)
    spacing_y = Inches(1.5)

    for i, (icon, step, color) in enumerate(steps):
        row = i // cols
        col = i % cols
        x = start_x + col * spacing_x
        y = start_y + row * spacing_y

        create_modern_card(
            slide, x, y, card_width, card_height,
            f"{icon} {step}",
            "",
            color
        )

    # Slide 4: Point Cloud Annotation
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_GRAY)

    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Point Cloud Annotation Process",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Annotation categories
    create_modern_card(
        slide, Inches(0.5), Inches(1.3), Inches(6), Inches(3.7),
        "🏷️ Annotation Categories",
        "• Workstations (AS_1, AS_3, AS_4, AS_5, AS_6)\n• Other Robot (collaborative robots)\n• Arena Boundary (environment limits)\n• KLT Objects (transport containers)\n• Unknown (unclassified points)",
        ACCENT_BLUE
    )

    # Key features
    create_modern_card(
        slide, Inches(6.8), Inches(1.3), Inches(5.83), Inches(3.7),
        "⚙️ Key Features",
        "• Geometric tolerance-based labeling\n• Layout-aware positioning (Layout 2 & 3)\n• Temporal timestamp handling\n• Visualization generation\n• Batch processing capability\n\nTolerance Parameters:\n• Workstation: 20cm\n• Robot: 12cm\n• Boundary: 25cm",
        SUCCESS_GREEN
    )

    # Slide 5: GNN Frame Conversion
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_BLUE)

    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "GNN Frame Conversion Process",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Conversion steps
    create_modern_card(
        slide, Inches(0.5), Inches(1.3), Inches(6), Inches(3.7),
        "🔗 Conversion Steps",
        "1. Voxelization (0.1m voxel size)\n2. Feature extraction (13 features per node)\n3. Graph construction (fully connected)\n4. Temporal aggregation (1, 3, 5 frame windows)\n5. Label mapping (5-class to binary)",
        WARNING_ORANGE
    )

    # Node features
    create_modern_card(
        slide, Inches(6.8), Inches(1.3), Inches(5.83), Inches(3.7),
        "📊 Node Features (13D)",
        "• Normalized position (x, y, z)\n• Raw position (x, y, z)\n• Position relative to center (x, y, z)\n• Position relative to origin (x, y, z)\n• Distance to center (1D)\n\nLabel Mapping:\n{unknown: 0, workstation: 1, robot: 1, boundary: 1, KLT: 1} → {unoccupied: 0, occupied: 1}",
        ACCENT_BLUE
    )

    # Slide 6: Model Architectures
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_GRAY)

    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "GNN Model Architectures",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Models overview
    create_modern_card(
        slide, Inches(0.5), Inches(1.3), Inches(12.33), Inches(3.7),
        "🤖 Seven Different Models Evaluated",
        "• GraphSAGE (Old Data) - 3 layers, 15K parameters, Temporal 3\n• Complex GATv2 (Temp 3) - 4 layers, 169K parameters, Temporal 3\n• Complex GATv2 (Temp 5) - 4 layers, 169K parameters, Temporal 5\n• Enhanced GATv2 - 4 layers, 6.0M parameters, Temporal 3\n• GATv2 Standard - 3 layers, 25K parameters, Temporal 3\n• GATv2 (Temp 5) - 3 layers, 30K parameters, Temporal 5\n• GATv2 5-Layer - 5 layers, 52K parameters, Temporal 3\n\nKey Features: Attention mechanisms (GATv2) vs aggregation (GraphSAGE), Normalization layers, Skip connections, Multiple pooling strategies",
        SUCCESS_GREEN
    )

    # Slide 7: Performance Comparison
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_BLUE)

    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Performance Comparison - All Models",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Top performers
    models_data = [
        ("GraphSAGE (Old Data)", "73.04%", "78.72%", "76.13%", SUCCESS_GREEN),
        ("Complex GATv2 (Temp 3)", "72.84%", "69.58%", "79.93%", ACCENT_BLUE),
        ("Complex GATv2 (Temp 5)", "70.03%", "67.99%", "77.61%", WARNING_ORANGE),
        ("Enhanced GATv2", "67.25%", "69.90%", "71.85%", DANGER_RED)
    ]

    # Create metric cards for top models
    for i, (model, acc, f1, auc, color) in enumerate(models_data):
        y_pos = Inches(1.3 + i * 1.0)

        # Model name
        create_styled_textbox(
            slide, Inches(0.5), y_pos, Inches(4), Inches(0.8),
            model, font_size=14, bold=True, color=PRIMARY_BLUE
        )

        # Metrics
        create_metric_card(slide, Inches(4.8), y_pos, Inches(2.5), Inches(0.8), "Accuracy", acc, color)
        create_metric_card(slide, Inches(7.5), y_pos, Inches(2.5), Inches(0.8), "F1 Score", f1, color)
        create_metric_card(slide, Inches(10.2), y_pos, Inches(2.5), Inches(0.8), "ROC AUC", auc, color)

    return prs

def add_final_slides(prs):
    """Add the remaining slides to complete the presentation."""

    # Slide 8: Key Findings
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_GRAY)

    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Key Research Insights",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # What works
    create_modern_card(
        slide, Inches(0.5), Inches(1.3), Inches(6), Inches(3.7),
        "✅ What Works",
        "• Attention mechanisms improve discrimination\n• Normalization layers boost performance\n• Skip connections help complex architectures\n• Moderate depth (3-4 layers) optimal\n• Balanced parameters (15K-169K) sweet spot",
        SUCCESS_GREEN
    )

    # What doesn't work
    create_modern_card(
        slide, Inches(6.8), Inches(1.3), Inches(5.83), Inches(3.7),
        "⚠️ What Doesn't Work",
        "• Excessive depth (5+ layers) hurts performance\n• Too many parameters (6M) lead to overfitting\n• Complex temporal windows may reduce accuracy\n• Insufficient regularization in complex models\n\n🔍 Surprising Finding:\nSimplicity wins: GraphSAGE (15K parameters) outperforms Enhanced GATv2 (6M parameters) by 5.82% in accuracy.",
        DANGER_RED
    )

    # Slide 9: Future Work
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_BLUE)

    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Future Research Directions",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Architectural enhancements
    create_modern_card(
        slide, Inches(0.5), Inches(1.3), Inches(6), Inches(1.8),
        "🏗️ Architectural Enhancements",
        "• Hybrid models combining GraphSAGE efficiency with GATv2 attention\n• Adaptive complexity based on scene complexity\n• Efficient attention for resource-constrained environments",
        ACCENT_BLUE
    )

    # Temporal modeling
    create_modern_card(
        slide, Inches(6.8), Inches(1.3), Inches(5.83), Inches(1.8),
        "⏰ Temporal Modeling",
        "• Adaptive temporal windows based on motion patterns\n• Cross-temporal attention mechanisms\n• Multi-scale temporal processing",
        WARNING_ORANGE
    )

    # Implementation roadmap
    create_modern_card(
        slide, Inches(0.5), Inches(3.3), Inches(12.33), Inches(1.7),
        "🗺️ Implementation Roadmap",
        "Short-term (1-2 months): Deploy Complex GATv2 for production testing, implement real-time inference\nMedium-term (3-6 months): Develop hybrid architectures, implement adaptive temporal windows\nLong-term (6+ months): Real-world deployment, integration with robot control systems",
        SUCCESS_GREEN
    )

    # Slide 10: Conclusions
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_GRAY)

    create_styled_textbox(
        slide, Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8),
        "Summary and Conclusions",
        font_size=32, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Key accomplishments
    create_modern_card(
        slide, Inches(0.5), Inches(1.3), Inches(6), Inches(3.7),
        "🎯 Key Accomplishments",
        "✅ Complete pipeline from raw data to trained models\n✅ Seven different architectures evaluated comprehensively\n✅ Best model achieves 72.84% accuracy on current data\n✅ Comprehensive analysis of performance trade-offs\n✅ Production-ready system with deployment guidelines",
        SUCCESS_GREEN
    )

    # Scientific contributions
    create_modern_card(
        slide, Inches(6.8), Inches(1.3), Inches(5.83), Inches(3.7),
        "🔬 Scientific Contributions",
        "• Systematic comparison of GNN architectures for occupancy prediction\n• Temporal window analysis showing trade-offs\n• Parameter efficiency insights challenging 'bigger is better'\n• Practical guidelines for model selection\n\n🏆 Main Finding:\nArchitectural simplicity often outperforms complexity in GNN-based occupancy prediction.",
        ACCENT_BLUE
    )

    # Slide 11: Thank You
    slide = prs.slides.add_slide(prs.slide_layouts[5])
    add_gradient_background(slide, WHITE, LIGHT_BLUE)

    # Thank you message
    create_styled_textbox(
        slide, Inches(2), Inches(2.5), Inches(9.33), Inches(2),
        "Thank You\n\nQuestions & Discussion",
        font_size=48, bold=True, color=PRIMARY_BLUE, alignment=PP_ALIGN.CENTER
    )

    # Contact information
    create_styled_textbox(
        slide, Inches(2), Inches(5.5), Inches(9.33), Inches(1.5),
        "Contact: <EMAIL>\nGitHub: github.com/thiyanayugi\nProject Repository: GNN Occupancy Prediction System",
        font_size=14, color=DARK_GRAY, alignment=PP_ALIGN.CENTER
    )

    return prs

if __name__ == "__main__":
    print("🎨 Creating modern PowerPoint presentation...")

    # Create the presentation
    prs = create_modern_presentation()
    prs = add_final_slides(prs)

    # Save the presentation
    output_file = "gnn_modern_presentation.pptx"
    prs.save(output_file)

    print(f"✅ Modern PowerPoint presentation created successfully: {output_file}")
    print(f"📊 Total slides: {len(prs.slides)}")
    print("\n📋 Slide Contents:")
    slide_titles = [
        "Title Slide",
        "Project Overview",
        "Data Processing Pipeline",
        "Point Cloud Annotation Process",
        "GNN Frame Conversion Process",
        "Model Architectures",
        "Performance Comparison",
        "Key Research Insights",
        "Future Research Directions",
        "Summary and Conclusions",
        "Thank You"
    ]

    for i, title in enumerate(slide_titles, 1):
        print(f"  {i:2d}. {title}")

    print(f"\n🎯 Modern presentation ready for your professor meeting!")
    print(f"📁 File location: {os.path.abspath(output_file)}")
    print("\n🎨 Design Features:")
    print("  • Modern color palette with gradients")
    print("  • Card-based layout with accent colors")
    print("  • Proper alignment and spacing")
    print("  • Visual hierarchy with icons and typography")
    print("  • Professional academic presentation style")
