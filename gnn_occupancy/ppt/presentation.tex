\documentclass[aspectratio=169]{beamer}
\usetheme{Madrid}
\usecolortheme{default}

% Packages
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{array}
\usepackage{multirow}
\usepackage{adjustbox}

% Title page information
\title{GNN Models for Occupancy Prediction}
\subtitle{Comprehensive Performance Analysis}
\author{<PERSON><PERSON><PERSON><PERSON><PERSON>}
\institute{University}
\date{January 25, 2025}

\begin{document}

% Title slide
\begin{frame}
\titlepage
\begin{center}
\small
7 Different Architectures Evaluated \\
Current vs Historical Data Training \\
Comprehensive Metrics Analysis
\end{center}
\end{frame}

% Model Overview
\begin{frame}{Model Overview}
\begin{block}{Models Evaluated}
\textbf{Current Data Models (6):}
\begin{itemize}
    \item Complex GATv2 (Temporal 3 \& 5)
    \item Enhanced GATv2 (Temporal 3)
    \item GATv2 Standard (Temporal 3)
    \item GATv2 (Temporal 5)
    \item GATv2 5-Layer (Temporal 3)
\end{itemize}

\textbf{Historical Data Models (1):}
\begin{itemize}
    \item GraphSAGE (Old Data)
\end{itemize}
\end{block}
\end{frame}

% Performance Rankings - Current Data
\begin{frame}{Performance Rankings - Current Data}
\begin{table}
\centering
\footnotesize
\begin{tabular}{clcccc}
\toprule
\textbf{Rank} & \textbf{Model} & \textbf{Accuracy} & \textbf{F1 Score} & \textbf{ROC AUC} & \textbf{Parameters} \\
\midrule
1 & \textbf{Complex GATv2 (Temp 3)} & \textbf{72.84\%} & 69.58\% & \textbf{79.93\%} & 169K \\
2 & \textbf{Complex GATv2 (Temp 5)} & \textbf{70.03\%} & 67.99\% & 77.61\% & 169K \\
3 & Enhanced GATv2 (Temp 3) & 67.25\% & \textbf{69.90\%} & 71.85\% & 6.0M \\
4 & GATv2 Standard (Temp 3) & 66.17\% & 69.31\% & 69.48\% & 25K \\
5 & GATv2 (Temp 5) & 63.85\% & 68.06\% & 68.96\% & 30K \\
6 & GATv2 5-Layer (Temp 3) & 57.83\% & 66.79\% & 64.89\% & 52K \\
\bottomrule
\end{tabular}
\end{table}
\end{frame}

% Complete Performance Comparison
\begin{frame}{Complete Performance Comparison}
\begin{table}
\centering
\tiny
\begin{tabular}{lccccc}
\toprule
\textbf{Model} & \textbf{Accuracy} & \textbf{Precision} & \textbf{Recall} & \textbf{F1 Score} & \textbf{ROC AUC} \\
\midrule
\textbf{GraphSAGE (Old Data)} & \textbf{73.04\%} & \textbf{70.36\%} & 89.32\% & \textbf{78.72\%} & 76.13\% \\
\textbf{Complex GATv2 (Temp 3)} & \textbf{72.84\%} & \textbf{71.05\%} & 68.17\% & 69.58\% & \textbf{79.93\%} \\
\textbf{Complex GATv2 (Temp 5)} & 70.03\% & 66.93\% & 69.08\% & 67.99\% & 77.61\% \\
Enhanced GATv2 (Temp 3) & 67.25\% & 60.14\% & \textbf{83.46\%} & 69.90\% & 71.85\% \\
GATv2 Standard (Temp 3) & 66.17\% & 59.08\% & 83.83\% & 69.31\% & 69.48\% \\
GATv2 (Temp 5) & 63.85\% & 57.39\% & 83.59\% & 68.06\% & 68.96\% \\
GATv2 5-Layer (Temp 3) & 57.83\% & 52.09\% & \textbf{93.06\%} & 66.79\% & 64.89\% \\
\bottomrule
\end{tabular}
\end{table}
\end{frame}

% Architecture Details
\begin{frame}{Architecture Details}
\begin{table}
\centering
\footnotesize
\begin{tabular}{lcccc}
\toprule
\textbf{Model} & \textbf{Layers} & \textbf{Hidden} & \textbf{Heads} & \textbf{Special Features} \\
\midrule
\textbf{Complex GATv2 (Temp 3)} & 4 & 128 & 8 & Layer+Batch Norm \\
\textbf{Complex GATv2 (Temp 5)} & 4 & 128 & 8 & Layer+Batch Norm \\
\textbf{Enhanced GATv2} & 4 & 192 & 8 & Residual, Self-Attention \\
\textbf{GATv2 Standard} & 3 & 64 & 4 & Standard GATv2 \\
\textbf{GATv2 (Temp 5)} & 3 & 64 & 4 & Extended Temporal \\
\textbf{GATv2 5-Layer} & 5 & 64 & 4 & Deep Architecture \\
\textbf{GraphSAGE} & 3 & 64 & - & Simple Aggregation \\
\bottomrule
\end{tabular}
\end{table}
\end{frame}

% Complex GATv2 (Temp 3) - Evaluation Results
\begin{frame}{Complex GATv2 (Temp 3) - Evaluation Results}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{figure}
\centering
\includegraphics[width=\textwidth]{checkpoints_gatv2_complex_4layers_temp3/visualizations_gatv2_complex_4layers_temp3/confusion_matrix_temporal_3.png}
\caption{Confusion Matrix}
\end{figure}
\end{column}
\begin{column}{0.5\textwidth}
\begin{figure}
\centering
\includegraphics[width=\textwidth]{checkpoints_gatv2_complex_4layers_temp3/visualizations_gatv2_complex_4layers_temp3/roc_curve_temporal_3.png}
\caption{ROC Curve (AUC = 0.7993)}
\end{figure}
\end{column}
\end{columns}
\end{frame}

% Complex GATv2 (Temp 3) - Performance Summary
\begin{frame}{Complex GATv2 (Temp 3) - Performance Summary}
\begin{block}{Key Metrics}
\begin{itemize}
    \item \textbf{Accuracy}: 72.84\% (Best among current data models)
    \item \textbf{Precision}: 71.05\% (Highest precision)
    \item \textbf{ROC AUC}: 79.93\% (Best discrimination)
    \item \textbf{Parameters}: 169,601
    \item \textbf{Training}: 86 epochs, 4 hours
\end{itemize}
\end{block}
\begin{block}{Architecture Features}
\begin{itemize}
    \item 4-layer GATv2 with 8 attention heads
    \item Layer normalization + Batch normalization
    \item Skip connections and dropout (0.3)
    \item 128 hidden dimensions
\end{itemize}
\end{block}
\end{frame}

% Complex GATv2 (Temp 5) - Evaluation Results
\begin{frame}{Complex GATv2 (Temp 5) - Evaluation Results}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{figure}
\centering
\includegraphics[width=\textwidth]{checkpoints_gatv2_complex_temp5/visualizations_gatv2_complex_temp5/complex_gatv2_confusion_matrix_temporal_5.png}
\caption{Confusion Matrix}
\end{figure}
\end{column}
\begin{column}{0.5\textwidth}
\begin{figure}
\centering
\includegraphics[width=\textwidth]{checkpoints_gatv2_complex_temp5/visualizations_gatv2_complex_temp5/complex_gatv2_roc_curve_temporal_5.png}
\caption{ROC Curve (AUC = 0.7761)}
\end{figure}
\end{column}
\end{columns}
\end{frame}

% Complex GATv2 (Temp 5) - Performance Summary
\begin{frame}{Complex GATv2 (Temp 5) - Performance Summary}
\begin{block}{Key Metrics}
\begin{itemize}
    \item \textbf{Accuracy}: 70.03\% (Third best overall)
    \item \textbf{F1 Score}: 67.99\% (Competitive performance)
    \item \textbf{ROC AUC}: 77.61\% (Strong discrimination)
    \item \textbf{Parameters}: 169,601 (Same as Temp 3)
    \item \textbf{Training}: 59 epochs, 2.5 hours (31\% faster)
\end{itemize}
\end{block}
\begin{block}{Temporal Analysis Insight}
\begin{itemize}
    \item Extended temporal window (5 vs 3 frames)
    \item Trade-off: -2.81\% accuracy for richer context
    \item Faster convergence despite longer temporal window
\end{itemize}
\end{block}
\end{frame}

% Enhanced GATv2 - Evaluation Results
\begin{frame}{Enhanced GATv2 - Evaluation Results}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{figure}
\centering
\includegraphics[width=\textwidth]{checkpoints_enhanced_temp3/visualizations_enhanced_temp3/enhanced_confusion_matrix_temporal_3.png}
\caption{Confusion Matrix}
\end{figure}
\end{column}
\begin{column}{0.5\textwidth}
\begin{figure}
\centering
\includegraphics[width=\textwidth]{checkpoints_enhanced_temp3/visualizations_enhanced_temp3/enhanced_roc_curve_temporal_3.png}
\caption{ROC Curve (AUC = 0.7185)}
\end{figure}
\end{column}
\end{columns}
\end{frame}

% Enhanced GATv2 - Performance Summary
\begin{frame}{Enhanced GATv2 - Performance Summary}
\begin{block}{Key Metrics}
\begin{itemize}
    \item \textbf{Recall}: 83.46\% (Highest among all models)
    \item \textbf{F1 Score}: 69.90\% (Second best F1)
    \item \textbf{Accuracy}: 67.25\% (Moderate performance)
    \item \textbf{Parameters}: 6,045,313 (6M - Most complex)
    \item \textbf{Training}: 11 epochs optimal, early stopped at 36
\end{itemize}
\end{block}
\begin{block}{Advanced Architecture}
\begin{itemize}
    \item Residual connections + Self-attention
    \item Hierarchical pooling + Transformer components
    \item 192 hidden dimensions, 8 attention heads
    \item Prone to overfitting despite regularization
\end{itemize}
\end{block}
\end{frame}

% Parameter Efficiency Analysis
\begin{frame}{Parameter Efficiency Analysis}
\begin{table}
\centering
\footnotesize
\begin{tabular}{lccc}
\toprule
\textbf{Model} & \textbf{Parameters} & \textbf{F1 Score} & \textbf{Efficiency (F1/1K)} \\
\midrule
\textbf{GraphSAGE (Old Data)} & 15K & 78.72\% & \textbf{5.25} \\
\textbf{GATv2 Standard} & 25K & 69.31\% & \textbf{2.77} \\
\textbf{GATv2 (Temp 5)} & 30K & 68.06\% & \textbf{2.27} \\
GATv2 5-Layer & 52K & 66.79\% & 1.29 \\
Complex GATv2 (Temp 3) & 169K & 69.58\% & 0.41 \\
Complex GATv2 (Temp 5) & 169K & 67.99\% & 0.40 \\
Enhanced GATv2 & 6.0M & 69.90\% & 0.012 \\
\bottomrule
\end{tabular}
\vspace{0.3cm}
\begin{block}{Key Finding}
Simpler models achieve significantly better parameter efficiency
\end{block}
\end{frame}

% Training Efficiency
\begin{frame}{Training Efficiency}
\begin{table}
\centering
\footnotesize
\begin{tabular}{lcccc}
\toprule
\textbf{Model} & \textbf{Time} & \textbf{Epochs} & \textbf{Accuracy} & \textbf{Time/Acc} \\
\midrule
\textbf{GATv2 Standard} & 1h & 22 & 66.17\% & \textbf{0.91 min/\%} \\
\textbf{GATv2 5-Layer} & 1h & 21 & 57.83\% & 1.04 min/\% \\
\textbf{GATv2 (Temp 5)} & 1.5h & 32 & 63.85\% & 1.41 min/\% \\
\textbf{GraphSAGE} & 2h & 46 & 73.04\% & 1.64 min/\% \\
\textbf{Complex GATv2 (Temp 5)} & 2.5h & 59 & 70.03\% & 2.14 min/\% \\
\textbf{Complex GATv2 (Temp 3)} & 4h & 86 & 72.84\% & 3.30 min/\% \\
\textbf{Enhanced GATv2} & 6h & 11 & 67.25\% & 5.35 min/\% \\
\bottomrule
\end{tabular}
\end{frame}

% Regression Performance
\begin{frame}{Regression Performance - Top 3 Models}
\begin{table}
\centering
\begin{tabular}{lcccc}
\toprule
\textbf{Model} & \textbf{R² Score} & \textbf{MSE} & \textbf{RMSE} & \textbf{MAE} \\
\midrule
\textbf{Complex GATv2 (Temp 3)} & \textbf{0.2425} & \textbf{0.1879} & \textbf{0.4335} & \textbf{0.3954} \\
\textbf{Complex GATv2 (Temp 5)} & 0.2095 & 0.1964 & 0.4432 & 0.4132 \\
Enhanced GATv2 & 0.1465 & 0.2117 & 0.4601 & 0.4303 \\
\bottomrule
\end{tabular}
\vspace{0.5cm}
\begin{block}{Key Insight}
Complex GATv2 (Temp 3) dominates regression metrics with 24.25\% variance explained
\end{block}
\end{frame}

% Performance Leaders by Category
\begin{frame}{Performance Leaders by Category}
\begin{table}
\centering
\begin{tabular}{lcc}
\toprule
\textbf{Category} & \textbf{Winner} & \textbf{Score} \\
\midrule
\textbf{Accuracy} & GraphSAGE (Old Data) & 73.04\% \\
\textbf{Precision} & Complex GATv2 (Temp 3) & 71.05\% \\
\textbf{Recall} & GATv2 5-Layer & 93.06\% \\
\textbf{F1 Score} & GraphSAGE (Old Data) & 78.72\% \\
\textbf{ROC AUC} & Complex GATv2 (Temp 3) & 79.93\% \\
\textbf{Parameter Efficiency} & GraphSAGE (Old Data) & 5.25 F1/1K \\
\textbf{Training Speed} & GATv2 Standard & 1 hour \\
\bottomrule
\end{tabular}
\end{frame}

% Temporal Window Analysis
\begin{frame}{Temporal Window Analysis}
\begin{table}
\centering
\begin{tabular}{lccc}
\toprule
\textbf{Model Type} & \textbf{Temporal 3} & \textbf{Temporal 5} & \textbf{Difference} \\
\midrule
\textbf{Complex GATv2} & 72.84\% acc & 70.03\% acc & -2.81\% \\
\textbf{GATv2 Standard} & 66.17\% acc & 63.85\% acc & -2.32\% \\
\textbf{Training Epochs} & 86 epochs & 59 epochs & -31\% faster \\
\bottomrule
\end{tabular}
\vspace{0.5cm}
\begin{block}{Finding}
Longer temporal windows reduce accuracy but improve training efficiency
\end{block}
\end{frame}

% Model Complexity vs Performance
\begin{frame}{Model Complexity vs Performance}
\begin{table}
\centering
\begin{tabular}{lcccc}
\toprule
\textbf{Complexity} & \textbf{Model} & \textbf{Parameters} & \textbf{F1 Score} & \textbf{Trend} \\
\midrule
\textbf{Simple} & GraphSAGE & 15K & 78.72\% & ↗ Best \\
\textbf{Moderate} & GATv2 Standard & 25K & 69.31\% & → Good \\
\textbf{Complex} & Complex GATv2 & 169K & 69.58\% & → Similar \\
\textbf{Very Complex} & Enhanced GATv2 & 6.0M & 69.90\% & ↘ Diminishing \\
\bottomrule
\end{tabular}
\vspace{0.5cm}
\begin{block}{Key Insight}
More parameters ≠ Better performance
\end{block}
\end{frame}

% Recommendations
\begin{frame}{Model Selection Recommendations}
\begin{table}
\centering
\footnotesize
\begin{tabular}{lll}
\toprule
\textbf{Use Case} & \textbf{Recommended Model} & \textbf{Rationale} \\
\midrule
\textbf{Production} & Complex GATv2 (Temp 3) & Best current data accuracy + ROC AUC \\
\textbf{Resource Constrained} & GraphSAGE & Highest parameter efficiency \\
\textbf{Safety Critical} & GATv2 5-Layer & Highest recall (93.06\%) \\
\textbf{Research} & Enhanced GATv2 & Advanced architectural features \\
\textbf{Fast Deployment} & GATv2 Standard & Quick training (1 hour) \\
\textbf{Temporal Analysis} & Complex GATv2 (Temp 5) & Extended temporal context \\
\bottomrule
\end{tabular}
\end{frame}

% Key Findings
\begin{frame}{Key Findings}
\begin{enumerate}
\item \textbf{Simplicity Wins:} GraphSAGE (15K params) outperforms Enhanced GATv2 (6M params)

\item \textbf{Attention Improves Discrimination:} GATv2 models achieve better ROC AUC

\item \textbf{Temporal Trade-offs:} Longer windows reduce accuracy but speed training

\item \textbf{Parameter Efficiency Matters:} Best models have 15K-169K parameters

\item \textbf{Data Quality Impact:} Historical vs current data significantly affects performance
\end{enumerate}
\end{frame}

% Summary
\begin{frame}{Summary}
\begin{block}{Research Outcomes}
\begin{itemize}
    \item \textbf{7 Models Evaluated} across different architectures
    \item \textbf{18 Hours Total Training} time investment
    \item \textbf{12.4M Parameters} evaluated across all models
    \item \textbf{Clear Performance Hierarchy} established
\end{itemize}
\end{block}

\begin{block}{Top Performers}
\begin{itemize}
    \item \textbf{Overall:} GraphSAGE (73.04\% accuracy)
    \item \textbf{Current Data:} Complex GATv2 Temp 3 (72.84\% accuracy)
    \item \textbf{Efficiency:} GraphSAGE (5.25 F1/1K parameters)
\end{itemize}
\end{block}

\textbf{Next Steps:} Model deployment and further optimization
\end{frame}

% Questions
\begin{frame}{Questions \& Discussion}
\begin{center}
\Huge Thank you for your attention

\vspace{1cm}

\Large Questions?

\vspace{0.5cm}

\normalsize
\begin{itemize}
    \item Model selection for specific applications
    \item Future research directions
    \item Implementation considerations
    \item Performance optimization strategies
\end{itemize}
\end{center}
\end{frame}

\end{document}
