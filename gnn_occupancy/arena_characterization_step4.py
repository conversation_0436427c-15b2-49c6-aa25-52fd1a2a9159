#!/usr/bin/env python3
"""
Step 4: Final Integration and Summary
Create final comprehensive arena characterization with all features integrated.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
from datetime import datetime
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.patches import Rectangle, Circle
import matplotlib.gridspec as gridspec
from matplotlib.backends.backend_pdf import PdfPages
import pandas as pd

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


class FinalArenaIntegrator:
    """Final integration and summary of arena characterization."""
    
    def __init__(self, results_dir: str = "results/arena_characterization"):
        self.results_dir = Path(results_dir)
        self.final_dir = self.results_dir / "final_integration"
        self.final_dir.mkdir(parents=True, exist_ok=True)
        
        # Load all previous results
        self.arena_chars = None
        self.spatial_grid = None
        self.density_grid = None
        self.occupancy_grid = None
        self.all_positions = None
        self.all_labels = None
        self.comprehensive_report = None
        
        self._load_all_results()
        
    def _load_all_results(self):
        """Load all results from previous steps."""
        print("📂 Loading all previous results...")
        
        # Load arena characteristics
        chars_file = self.results_dir / 'arena_characteristics.json'
        with open(chars_file, 'r') as f:
            self.arena_chars = json.load(f)
        
        # Load grids and data
        self.spatial_grid = np.load(self.results_dir / 'spatial_coverage_grid.npy')
        self.density_grid = np.load(self.results_dir / 'density_grid.npy')
        self.occupancy_grid = np.load(self.results_dir / 'occupancy_grid.npy')
        self.all_positions = np.load(self.results_dir / 'all_positions.npy')
        self.all_labels = np.load(self.results_dir / 'all_labels.npy')
        
        # Load comprehensive report
        report_file = self.results_dir / 'interactive_visualizations' / 'comprehensive_arena_report.json'
        with open(report_file, 'r') as f:
            self.comprehensive_report = json.load(f)
        
        print("  ✅ All results loaded successfully")
    
    def create_final_summary_document(self):
        """Create final comprehensive summary document."""
        print("📄 Creating final summary document...")
        
        # Extract key metrics
        arena_data = self.comprehensive_report['arena_characterization_report']
        
        summary_content = f"""# 🏟️ Comprehensive Arena Characterization Report

**Analysis Date**: {datetime.now().strftime('%B %d, %Y')}  
**Arena Type**: Robotic Experimental Environment  
**Analysis System**: Multi-Step Spatial Characterization Framework  

---

## 📊 **Executive Summary**

This comprehensive analysis characterizes a **{arena_data['arena_dimensions']['width_m']:.1f}m × {arena_data['arena_dimensions']['height_m']:.1f}m robotic experimental arena** using advanced spatial analysis techniques. The arena covers **{arena_data['arena_dimensions']['total_area_m2']:.1f} m²** and has been thoroughly analyzed using **{arena_data['data_quality_assessment']['total_samples']:,} test samples**.

### 🎯 **Key Findings**

1. **🏟️ Arena Scale**: Large-scale experimental environment ({arena_data['arena_dimensions']['total_area_m2']:.1f} m²)
2. **📍 Spatial Coverage**: {arena_data['data_quality_assessment']['spatial_coverage_percentage']:.1f}% of arena area sampled
3. **🤖 Operational Areas**: {arena_data['feature_analysis']['operational_area_m2']:.1f} m² actively used by robots
4. **🏭 Workstation Distribution**: {arena_data['feature_analysis']['workstation_percentage']:.1f}% of operational area
5. **🛤️ Robot Path Network**: {arena_data['feature_analysis']['path_percentage']:.1f}% dedicated to navigation

---

## 🏟️ **Arena Specifications**

### **Physical Dimensions**
- **Width**: {arena_data['arena_dimensions']['width_m']:.2f} meters
- **Height**: {arena_data['arena_dimensions']['height_m']:.2f} meters  
- **Total Area**: {arena_data['arena_dimensions']['total_area_m2']:.1f} m²
- **Coordinate Bounds**: 
  - X: [{arena_data['arena_dimensions']['bounds']['x_min']:.2f}, {arena_data['arena_dimensions']['bounds']['x_max']:.2f}] m
  - Y: [{arena_data['arena_dimensions']['bounds']['y_min']:.2f}, {arena_data['arena_dimensions']['bounds']['y_max']:.2f}] m

### **Grid Resolution**
- **Cell Size**: {arena_data['technical_specifications']['grid_resolution_m']} m × {arena_data['technical_specifications']['grid_resolution_m']} m
- **Grid Dimensions**: {arena_data['technical_specifications']['grid_dimensions']} cells
- **Total Grid Cells**: {arena_data['technical_specifications']['total_grid_cells']:,}

---

## 🔍 **Spatial Feature Analysis**

### **🏭 Workstation Areas**
- **Total Area**: {arena_data['feature_analysis']['workstation_area_m2']:.1f} m²
- **Percentage of Arena**: {arena_data['feature_analysis']['workstation_percentage']:.1f}%
- **Characteristics**: High-occupancy regions indicating fixed equipment or work areas

### **🛤️ Robot Navigation Paths**
- **Total Path Area**: {arena_data['feature_analysis']['robot_path_area_m2']:.1f} m²
- **Percentage of Arena**: {arena_data['feature_analysis']['path_percentage']:.1f}%
- **Characteristics**: High-traffic, low-occupancy corridors for robot movement

### **🌐 Operational Coverage**
- **Active Area**: {arena_data['feature_analysis']['operational_area_m2']:.1f} m²
- **Coverage Efficiency**: {arena_data['data_quality_assessment']['spatial_coverage_percentage']:.1f}%
- **Sample Density**: {arena_data['data_quality_assessment']['sample_density_per_m2']:.1f} samples/m²

---

## 📈 **Data Quality Assessment**

### **📊 Sample Distribution**
- **Total Samples**: {arena_data['data_quality_assessment']['total_samples']:,}
- **Unique Positions**: {arena_data['data_quality_assessment']['unique_positions']:,}
- **Spatial Coverage**: {arena_data['data_quality_assessment']['spatial_coverage_percentage']:.1f}% of arena
- **Sample Density**: {arena_data['data_quality_assessment']['sample_density_per_m2']:.1f} samples per m²

### **🎯 Coverage Quality**
- **Well-Sampled Regions**: {arena_data['data_quality_assessment']['well_sampled_regions']:,} grid cells
- **Under-Sampled Regions**: {arena_data['data_quality_assessment']['under_sampled_regions']:,} grid cells
- **Coverage Completeness**: {(arena_data['data_quality_assessment']['well_sampled_regions'] / (arena_data['data_quality_assessment']['well_sampled_regions'] + arena_data['data_quality_assessment']['under_sampled_regions']) * 100):.1f}%

---

## 🤖 **Occupancy Statistics**

### **📍 Space Utilization**
- **Overall Occupancy**: {arena_data['occupancy_statistics']['overall_occupancy_ratio']*100:.1f}%
- **Occupied Samples**: {arena_data['occupancy_statistics']['occupied_samples']:,}
- **Free Space Samples**: {arena_data['occupancy_statistics']['free_samples']:,}

### **🏭 Area Classification**
- **Static Occupied Areas**: Workstations and fixed equipment
- **Dynamic Navigation Areas**: Robot movement corridors
- **Mixed-Use Zones**: Areas with variable occupancy patterns

---

## 🔧 **Technical Specifications**

### **📏 Measurement System**
- **Coordinate System**: Real-world meters
- **Grid Resolution**: {arena_data['technical_specifications']['grid_resolution_m']} m per cell
- **Temporal Window**: {arena_data['technical_specifications']['temporal_window']} frames
- **Analysis Framework**: Multi-step spatial characterization

### **📊 Analysis Methods**
- **Spatial Coverage Analysis**: Grid-based occupancy mapping
- **Feature Identification**: Automated workstation and path detection
- **Data Quality Assessment**: Sample density and coverage evaluation
- **Statistical Analysis**: Comprehensive occupancy pattern analysis

---

## 📁 **Generated Outputs**

### **📊 Visualizations Created**
```
🎨 Basic Visualizations:
├── arena_overview.png - Comprehensive 6-panel overview
├── detailed_arena_map.png - High-resolution spatial map
└── arena_overview.pdf - Publication-ready overview

🎯 Interactive Visualizations:
├── interactive_arena_analysis.html - Interactive web visualization
├── publication_arena_analysis.png - High-resolution publication figure
└── publication_arena_analysis.pdf - Vector publication figure

📄 Data Files:
├── arena_characteristics.json - Complete arena metrics
├── comprehensive_arena_report.json - Detailed analysis report
├── spatial_coverage_grid.npy - Coverage data array
├── density_grid.npy - Sample density array
├── occupancy_grid.npy - Occupancy probability array
├── all_positions.npy - Raw position data
└── all_labels.npy - Raw occupancy labels
```

---

## 🎯 **Key Insights for Robotics Research**

### **🏭 Environment Characteristics**
1. **Large-Scale Arena**: {arena_data['arena_dimensions']['total_area_m2']:.1f} m² provides substantial experimental space
2. **Mixed Environment**: Combination of static workstations and dynamic navigation areas
3. **High Data Quality**: {arena_data['data_quality_assessment']['spatial_coverage_percentage']:.1f}% coverage with {arena_data['data_quality_assessment']['sample_density_per_m2']:.1f} samples/m²

### **🤖 Robot Operation Insights**
1. **Navigation Efficiency**: Clear path network identified for robot movement
2. **Workspace Organization**: Defined workstation areas for task execution
3. **Collision Avoidance**: Occupancy patterns reveal high-risk zones

### **📊 Model Performance Context**
1. **Spatial Complexity**: Arena provides diverse scenarios for model testing
2. **Data Richness**: High sample density enables robust model evaluation
3. **Real-World Relevance**: Industrial-scale environment for practical validation

---

## 🏆 **Recommendations**

### **🔬 For Research Applications**
1. **Model Testing**: Use identified workstation and path areas for targeted evaluation
2. **Data Collection**: Focus additional sampling on under-sampled regions
3. **Benchmark Development**: Leverage arena characteristics for standardized testing

### **🤖 For Robot Deployment**
1. **Path Planning**: Utilize identified navigation corridors for efficient routing
2. **Safety Zones**: Implement collision avoidance in high-occupancy areas
3. **Task Allocation**: Optimize robot assignments based on workstation locations

### **📈 For Future Analysis**
1. **Temporal Analysis**: Extend characterization to include time-based patterns
2. **Multi-Robot Coordination**: Analyze interaction patterns in shared spaces
3. **Dynamic Adaptation**: Monitor arena changes over extended periods

---

**Analysis Framework**: 4-Step Comprehensive Arena Characterization  
**Total Analysis Time**: Multi-step automated processing  
**Data Processing**: {arena_data['data_quality_assessment']['total_samples']:,} samples across {arena_data['arena_dimensions']['total_area_m2']:.1f} m²  
**Report Generated**: {datetime.now().strftime('%B %d, %Y at %H:%M:%S')}
"""
        
        # Save summary document
        summary_path = self.final_dir / 'FINAL_ARENA_CHARACTERIZATION_SUMMARY.md'
        with open(summary_path, 'w') as f:
            f.write(summary_content)
        
        print(f"  ✅ Saved final summary: {summary_path}")
        
    def create_final_statistics_csv(self):
        """Create comprehensive statistics CSV for analysis."""
        print("📊 Creating final statistics CSV...")
        
        arena_data = self.comprehensive_report['arena_characterization_report']
        
        # Create comprehensive statistics table
        stats_data = [
            # Arena Dimensions
            ['Arena_Width_m', arena_data['arena_dimensions']['width_m']],
            ['Arena_Height_m', arena_data['arena_dimensions']['height_m']],
            ['Arena_Total_Area_m2', arena_data['arena_dimensions']['total_area_m2']],
            ['Arena_X_Min', arena_data['arena_dimensions']['bounds']['x_min']],
            ['Arena_X_Max', arena_data['arena_dimensions']['bounds']['x_max']],
            ['Arena_Y_Min', arena_data['arena_dimensions']['bounds']['y_min']],
            ['Arena_Y_Max', arena_data['arena_dimensions']['bounds']['y_max']],
            
            # Feature Analysis
            ['Workstation_Area_m2', arena_data['feature_analysis']['workstation_area_m2']],
            ['Robot_Path_Area_m2', arena_data['feature_analysis']['robot_path_area_m2']],
            ['Operational_Area_m2', arena_data['feature_analysis']['operational_area_m2']],
            ['Workstation_Percentage', arena_data['feature_analysis']['workstation_percentage']],
            ['Path_Percentage', arena_data['feature_analysis']['path_percentage']],
            
            # Data Quality
            ['Total_Samples', arena_data['data_quality_assessment']['total_samples']],
            ['Unique_Positions', arena_data['data_quality_assessment']['unique_positions']],
            ['Spatial_Coverage_Percentage', arena_data['data_quality_assessment']['spatial_coverage_percentage']],
            ['Sample_Density_per_m2', arena_data['data_quality_assessment']['sample_density_per_m2']],
            ['Well_Sampled_Regions', arena_data['data_quality_assessment']['well_sampled_regions']],
            ['Under_Sampled_Regions', arena_data['data_quality_assessment']['under_sampled_regions']],
            
            # Occupancy Statistics
            ['Overall_Occupancy_Ratio', arena_data['occupancy_statistics']['overall_occupancy_ratio']],
            ['Occupied_Samples', arena_data['occupancy_statistics']['occupied_samples']],
            ['Free_Samples', arena_data['occupancy_statistics']['free_samples']],
            
            # Technical Specifications
            ['Grid_Resolution_m', arena_data['technical_specifications']['grid_resolution_m']],
            ['Grid_Width', int(arena_data['technical_specifications']['grid_dimensions'].split(' × ')[0])],
            ['Grid_Height', int(arena_data['technical_specifications']['grid_dimensions'].split(' × ')[1])],
            ['Total_Grid_Cells', arena_data['technical_specifications']['total_grid_cells']],
            ['Temporal_Window', arena_data['technical_specifications']['temporal_window']]
        ]
        
        # Create DataFrame
        df = pd.DataFrame(stats_data, columns=['Metric', 'Value'])
        
        # Save CSV
        csv_path = self.final_dir / 'arena_comprehensive_statistics.csv'
        df.to_csv(csv_path, index=False)
        
        print(f"  ✅ Saved statistics CSV: {csv_path}")
        
    def create_final_visualization_index(self):
        """Create an index of all generated visualizations."""
        print("🗂️ Creating visualization index...")
        
        # Collect all visualization files
        viz_files = []
        
        # Basic visualizations
        basic_viz_dir = self.results_dir / "visualizations"
        if basic_viz_dir.exists():
            for file in basic_viz_dir.glob("*"):
                viz_files.append({
                    'category': 'Basic Visualizations',
                    'filename': file.name,
                    'path': str(file.relative_to(self.results_dir)),
                    'description': self._get_file_description(file.name)
                })
        
        # Interactive visualizations
        interactive_viz_dir = self.results_dir / "interactive_visualizations"
        if interactive_viz_dir.exists():
            for file in interactive_viz_dir.glob("*"):
                viz_files.append({
                    'category': 'Interactive Visualizations',
                    'filename': file.name,
                    'path': str(file.relative_to(self.results_dir)),
                    'description': self._get_file_description(file.name)
                })
        
        # Create index
        index_content = f"""# 📊 Arena Characterization - Visualization Index

**Generated**: {datetime.now().strftime('%B %d, %Y at %H:%M:%S')}  
**Total Files**: {len(viz_files)}

---

## 📁 **File Directory**

"""
        
        current_category = None
        for viz_file in sorted(viz_files, key=lambda x: (x['category'], x['filename'])):
            if viz_file['category'] != current_category:
                current_category = viz_file['category']
                index_content += f"\n### 🎨 **{current_category}**\n\n"
            
            index_content += f"- **{viz_file['filename']}**\n"
            index_content += f"  - *Path*: `{viz_file['path']}`\n"
            index_content += f"  - *Description*: {viz_file['description']}\n\n"
        
        index_content += f"""
---

## 🔍 **File Descriptions**

### **Basic Visualizations**
- **PNG Files**: High-resolution raster images (300 DPI) suitable for presentations
- **PDF Files**: Vector graphics suitable for publication and printing

### **Interactive Visualizations**
- **HTML Files**: Interactive web-based visualizations with zoom and hover capabilities
- **JSON Files**: Machine-readable data files for further analysis

### **Data Files**
- **NPY Files**: NumPy arrays containing processed spatial data
- **CSV Files**: Tabular data suitable for spreadsheet analysis

---

## 🎯 **Usage Recommendations**

### **For Presentations**
- Use PNG files from basic visualizations
- Interactive HTML files for live demonstrations

### **For Publications**
- Use PDF files for vector graphics in papers
- High-resolution PNG files for journals requiring raster images

### **For Further Analysis**
- NPY files for Python-based analysis
- CSV files for statistical software or spreadsheet analysis
- JSON files for web applications or databases

---

**Arena Characterization System**: 4-Step Comprehensive Analysis Framework  
**Analysis Completed**: {datetime.now().strftime('%B %d, %Y')}
"""
        
        # Save index
        index_path = self.final_dir / 'VISUALIZATION_INDEX.md'
        with open(index_path, 'w') as f:
            f.write(index_content)
        
        print(f"  ✅ Saved visualization index: {index_path}")
        
    def _get_file_description(self, filename: str) -> str:
        """Get description for a file based on its name."""
        descriptions = {
            'arena_overview.png': 'Comprehensive 6-panel arena overview with statistics',
            'arena_overview.pdf': 'Publication-ready arena overview (vector graphics)',
            'detailed_arena_map.png': 'High-resolution spatial map with sample points',
            'detailed_arena_map.pdf': 'Vector version of detailed arena map',
            'interactive_arena_analysis.html': 'Interactive web visualization with zoom and layers',
            'publication_arena_analysis.png': 'High-resolution publication figure',
            'publication_arena_analysis.pdf': 'Vector publication figure',
            'comprehensive_arena_report.json': 'Complete analysis report in JSON format',
            'arena_characteristics.json': 'Arena metrics and characteristics',
            'spatial_coverage_grid.npy': 'Binary grid showing spatial coverage',
            'density_grid.npy': 'Sample density per grid cell',
            'occupancy_grid.npy': 'Occupancy probability per grid cell',
            'all_positions.npy': 'Raw X,Y position coordinates',
            'all_labels.npy': 'Raw occupancy labels (0=free, 1=occupied)'
        }
        
        return descriptions.get(filename, 'Generated analysis file')
    
    def run_step4_final_integration(self):
        """Run complete Step 4 final integration."""
        print("🚀 Starting Step 4: Final Integration and Summary")
        print("=" * 70)
        
        try:
            # Create final summary document
            self.create_final_summary_document()
            
            # Create comprehensive statistics CSV
            self.create_final_statistics_csv()
            
            # Create visualization index
            self.create_final_visualization_index()
            
            print("\n" + "=" * 70)
            print("🎉 STEP 4 COMPLETED SUCCESSFULLY!")
            print("=" * 70)
            print(f"📁 Final integration saved to: {self.final_dir}")
            print("✅ Arena characterization system COMPLETE!")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Step 4 failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function for Step 4."""
    print("🔬 Arena Characterization System - Step 4")
    print("🎯 Final Integration and Summary")
    print("=" * 80)
    
    integrator = FinalArenaIntegrator()
    success = integrator.run_step4_final_integration()
    
    if success:
        print("\n🎉 ARENA CHARACTERIZATION SYSTEM COMPLETED SUCCESSFULLY!")
        print("📁 Check the final_integration directory for comprehensive results.")
    else:
        print("\n❌ Step 4 failed. Check the error messages above.")
    
    return success


if __name__ == "__main__":
    main()
