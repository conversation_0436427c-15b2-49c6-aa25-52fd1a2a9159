#!/bin/bash

# Default values
GNN_TYPE="graphsage"
TEMPORAL_WINDOW=3
EPOCHS=5
BATCH_SIZE=32
LEARNING_RATE=0.001
MODE="train"
EVAL_AFTER_TRAIN=false
VIZ_ONLY=false
MODEL_DIR=""
NUM_VIZ_SAMPLES=3

# Function to display usage information
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -g, --gnn-type TYPE       GNN type (graphsage, gatv2, ecc) [default: graphsage]"
    echo "  -t, --temporal WINDOW     Temporal window size (1, 3, 5) [default: 3]"
    echo "  -e, --epochs NUM          Number of epochs [default: 5]"
    echo "  -b, --batch-size SIZE     Batch size [default: 32]"
    echo "  -l, --learning-rate RATE  Learning rate [default: 0.001]"
    echo "  -m, --mode MODE           Mode (train, evaluate, visualize) [default: train]"
    echo "  -a, --eval-after          Run evaluation after training [default: false]"
    echo "  -d, --model-dir DIR       Model directory for evaluation/visualization"
    echo "  -s, --samples NUM         Number of samples to visualize [default: 3]"
    echo "  -v, --viz-only            Run visualization only [default: false]"
    echo "  -h, --help                Display this help message"
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        -g|--gnn-type)
            GNN_TYPE="$2"
            shift 2
            ;;
        -t|--temporal)
            TEMPORAL_WINDOW="$2"
            shift 2
            ;;
        -e|--epochs)
            EPOCHS="$2"
            shift 2
            ;;
        -b|--batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        -l|--learning-rate)
            LEARNING_RATE="$2"
            shift 2
            ;;
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -a|--eval-after)
            EVAL_AFTER_TRAIN=true
            shift
            ;;
        -d|--model-dir)
            MODEL_DIR="$2"
            shift 2
            ;;
        -s|--samples)
            NUM_VIZ_SAMPLES="$2"
            shift 2
            ;;
        -v|--viz-only)
            VIZ_ONLY=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Validate GNN type
if [[ "$GNN_TYPE" != "graphsage" && "$GNN_TYPE" != "gatv2" && "$GNN_TYPE" != "ecc" ]]; then
    echo "Error: GNN type must be one of: graphsage, gatv2, ecc"
    exit 1
fi

# Validate temporal window
if [[ "$TEMPORAL_WINDOW" != "1" && "$TEMPORAL_WINDOW" != "3" && "$TEMPORAL_WINDOW" != "5" ]]; then
    echo "Error: Temporal window must be one of: 1, 3, 5"
    exit 1
fi

# Validate mode
if [[ "$MODE" != "train" && "$MODE" != "evaluate" && "$MODE" != "visualize" ]]; then
    echo "Error: Mode must be one of: train, evaluate, visualize"
    exit 1
fi

# Check if model directory is provided for evaluation or visualization
if [[ ("$MODE" == "evaluate" || "$MODE" == "visualize" || "$VIZ_ONLY" == true) && -z "$MODEL_DIR" ]]; then
    echo "Error: Model directory (-d, --model-dir) must be provided for evaluation or visualization"
    exit 1
fi

# Check if model directory exists
if [[ ! -z "$MODEL_DIR" && ! -d "$MODEL_DIR" ]]; then
    echo "Error: Model directory '$MODEL_DIR' does not exist"
    exit 1
fi

# Set up directories and config
if [[ "$MODE" == "train" ]]; then
    # Create timestamp for folder name
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    OUTPUT_DIR="output/${GNN_TYPE}_temp${TEMPORAL_WINDOW}_${TIMESTAMP}"

    # Create output directories
    mkdir -p "${OUTPUT_DIR}/checkpoints"
    mkdir -p "${OUTPUT_DIR}/visualizations"

    # Copy config file
    cp /home/<USER>/ma_yugi/gnn_occupancy/config.yaml "${OUTPUT_DIR}/config.yaml"

    # Update config file with parameters
    sed -i "s/gnn_type: \".*\"/gnn_type: \"${GNN_TYPE}\"/g" "${OUTPUT_DIR}/config.yaml"
    sed -i "s/temporal_windows: \[.*\]/temporal_windows: \[${TEMPORAL_WINDOW}\]/g" "${OUTPUT_DIR}/config.yaml"
    sed -i "s/batch_size: [0-9]*/batch_size: ${BATCH_SIZE}/g" "${OUTPUT_DIR}/config.yaml"
    sed -i "s/learning_rate: [0-9.e-]*/learning_rate: ${LEARNING_RATE}/g" "${OUTPUT_DIR}/config.yaml"
    sed -i "s/num_epochs: [0-9]*/num_epochs: ${EPOCHS}/g" "${OUTPUT_DIR}/config.yaml"
    sed -i "s|checkpoint_dir: \".*\"|checkpoint_dir: \"${OUTPUT_DIR}/checkpoints\"|g" "${OUTPUT_DIR}/config.yaml"
    sed -i "s|save_dir: \".*\"|save_dir: \"${OUTPUT_DIR}/visualizations\"|g" "${OUTPUT_DIR}/config.yaml"
    sed -i "s/num_samples: [0-9]*/num_samples: ${NUM_VIZ_SAMPLES}/g" "${OUTPUT_DIR}/config.yaml"

    # Adjust input dimension based on temporal window
    if [[ "$TEMPORAL_WINDOW" -gt 1 ]]; then
        sed -i "s/input_dim: [0-9]*/input_dim: 10/g" "${OUTPUT_DIR}/config.yaml"
    else
        sed -i "s/input_dim: [0-9]*/input_dim: 9/g" "${OUTPUT_DIR}/config.yaml"
    fi

    # Copy config to checkpoints directory
    cp "${OUTPUT_DIR}/config.yaml" "${OUTPUT_DIR}/checkpoints/config.yaml"

    echo "Created output directory: ${OUTPUT_DIR}"
    echo "Updated configuration saved to: ${OUTPUT_DIR}/config.yaml"
    
    CONFIG_PATH="${OUTPUT_DIR}/config.yaml"
else
    # For evaluation or visualization, use the provided model directory
    OUTPUT_DIR="${MODEL_DIR}"
    CONFIG_PATH="${MODEL_DIR}/config.yaml"
    
    # Update visualization samples if needed
    if [[ "$MODE" == "visualize" || "$VIZ_ONLY" == true ]]; then
        sed -i "s/num_samples: [0-9]*/num_samples: ${NUM_VIZ_SAMPLES}/g" "${CONFIG_PATH}"
    fi
    
    echo "Using existing model directory: ${OUTPUT_DIR}"
    echo "Using configuration from: ${CONFIG_PATH}"
fi

# Execute the requested operation
if [[ "$MODE" == "train" ]]; then
    echo "Starting training with GNN type: ${GNN_TYPE}, temporal window: ${TEMPORAL_WINDOW}"
    python3 /home/<USER>/ma_yugi/gnn_occupancy/main.py --mode train --temporal_window "${TEMPORAL_WINDOW}" --gnn_type "${GNN_TYPE}" --config "${CONFIG_PATH}"
    
    # Run evaluation after training if requested
    if [[ "$EVAL_AFTER_TRAIN" == true ]]; then
        echo "Running evaluation after training"
        python3 /home/<USER>/ma_yugi/gnn_occupancy/main.py --mode evaluate --temporal_window "${TEMPORAL_WINDOW}" --gnn_type "${GNN_TYPE}" --config "${CONFIG_PATH}"
    fi
elif [[ "$MODE" == "evaluate" ]]; then
    echo "Running evaluation with GNN type: ${GNN_TYPE}, temporal window: ${TEMPORAL_WINDOW}"
    python3 /home/<USER>/ma_yugi/gnn_occupancy/main.py --mode evaluate --temporal_window "${TEMPORAL_WINDOW}" --gnn_type "${GNN_TYPE}" --config "${CONFIG_PATH}"
elif [[ "$MODE" == "visualize" || "$VIZ_ONLY" == true ]]; then
    echo "Running visualization with GNN type: ${GNN_TYPE}, temporal window: ${TEMPORAL_WINDOW}"
    python3 /home/<USER>/ma_yugi/gnn_occupancy/main.py --mode evaluate --temporal_window "${TEMPORAL_WINDOW}" --gnn_type "${GNN_TYPE}" --config "${CONFIG_PATH}" --visualize-only
fi

echo "Process completed. Results saved to: ${OUTPUT_DIR}"
