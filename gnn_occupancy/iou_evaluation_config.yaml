# Configuration file for Comprehensive 2D IoU Evaluation System
# This file allows you to customize the evaluation parameters

# Grid Configuration
grid:
  resolution: 0.1        # Grid cell size in meters (0.1 = 10cm cells)
  padding: 0.5          # Padding around data bounds in meters
  threshold: 0.5        # Prediction threshold for binary classification

# Data Configuration
data:
  data_dir: "../data/07_gnn_ready"  # Path to test data directory
  temporal_windows: [3, 5]          # Temporal windows to evaluate
  batch_size: 1                     # Batch size for evaluation (keep at 1 for IoU calculation)
  num_workers: 0                    # Number of data loading workers

# Model Configuration
models:
  checkpoint_patterns:              # Patterns to search for checkpoint directories
    - "checkpoints*"
    - "checkpoints_*"
  model_file_pattern: "model_temporal_*_best.pt"  # Pattern for model files
  config_file_name: "config.yaml"  # Name of config file in checkpoint directories

# Evaluation Configuration
evaluation:
  max_samples_per_model: null       # Limit samples per model (null = no limit)
  save_intermediate_results: true   # Save results after each model
  enable_spatial_analysis: true     # Enable spatial IoU analysis
  enable_failure_analysis: true     # Enable failure case analysis

# Visualization Configuration
visualization:
  style: "seaborn-v0_8"            # Matplotlib style
  color_palette: "husl"            # Seaborn color palette
  figure_dpi: 300                  # DPI for saved figures
  figure_format: ["png", "pdf"]    # Output formats for figures
  
  # Chart configurations
  charts:
    model_comparison:
      figsize: [20, 16]            # Figure size for model comparison charts
      show_top_n: 10               # Show top N models in detailed charts
    
    spatial_analysis:
      figsize: [18, 12]            # Figure size for spatial analysis
      show_top_n: 3                # Show top N models for spatial analysis
    
    individual_analysis:
      figsize: [15, 12]            # Figure size for individual model analysis
      show_top_n: 3                # Create individual analysis for top N models

# Output Configuration
output:
  results_dir: "results/2d_iou_evaluation"  # Directory to save results
  create_pdf_report: true                   # Create comprehensive PDF report
  create_csv_export: true                   # Export results to CSV
  create_json_summary: true                 # Create JSON summary statistics
  
  # File naming
  files:
    model_comparison_charts: "model_comparison_charts"
    spatial_analysis_heatmaps: "spatial_analysis_heatmaps"
    individual_analysis_prefix: "individual_analysis_"
    detailed_results_csv: "iou_detailed_results.csv"
    summary_statistics_json: "summary_statistics.json"
    pdf_report: "iou_summary_report.pdf"

# Statistical Analysis Configuration
statistics:
  confidence_level: 0.95           # Confidence level for statistical tests
  significance_threshold: 0.05     # P-value threshold for significance
  enable_statistical_tests: true   # Enable statistical significance testing
  
  # Tests to perform
  tests:
    temporal_window_comparison: true    # Compare temporal windows
    architecture_comparison: true      # Compare architectures
    parameter_efficiency: true         # Analyze parameter efficiency

# Advanced Configuration
advanced:
  memory_optimization: true        # Enable memory optimization techniques
  parallel_processing: false      # Enable parallel processing (experimental)
  cache_grid_conversions: true    # Cache grid conversions to disk
  verbose_logging: true           # Enable verbose logging
  
  # Error handling
  error_handling:
    continue_on_model_error: true  # Continue evaluation if a model fails
    max_retries: 3                # Maximum retries for failed operations
    timeout_seconds: 300          # Timeout for model evaluation

# Hardware Configuration
hardware:
  use_gpu: true                   # Use GPU if available
  gpu_memory_fraction: 0.8       # Fraction of GPU memory to use
  cpu_threads: null              # Number of CPU threads (null = auto)

# Debugging Configuration
debug:
  enable_debug_mode: false       # Enable debug mode
  save_debug_info: false         # Save debug information
  plot_sample_grids: false       # Plot sample grids for debugging
  max_debug_samples: 5           # Maximum samples to debug
