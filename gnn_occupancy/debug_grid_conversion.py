#!/usr/bin/env python3
"""
Debug script to understand the grid conversion issue.
"""

import torch
import numpy as np
from data import OccupancyDataset
from torch_geometric.data import DataLoader

def debug_grid_conversion():
    """Debug the grid conversion to understand the indexing issue."""
    print("🔍 Debugging grid conversion...")
    
    # Create dataset
    dataset = OccupancyDataset(
        root="../data/07_gnn_ready",
        split='test',
        temporal_window=3,
        binary_mapping={'occupied': [1, 2, 3, 4], 'unoccupied': [0]}
    )
    
    # Create data loader
    data_loader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # Get first batch
    batch = next(iter(data_loader))
    
    print(f"📊 Batch information:")
    print(f"  - batch.x.shape: {batch.x.shape}")
    print(f"  - batch.y.shape: {batch.y.shape}")
    print(f"  - batch.pos.shape: {batch.pos.shape}")
    
    # Extract positions and labels
    positions = batch.pos[:, :2].cpu().numpy()  # X, Y coordinates
    labels = batch.y.cpu().numpy().flatten()
    
    print(f"\n📍 Extracted data:")
    print(f"  - positions.shape: {positions.shape}")
    print(f"  - labels.shape: {labels.shape}")
    print(f"  - positions: {positions}")
    print(f"  - labels: {labels}")
    
    # Test grid conversion
    resolution = 0.1
    padding = 0.5
    
    # Calculate grid bounds with padding
    min_x, min_y = positions.min(axis=0) - padding
    max_x, max_y = positions.max(axis=0) + padding
    
    print(f"\n🗺️ Grid bounds:")
    print(f"  - min_x: {min_x}, min_y: {min_y}")
    print(f"  - max_x: {max_x}, max_y: {max_y}")
    
    # Calculate grid dimensions
    grid_width = int(np.ceil((max_x - min_x) / resolution))
    grid_height = int(np.ceil((max_y - min_y) / resolution))
    
    print(f"  - grid_width: {grid_width}, grid_height: {grid_height}")
    
    # Convert points to grid indices
    grid_x = ((positions[:, 0] - min_x) / resolution).astype(int)
    grid_y = ((positions[:, 1] - min_y) / resolution).astype(int)
    
    print(f"\n📐 Grid indices:")
    print(f"  - grid_x: {grid_x}")
    print(f"  - grid_y: {grid_y}")
    print(f"  - grid_x.shape: {grid_x.shape}")
    print(f"  - grid_y.shape: {grid_y.shape}")
    
    # Clip to grid bounds
    grid_x = np.clip(grid_x, 0, grid_width - 1)
    grid_y = np.clip(grid_y, 0, grid_height - 1)
    
    print(f"  - clipped grid_x: {grid_x}")
    print(f"  - clipped grid_y: {grid_y}")
    
    # Initialize grids
    gt_grid = np.zeros((grid_height, grid_width), dtype=np.float32)
    gt_count = np.zeros((grid_height, grid_width), dtype=np.int32)
    
    print(f"\n🏗️ Grid initialization:")
    print(f"  - gt_grid.shape: {gt_grid.shape}")
    print(f"  - gt_count.shape: {gt_count.shape}")
    
    # Test aggregation
    print(f"\n🔄 Testing aggregation:")
    for i in range(len(positions)):
        y, x = grid_y[i], grid_x[i]
        print(f"  - Point {i}: grid_y[{i}]={y}, grid_x[{i}]={x}, label={labels[i]}")
        
        # This is where the error might occur
        try:
            gt_grid[y, x] += labels[i]
            gt_count[y, x] += 1
            print(f"    ✅ Successfully added to grid")
        except Exception as e:
            print(f"    ❌ Error: {e}")
            print(f"    ❌ y={y}, x={x}, gt_grid.shape={gt_grid.shape}")
            break
    
    print(f"\n✅ Grid conversion completed successfully!")
    return True

if __name__ == "__main__":
    debug_grid_conversion()
