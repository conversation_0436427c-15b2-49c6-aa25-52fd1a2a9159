#!/bin/bash

echo "Starting Enhanced GNN training..."
echo "Configuration:"
echo "  - Architecture: Enhanced GATv2 with Transformer features"
echo "  - Hidden dimensions: 256"
echo "  - Number of layers: 6"
echo "  - Attention heads: 12"
echo "  - Temporal window: 3"
echo "  - Advanced features: Residual connections, Self-attention, Hierarchical pooling"
echo "  - Optimizer: Adam<PERSON> with cosine annealing"
echo "  - Learning rate: 0.0005 with warmup"
echo ""

echo "Starting training..."
cd /home/<USER>/ma_yugi/gnn_occupancy
python3 train_enhanced.py

echo ""
echo "Training completed!"
echo "Checkpoints saved to: checkpoints_enhanced_temp3/"
echo "Visualizations will be saved to: visualizations_enhanced_temp3/"
echo ""
echo "Next steps:"
echo "1. Run comprehensive evaluation"
echo "2. Compare with previous models"
echo "3. Analyze performance improvements"
