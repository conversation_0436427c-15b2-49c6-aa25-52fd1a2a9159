# GNN Occupancy Prediction Models
## Comprehensive Performance Analysis

---

## Slide 1: Title Slide

**GNN Models for Occupancy Prediction**
**Performance Comparison & Analysis**

- 7 Different Architectures Evaluated
- Current vs Historical Data Training
- Comprehensive Metrics Analysis
- January 25, 2025

---

## Slide 2: Model Overview

**Models Evaluated**

**Current Data Models (6):**
- Complex GATv2 (Temporal 3 & 5)
- Enhanced GATv2 (Temporal 3)
- GATv2 Standard (Temporal 3)
- GATv2 (Temporal 5)
- GATv2 5-Layer (Temporal 3)

**Historical Data Models (1):**
- GraphSAGE (Old Data)

---

## Slide 3: Performance Rankings - Current Data

| Rank | Model | Accuracy | F1 Score | ROC AUC | Parameters |
|------|-------|----------|----------|---------|------------|
| 1 | **Complex GATv2 (Temp 3)** | **72.84%** | 69.58% | **79.93%** | 169K |
| 2 | **Complex GATv2 (Temp 5)** | **70.03%** | 67.99% | 77.61% | 169K |
| 3 | Enhanced GATv2 (Temp 3) | 67.25% | **69.90%** | 71.85% | 6.0M |
| 4 | GATv2 Standard (Temp 3) | 66.17% | 69.31% | 69.48% | 25K |
| 5 | GATv2 (Temp 5) | 63.85% | 68.06% | 68.96% | 30K |
| 6 | GATv2 5-Layer (Temp 3) | 57.83% | 66.79% | 64.89% | 52K |

---

## Slide 4: Complete Performance Comparison

| Model | Accuracy | Precision | Recall | F1 Score | ROC AUC |
|-------|----------|-----------|--------|----------|---------|
| **GraphSAGE (Old Data)** | **73.04%** | **70.36%** | 89.32% | **78.72%** | 76.13% |
| **Complex GATv2 (Temp 3)** | **72.84%** | **71.05%** | 68.17% | 69.58% | **79.93%** |
| **Complex GATv2 (Temp 5)** | 70.03% | 66.93% | 69.08% | 67.99% | 77.61% |
| Enhanced GATv2 (Temp 3) | 67.25% | 60.14% | **83.46%** | 69.90% | 71.85% |
| GATv2 Standard (Temp 3) | 66.17% | 59.08% | 83.83% | 69.31% | 69.48% |
| GATv2 (Temp 5) | 63.85% | 57.39% | 83.59% | 68.06% | 68.96% |
| GATv2 5-Layer (Temp 3) | 57.83% | 52.09% | **93.06%** | 66.79% | 64.89% |

---

## Slide 5: Architecture Details

| Model | Layers | Hidden Dim | Attention Heads | Special Features |
|-------|--------|------------|-----------------|------------------|
| **Complex GATv2 (Temp 3)** | 4 | 128 | 8 | Layer+Batch Norm |
| **Complex GATv2 (Temp 5)** | 4 | 128 | 8 | Layer+Batch Norm |
| **Enhanced GATv2** | 4 | 192 | 8 | Residual, Self-Attention, Hierarchical Pooling |
| **GATv2 Standard** | 3 | 64 | 4 | Standard GATv2 |
| **GATv2 (Temp 5)** | 3 | 64 | 4 | Extended Temporal |
| **GATv2 5-Layer** | 5 | 64 | 4 | Deep Architecture |
| **GraphSAGE** | 3 | 64 | - | Simple Aggregation |

---

## Slide 6: Parameter Efficiency Analysis

| Model | Parameters | F1 Score | Efficiency (F1/1K params) |
|-------|------------|----------|---------------------------|
| **GraphSAGE (Old Data)** | 15K | 78.72% | **5.25** |
| **GATv2 Standard** | 25K | 69.31% | **2.77** |
| **GATv2 (Temp 5)** | 30K | 68.06% | **2.27** |
| GATv2 5-Layer | 52K | 66.79% | 1.29 |
| Complex GATv2 (Temp 3) | 169K | 69.58% | 0.41 |
| Complex GATv2 (Temp 5) | 169K | 67.99% | 0.40 |
| Enhanced GATv2 | 6.0M | 69.90% | 0.012 |

**Key Finding:** Simpler models achieve better parameter efficiency

---

## Slide 7: Training Efficiency

| Model | Training Time | Epochs | Accuracy | Time/Accuracy |
|-------|---------------|--------|----------|---------------|
| **GATv2 Standard** | 1h | 22 | 66.17% | **0.91 min/%** |
| **GATv2 5-Layer** | 1h | 21 | 57.83% | 1.04 min/% |
| **GATv2 (Temp 5)** | 1.5h | 32 | 63.85% | 1.41 min/% |
| **GraphSAGE** | 2h | 46 | 73.04% | 1.64 min/% |
| **Complex GATv2 (Temp 5)** | 2.5h | 59 | 70.03% | 2.14 min/% |
| **Complex GATv2 (Temp 3)** | 4h | 86 | 72.84% | 3.30 min/% |
| **Enhanced GATv2** | 6h | 11 | 67.25% | 5.35 min/% |

---

## Slide 8: Regression Performance

**Top 3 Models - Regression Metrics**

| Model | R² Score | MSE | RMSE | MAE |
|-------|----------|-----|------|-----|
| **Complex GATv2 (Temp 3)** | **0.2425** | **0.1879** | **0.4335** | **0.3954** |
| **Complex GATv2 (Temp 5)** | 0.2095 | 0.1964 | 0.4432 | 0.4132 |
| Enhanced GATv2 | 0.1465 | 0.2117 | 0.4601 | 0.4303 |

**Key Insight:** Complex GATv2 (Temp 3) dominates regression metrics

---

## Slide 9: Performance Leaders by Category

**🏆 Category Champions**

| Category | Winner | Score |
|----------|--------|-------|
| **Accuracy** | GraphSAGE (Old Data) | 73.04% |
| **Precision** | Complex GATv2 (Temp 3) | 71.05% |
| **Recall** | GATv2 5-Layer | 93.06% |
| **F1 Score** | GraphSAGE (Old Data) | 78.72% |
| **ROC AUC** | Complex GATv2 (Temp 3) | 79.93% |
| **Parameter Efficiency** | GraphSAGE (Old Data) | 5.25 F1/1K |
| **Training Speed** | GATv2 Standard | 1 hour |

---

## Slide 10: Temporal Window Analysis

**Impact of Temporal Window Size**

| Model Type | Temporal 3 | Temporal 5 | Difference |
|------------|------------|------------|------------|
| **Complex GATv2** | 72.84% acc | 70.03% acc | -2.81% |
| **GATv2 Standard** | 66.17% acc | 63.85% acc | -2.32% |
| **Training Epochs** | 86 epochs | 59 epochs | -31% faster |

**Finding:** Longer temporal windows reduce accuracy but improve training efficiency

---

## Slide 11: Model Complexity vs Performance

**Parameter Count vs F1 Score**

| Complexity | Model | Parameters | F1 Score | Trend |
|------------|-------|------------|----------|-------|
| **Simple** | GraphSAGE | 15K | 78.72% | ⬆️ Best |
| **Moderate** | GATv2 Standard | 25K | 69.31% | ➡️ Good |
| **Complex** | Complex GATv2 | 169K | 69.58% | ➡️ Similar |
| **Very Complex** | Enhanced GATv2 | 6.0M | 69.90% | ⬇️ Diminishing |

**Key Insight:** More parameters ≠ Better performance

---

## Slide 12: Recommendations

**Model Selection Guidelines**

| Use Case | Recommended Model | Rationale |
|----------|-------------------|-----------|
| **Production Deployment** | Complex GATv2 (Temp 3) | Best current data accuracy + ROC AUC |
| **Resource Constrained** | GraphSAGE | Highest parameter efficiency |
| **Safety Critical** | GATv2 5-Layer | Highest recall (93.06%) |
| **Research Applications** | Enhanced GATv2 | Advanced architectural features |
| **Fast Deployment** | GATv2 Standard | Quick training (1 hour) |
| **Temporal Analysis** | Complex GATv2 (Temp 5) | Extended temporal context |

---

## Slide 13: Key Findings

**Major Insights**

1. **Simplicity Wins:** GraphSAGE (15K params) outperforms Enhanced GATv2 (6M params)

2. **Attention Improves Discrimination:** GATv2 models achieve better ROC AUC

3. **Temporal Trade-offs:** Longer windows reduce accuracy but speed training

4. **Parameter Efficiency Matters:** Best models have 15K-169K parameters

5. **Data Quality Impact:** Historical vs current data significantly affects performance

---

## Slide 14: Summary

**Research Outcomes**

- **7 Models Evaluated** across different architectures
- **18 Hours Total Training** time investment
- **12.4M Parameters** evaluated across all models
- **Clear Performance Hierarchy** established

**Top Performers:**
- **Overall:** GraphSAGE (73.04% accuracy)
- **Current Data:** Complex GATv2 Temp 3 (72.84% accuracy)
- **Efficiency:** GraphSAGE (5.25 F1/1K parameters)

**Next Steps:** Model deployment and further optimization

---

## Slide 15: Questions & Discussion

**Thank you for your attention**

**Questions?**

- Model selection for specific applications
- Future research directions
- Implementation considerations
- Performance optimization strategies
