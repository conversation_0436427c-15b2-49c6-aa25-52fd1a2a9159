#!/bin/bash

# Create necessary directories
mkdir -p checkpoints_gatv2_temp3
mkdir -p visualizations_gatv2_temp3
mkdir -p logs

# Set environment variables
export PYTHONPATH=$PYTHONPATH:$(pwd)

# Run training
python3 main.py \
    --mode train \
    --config config_gatv2_temp3.yaml \
    --temporal_window 3 \
    --gnn_type gatv2

# Run evaluation after training
python3 main.py \
    --mode evaluate \
    --config config_gatv2_temp3.yaml \
    --temporal_window 3 \
    --gnn_type gatv2 