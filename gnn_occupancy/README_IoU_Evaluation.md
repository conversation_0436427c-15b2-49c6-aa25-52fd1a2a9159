# Comprehensive 2D IoU Evaluation System

A complete evaluation framework for comparing GNN occupancy prediction models using 2D Intersection over Union (IoU) metrics.

## 🚀 Quick Start

### 1. Run the Test Suite
First, verify that everything is working correctly:

```bash
python test_iou_evaluation.py
```

This will run a series of tests to ensure:
- Grid conversion works correctly
- Models can be discovered
- Test data can be loaded
- Visualization libraries are working
- A quick evaluation can be performed

### 2. Run the Full Evaluation
Once tests pass, run the complete evaluation:

```bash
python comprehensive_2d_iou_evaluation.py
```

This will:
- Discover all trained models in checkpoint directories
- Evaluate each model using 2D IoU metrics
- Generate comprehensive visualizations
- Create detailed reports and statistics
- Export results in multiple formats

## 📊 What You Get

### Generated Files
The evaluation creates a comprehensive set of outputs in `results/2d_iou_evaluation/`:

```
results/2d_iou_evaluation/
├── iou_summary_report.pdf              # Comprehensive PDF report
├── iou_detailed_results.csv            # Detailed results spreadsheet
├── summary_statistics.json             # Summary statistics
├── model_comparison_charts.png/.pdf    # Model comparison visualizations
├── spatial_analysis_heatmaps.png/.pdf  # Spatial IoU analysis
├── individual_analysis_*.png           # Individual model analyses
└── *_iou_results.json                  # Individual model results
```

### Key Visualizations

1. **Model Comparison Charts** (6 subplots):
   - Bar chart with mean IoU ± std dev
   - Box plots showing IoU distributions
   - Scatter plot: IoU vs model parameters
   - Temporal window comparison
   - Architecture comparison
   - IoU distribution histograms

2. **Spatial Analysis Heatmaps**:
   - Ground truth density maps
   - Spatial IoU performance maps
   - Identification of challenging spatial regions

3. **Individual Model Analysis** (for top 3 models):
   - IoU distribution histograms
   - Performance over samples
   - Failure vs success case analysis
   - Detailed performance statistics

### Key Metrics Reported

- **Mean IoU**: Average IoU across all test samples
- **Median IoU**: Median IoU (robust to outliers)
- **Standard Deviation**: Variability in performance
- **Min/Max IoU**: Range of performance
- **Parameter Efficiency**: IoU per parameter
- **Statistical Significance**: Comparisons between models

## ⚙️ Configuration

### Grid Configuration
- **Resolution**: Grid cell size (default: 0.1m = 10cm cells)
- **Padding**: Padding around data bounds (default: 0.5m)
- **Threshold**: Prediction threshold for binary classification (default: 0.5)

### Customization
Edit `iou_evaluation_config.yaml` to customize:
- Grid parameters
- Visualization settings
- Output formats
- Statistical analysis options
- Hardware utilization

## 🔧 System Requirements

### Dependencies
```bash
# Core dependencies
torch
torch-geometric
numpy
pandas
matplotlib
seaborn
scipy
scikit-learn
pyyaml
tqdm

# For PDF generation
matplotlib (with PDF backend)
```

### Hardware
- **GPU**: Recommended for faster model inference
- **RAM**: 8GB+ recommended for large datasets
- **Storage**: ~1GB for results and intermediate files

## 📈 Understanding the Results

### IoU Interpretation
- **IoU = 1.0**: Perfect prediction
- **IoU = 0.8+**: Excellent performance
- **IoU = 0.6-0.8**: Good performance
- **IoU = 0.4-0.6**: Moderate performance
- **IoU < 0.4**: Poor performance

### Model Ranking
Models are ranked by:
1. **Mean IoU**: Primary ranking metric
2. **Parameter Efficiency**: IoU per parameter
3. **Consistency**: Low standard deviation
4. **Robustness**: High minimum IoU

### Statistical Analysis
- **Mann-Whitney U Test**: Compares temporal windows
- **ANOVA**: Compares architectures
- **Confidence Intervals**: Uncertainty quantification

## 🐛 Troubleshooting

### Common Issues

1. **"No models found"**
   - Check checkpoint directory structure
   - Verify model file naming convention
   - Ensure config.yaml files exist

2. **"Data loading failed"**
   - Verify data directory path
   - Check temporal window data availability
   - Ensure proper file permissions

3. **"CUDA out of memory"**
   - Reduce batch size (keep at 1 for IoU)
   - Use CPU instead of GPU
   - Process models sequentially

4. **"Visualization errors"**
   - Check matplotlib backend
   - Verify seaborn installation
   - Try different plotting styles

### Debug Mode
Enable debug mode in config:
```yaml
debug:
  enable_debug_mode: true
  save_debug_info: true
  plot_sample_grids: true
```

## 🔬 Technical Details

### 2D Grid Conversion
1. **Bounds Calculation**: Determine min/max X,Y coordinates
2. **Grid Creation**: Create discrete grid with specified resolution
3. **Point Mapping**: Map continuous coordinates to grid cells
4. **Aggregation**: Use majority voting for ground truth, averaging for predictions
5. **Thresholding**: Apply threshold to create binary occupancy maps

### IoU Calculation
```
IoU = (Prediction ∩ Ground_Truth) / (Prediction ∪ Ground_Truth)
```

Special cases:
- Empty prediction + Empty ground truth = IoU = 1.0
- Empty union = IoU = 1.0 (both empty)

### Spatial Analysis
- Aggregates IoU performance across spatial regions
- Identifies challenging areas in the environment
- Compares spatial patterns between models

## 📚 Advanced Usage

### Custom Grid Resolutions
Test different resolutions:
```python
from comprehensive_2d_iou_evaluation import GridConfig

# Fine-grained analysis (5cm cells)
fine_config = GridConfig(resolution=0.05)

# Coarse-grained analysis (20cm cells)
coarse_config = GridConfig(resolution=0.2)
```

### Subset Evaluation
Evaluate specific models:
```python
evaluator = Comprehensive2DIoUEvaluator()
models = evaluator.discover_models()

# Filter models by architecture
graphsage_models = [m for m in models if 'graphsage' in m.architecture.lower()]

# Evaluate subset
for model in graphsage_models:
    results = evaluator.evaluate_model_iou(model)
```

### Custom Analysis
Extend the system:
```python
class CustomEvaluator(Comprehensive2DIoUEvaluator):
    def custom_analysis(self):
        # Add your custom analysis here
        pass
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Run the test suite to identify problems
3. Enable debug mode for detailed logging
4. Review the generated error messages

## 🎯 Expected Results

Based on typical GNN performance:
- **Best models**: IoU 0.7-0.9
- **Good models**: IoU 0.5-0.7
- **Baseline models**: IoU 0.3-0.5

The evaluation will identify:
- Best performing architecture
- Optimal temporal window
- Most parameter-efficient models
- Spatial performance patterns
- Statistical significance of differences
