#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import argparse
from tqdm import tqdm
import seaborn as sns

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])

def generate_confusion_matrix(model_dir, temporal_window):
    """
    Generate and save a confusion matrix for a trained model.
    
    Args:
        model_dir: Directory containing the model checkpoint and config
        temporal_window: Size of temporal window
    """
    # Load configuration
    config_path = os.path.join(model_dir, "config.yaml")
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    
    # Set device
    device = torch.device(config["training"]["device"])
    
    # Import necessary modules
    import sys
    sys.path.append('/home/<USER>/ma_yugi/gnn_occupancy')
    from data import create_data_loaders
    from model import create_model
    
    # Create data loaders
    # Update config to use only the specified temporal window
    config_copy = config.copy()
    config_copy["data"]["temporal_windows"] = [temporal_window]
    data_loaders = create_data_loaders(config_copy)
    
    # Print available keys for debugging
    print(f"Available data loader keys: {list(data_loaders.keys())}")
    
    # Find the test loader key
    test_key = None
    for key in data_loaders.keys():
        if key.startswith("test") or "test" in key:
            if str(temporal_window) in key or f"temporal_{temporal_window}" in key:
                test_key = key
                break
    
    if test_key is None:
        # Try to find any test loader
        for key in data_loaders.keys():
            if key.startswith("test") or "test" in key:
                test_key = key
                break
    
    if test_key is None:
        # Use any available loader
        test_key = list(data_loaders.keys())[0]
        print(f"Warning: No test loader found. Using {test_key} instead.")
    
    print(f"Using data loader with key: {test_key}")
    test_loader = data_loaders[test_key]
    
    # Create model
    model = create_model(config_copy)
    
    # Load checkpoint
    checkpoint_path = os.path.join(
        config["training"]["checkpoint_dir"],
        f"model_temporal_{temporal_window}_best.pt"
    )
    
    if not os.path.exists(checkpoint_path):
        # Try to find the checkpoint file
        checkpoint_dir = config["training"]["checkpoint_dir"]
        if os.path.exists(checkpoint_dir):
            checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith(".pt")]
            if checkpoint_files:
                checkpoint_path = os.path.join(checkpoint_dir, checkpoint_files[0])
                print(f"Using checkpoint: {checkpoint_path}")
            else:
                print(f"No checkpoint files found in {checkpoint_dir}")
                return
        else:
            print(f"Checkpoint directory {checkpoint_dir} does not exist")
            return
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint["model_state_dict"])
    
    # Move model to device
    model = model.to(device)
    
    # Set model to evaluation mode
    model.eval()
    
    # Collect predictions and targets
    all_targets = []
    all_preds = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc=f"Evaluating temporal_{temporal_window}"):
            # Move batch to device
            batch = batch.to(device)
            
            # Forward pass
            outputs = model(batch)
            
            # Get predictions
            probs = torch.sigmoid(outputs).cpu().numpy()
            preds = (probs > 0.5).astype(int)
            
            # Get targets
            targets = batch.y.cpu().numpy()
            
            # Append to lists
            all_targets.extend(targets)
            all_preds.extend(preds)
    
    # Convert to numpy arrays
    all_targets = np.array(all_targets)
    all_preds = np.array(all_preds)
    
    # Create confusion matrix
    cm = confusion_matrix(all_targets, all_preds)
    
    # Create directory for visualizations if it doesn't exist
    vis_dir = os.path.join(model_dir, "visualizations")
    os.makedirs(vis_dir, exist_ok=True)
    
    # Plot confusion matrix
    plt.figure(figsize=(10, 8))
    
    # Use seaborn for a nicer heatmap
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['Unoccupied', 'Occupied'],
                yticklabels=['Unoccupied', 'Occupied'])
    
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title(f'Confusion Matrix (Temporal Window: {temporal_window})')
    
    # Save the figure
    cm_path = os.path.join(vis_dir, f"confusion_matrix_temporal_{temporal_window}.png")
    plt.savefig(cm_path, dpi=300, bbox_inches='tight')
    
    # Also save to the main directory for easy access
    cm_path_main = os.path.join(model_dir, f"confusion_matrix_temporal_{temporal_window}.png")
    plt.savefig(cm_path_main, dpi=300, bbox_inches='tight')
    
    print(f"Confusion matrix saved to {cm_path} and {cm_path_main}")
    
    # Calculate and print metrics
    tn, fp, fn, tp = cm.ravel()
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    print(f"\nConfusion Matrix:\n{cm}")
    print(f"\nMetrics:")
    print(f"  Accuracy:  {accuracy:.4f}")
    print(f"  Precision: {precision:.4f}")
    print(f"  Recall:    {recall:.4f}")
    print(f"  F1 Score:  {f1:.4f}")
    
    # Return the confusion matrix
    return cm

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate confusion matrix for a trained model")
    parser.add_argument(
        "--model_dir",
        type=str,
        required=True,
        help="Directory containing the model checkpoint and config",
    )
    parser.add_argument(
        "--temporal_window",
        type=int,
        required=True,
        help="Size of temporal window",
    )
    
    args = parser.parse_args()
    
    cm = generate_confusion_matrix(args.model_dir, args.temporal_window)
