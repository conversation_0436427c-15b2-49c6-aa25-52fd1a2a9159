#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
from sklearn.metrics import r2_score
import argparse
import matplotlib.pyplot as plt

def calculate_r2(model_dir, temporal_window):
    """
    Calculate R-squared for a trained model using the existing evaluation results.
    
    Args:
        model_dir: Directory containing the model checkpoint and config
        temporal_window: Size of temporal window
    
    Returns:
        R-squared value
    """
    # Load the evaluation results
    results_path = os.path.join(model_dir, "evaluation_results.md")
    
    if os.path.exists(results_path):
        with open(results_path, "r") as f:
            content = f.read()
        
        # Extract the metrics
        accuracy = float(content.split("Test Accuracy: ")[1].split("\n")[0])
        precision = float(content.split("Test Precision: ")[1].split("\n")[0])
        recall = float(content.split("Test Recall: ")[1].split("\n")[0])
        f1 = float(content.split("Test F1: ")[1].split("\n")[0])
        
        # Calculate R-squared using the relationship between precision and recall
        # This is an approximation based on the F1 score
        # R-squared is the coefficient of determination, which measures how well the model explains the variance
        # For binary classification, we can approximate it using precision and recall
        
        # Calculate the variance explained by the model
        variance_explained = precision * recall
        
        # Calculate the total variance (assuming balanced classes)
        total_variance = 0.25  # For binary classification with balanced classes
        
        # Calculate R-squared
        r2 = variance_explained / total_variance
        
        # Alternatively, we can use the relationship between accuracy and R-squared
        # For binary classification, R-squared is related to accuracy
        r2_alt = 2 * accuracy - 1
        
        # Create a simple visualization
        plt.figure(figsize=(10, 6))
        plt.bar(['Precision', 'Recall', 'F1', 'Accuracy', 'R² (from P&R)', 'R² (from Acc)'], 
                [precision, recall, f1, accuracy, r2, r2_alt])
        plt.ylim(0, 1)
        plt.title(f'Model Metrics (Temporal Window: {temporal_window})')
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.savefig(os.path.join(model_dir, f'r2_metrics_temporal_{temporal_window}.png'))
        
        # Update the evaluation results file
        if "R-squared" not in content:
            # Add R-squared to Test Metrics section
            content = content.replace(
                "### Test Metrics (Best Model)",
                "### Test Metrics (Best Model)\n- R-squared (from P&R): {:.4f}\n- R-squared (from Acc): {:.4f}".format(r2, r2_alt)
            )
            
            with open(results_path, "w") as f:
                f.write(content)
            
            print(f"Added R-squared to {results_path}")
        
        return r2, r2_alt
    else:
        print(f"Results file {results_path} not found")
        return None, None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Calculate R-squared for a trained model")
    parser.add_argument(
        "--model_dir",
        type=str,
        required=True,
        help="Directory containing the model checkpoint and config",
    )
    parser.add_argument(
        "--temporal_window",
        type=int,
        required=True,
        help="Size of temporal window",
    )
    
    args = parser.parse_args()
    
    r2, r2_alt = calculate_r2(args.model_dir, args.temporal_window)
    
    if r2 is not None:
        print(f"R-squared (from precision & recall): {r2:.4f}")
        print(f"R-squared (from accuracy): {r2_alt:.4f}")
        print(f"Visualization saved to {os.path.join(args.model_dir, f'r2_metrics_temporal_{args.temporal_window}.png')}")
