#!/usr/bin/env python3
"""
Step 3: Advanced Interactive Visualizations
Create interactive visualizations with layer toggles, zoom capabilities, and detailed statistics.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
from datetime import datetime
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.patches import Rectangle, Circle
import matplotlib.gridspec as gridspec
from matplotlib.widgets import CheckButtons, Button
from scipy.spatial import ConvexHull
from scipy.ndimage import gaussian_filter
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


class AdvancedArenaVisualizer:
    """Advanced interactive arena visualization creator."""
    
    def __init__(self, results_dir: str = "results/arena_characterization"):
        self.results_dir = Path(results_dir)
        self.viz_dir = self.results_dir / "interactive_visualizations"
        self.viz_dir.mkdir(parents=True, exist_ok=True)
        
        # Load analysis results
        self.arena_chars = None
        self.spatial_grid = None
        self.density_grid = None
        self.occupancy_grid = None
        self.all_positions = None
        self.all_labels = None
        
        self._load_analysis_results()
        self._analyze_arena_features()
        
    def _load_analysis_results(self):
        """Load results from previous steps."""
        print("📂 Loading analysis results...")
        
        # Load arena characteristics
        chars_file = self.results_dir / 'arena_characteristics.json'
        with open(chars_file, 'r') as f:
            self.arena_chars = json.load(f)
        
        # Load grids and data
        self.spatial_grid = np.load(self.results_dir / 'spatial_coverage_grid.npy')
        self.density_grid = np.load(self.results_dir / 'density_grid.npy')
        self.occupancy_grid = np.load(self.results_dir / 'occupancy_grid.npy')
        self.all_positions = np.load(self.results_dir / 'all_positions.npy')
        self.all_labels = np.load(self.results_dir / 'all_labels.npy')
        
        print("  ✅ All analysis results loaded")
    
    def _analyze_arena_features(self):
        """Analyze arena features to identify workstations, boundaries, and paths."""
        print("🔍 Analyzing arena features...")
        
        # Identify high-occupancy regions (potential workstations)
        occupancy_threshold = 0.8
        high_occ_mask = self.occupancy_grid > occupancy_threshold
        
        # Identify boundaries (edges of coverage)
        from scipy.ndimage import binary_erosion
        coverage_eroded = binary_erosion(self.spatial_grid, iterations=2)
        boundary_mask = self.spatial_grid & ~coverage_eroded
        
        # Identify robot paths (high density, low occupancy)
        density_normalized = self.density_grid / np.max(self.density_grid)
        path_mask = (density_normalized > 0.3) & (self.occupancy_grid < 0.2)
        
        # Store feature masks
        self.workstation_mask = high_occ_mask
        self.boundary_mask = boundary_mask
        self.path_mask = path_mask
        
        # Calculate feature statistics
        self.feature_stats = {
            'workstation_area': np.sum(high_occ_mask) * (self.arena_chars['grid_properties']['resolution'] ** 2),
            'boundary_length': np.sum(boundary_mask) * self.arena_chars['grid_properties']['resolution'],
            'path_area': np.sum(path_mask) * (self.arena_chars['grid_properties']['resolution'] ** 2),
            'total_coverage_area': np.sum(self.spatial_grid) * (self.arena_chars['grid_properties']['resolution'] ** 2)
        }
        
        print(f"  ✅ Identified {np.sum(high_occ_mask)} workstation cells")
        print(f"  ✅ Identified {np.sum(boundary_mask)} boundary cells")
        print(f"  ✅ Identified {np.sum(path_mask)} path cells")
    
    def create_interactive_plotly_visualization(self):
        """Create interactive Plotly visualization with layer toggles."""
        print("🎯 Creating interactive Plotly visualization...")
        
        # Prepare coordinate arrays
        extent = [
            self.arena_chars['spatial_dimensions']['min_x'],
            self.arena_chars['spatial_dimensions']['max_x'],
            self.arena_chars['spatial_dimensions']['min_y'],
            self.arena_chars['spatial_dimensions']['max_y']
        ]
        
        # Create coordinate grids
        x_coords = np.linspace(extent[0], extent[1], self.occupancy_grid.shape[1])
        y_coords = np.linspace(extent[2], extent[3], self.occupancy_grid.shape[0])
        
        # Create subplots with pie chart specification
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Occupancy Map with Features', 'Sample Density',
                          'Data Coverage', 'Feature Analysis'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"type": "pie"}]]
        )
        
        # 1. Main occupancy map with feature overlays
        fig.add_trace(
            go.Heatmap(
                z=self.occupancy_grid,
                x=x_coords,
                y=y_coords,
                colorscale='RdYlGn_r',
                name='Occupancy',
                hovertemplate='X: %{x:.2f}m<br>Y: %{y:.2f}m<br>Occupancy: %{z:.3f}<extra></extra>',
                zmin=0, zmax=1
            ),
            row=1, col=1
        )
        
        # Add workstation overlay
        workstation_overlay = np.where(self.workstation_mask, 1, np.nan)
        fig.add_trace(
            go.Heatmap(
                z=workstation_overlay,
                x=x_coords,
                y=y_coords,
                colorscale=[[0, 'rgba(0,0,0,0)'], [1, 'rgba(255,0,0,0.5)']],
                name='Workstations',
                showscale=False,
                hovertemplate='Workstation Area<extra></extra>'
            ),
            row=1, col=1
        )
        
        # Add path overlay
        path_overlay = np.where(self.path_mask, 1, np.nan)
        fig.add_trace(
            go.Heatmap(
                z=path_overlay,
                x=x_coords,
                y=y_coords,
                colorscale=[[0, 'rgba(0,0,0,0)'], [1, 'rgba(0,0,255,0.3)']],
                name='Robot Paths',
                showscale=False,
                hovertemplate='Robot Path<extra></extra>'
            ),
            row=1, col=1
        )
        
        # 2. Sample density
        density_log = np.log1p(self.density_grid)
        fig.add_trace(
            go.Heatmap(
                z=density_log,
                x=x_coords,
                y=y_coords,
                colorscale='Viridis',
                name='Density',
                hovertemplate='X: %{x:.2f}m<br>Y: %{y:.2f}m<br>Log Density: %{z:.2f}<extra></extra>'
            ),
            row=1, col=2
        )
        
        # 3. Data coverage
        fig.add_trace(
            go.Heatmap(
                z=self.spatial_grid.astype(float),
                x=x_coords,
                y=y_coords,
                colorscale='Blues',
                name='Coverage',
                hovertemplate='X: %{x:.2f}m<br>Y: %{y:.2f}m<br>Covered: %{z}<extra></extra>'
            ),
            row=2, col=1
        )
        
        # 4. Feature analysis pie chart
        feature_areas = [
            self.feature_stats['workstation_area'],
            self.feature_stats['path_area'],
            self.feature_stats['total_coverage_area'] - self.feature_stats['workstation_area'] - self.feature_stats['path_area']
        ]
        
        fig.add_trace(
            go.Pie(
                labels=['Workstations', 'Robot Paths', 'Other Areas'],
                values=feature_areas,
                name='Feature Distribution',
                hovertemplate='%{label}: %{value:.2f}m²<br>%{percent}<extra></extra>'
            ),
            row=2, col=2
        )
        
        # Update layout
        fig.update_layout(
            title=f'Interactive Arena Analysis - {self.arena_chars["spatial_dimensions"]["width"]:.1f}m × {self.arena_chars["spatial_dimensions"]["height"]:.1f}m Arena',
            height=800,
            showlegend=True
        )
        
        # Update axes
        for i in range(1, 3):
            for j in range(1, 3):
                if not (i == 2 and j == 2):  # Skip pie chart
                    fig.update_xaxes(title_text="X Position (m)", row=i, col=j)
                    fig.update_yaxes(title_text="Y Position (m)", row=i, col=j)
        
        # Save interactive plot
        interactive_path = self.viz_dir / 'interactive_arena_analysis.html'
        fig.write_html(str(interactive_path))
        
        print(f"  ✅ Saved interactive visualization: {interactive_path}")
        
    def create_high_resolution_publication_figure(self):
        """Create high-resolution figure suitable for publication."""
        print("📊 Creating high-resolution publication figure...")
        
        # Set up publication-quality figure
        plt.style.use('default')
        fig = plt.figure(figsize=(16, 12))
        gs = gridspec.GridSpec(3, 3, figure=fig, hspace=0.4, wspace=0.4)
        
        # Main arena map (spans 2x2)
        ax_main = fig.add_subplot(gs[:2, :2])
        self._create_main_arena_plot(ax_main)
        
        # Feature analysis plots
        ax_features = fig.add_subplot(gs[0, 2])
        self._create_feature_analysis_plot(ax_features)
        
        ax_stats = fig.add_subplot(gs[1, 2])
        self._create_statistics_table(ax_stats)
        
        ax_coverage = fig.add_subplot(gs[2, 0])
        self._create_coverage_analysis(ax_coverage)
        
        ax_density = fig.add_subplot(gs[2, 1])
        self._create_density_profile(ax_density)
        
        ax_occupancy = fig.add_subplot(gs[2, 2])
        self._create_occupancy_histogram(ax_occupancy)
        
        # Add main title
        fig.suptitle('Comprehensive Arena Characterization\nRobotic Environment Analysis', 
                    fontsize=16, fontweight='bold', y=0.95)
        
        # Save high-resolution figure
        pub_path = self.viz_dir / 'publication_arena_analysis.png'
        plt.savefig(pub_path, dpi=600, bbox_inches='tight', facecolor='white')
        pub_pdf_path = self.viz_dir / 'publication_arena_analysis.pdf'
        plt.savefig(pub_pdf_path, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"  ✅ Saved publication figure: {pub_path}")
    
    def _create_main_arena_plot(self, ax):
        """Create the main arena plot with all features."""
        extent = [
            self.arena_chars['spatial_dimensions']['min_x'],
            self.arena_chars['spatial_dimensions']['max_x'],
            self.arena_chars['spatial_dimensions']['min_y'],
            self.arena_chars['spatial_dimensions']['max_y']
        ]
        
        # Base occupancy map
        im = ax.imshow(self.occupancy_grid, extent=extent, origin='lower', 
                      cmap='RdYlGn_r', alpha=0.8, vmin=0, vmax=1)
        
        # Overlay workstations
        workstation_overlay = np.ma.masked_where(~self.workstation_mask, 
                                               np.ones_like(self.workstation_mask))
        ax.imshow(workstation_overlay, extent=extent, origin='lower', 
                 cmap='Reds', alpha=0.6, vmin=0, vmax=1)
        
        # Overlay robot paths
        path_overlay = np.ma.masked_where(~self.path_mask, 
                                        np.ones_like(self.path_mask))
        ax.imshow(path_overlay, extent=extent, origin='lower', 
                 cmap='Blues', alpha=0.4, vmin=0, vmax=1)
        
        # Add arena boundary
        width = self.arena_chars['spatial_dimensions']['width']
        height = self.arena_chars['spatial_dimensions']['height']
        min_x = self.arena_chars['spatial_dimensions']['min_x']
        min_y = self.arena_chars['spatial_dimensions']['min_y']
        
        boundary = Rectangle((min_x, min_y), width, height, 
                           linewidth=3, edgecolor='black', facecolor='none')
        ax.add_patch(boundary)
        
        # Add scale bar
        scale_length = 2.0
        scale_x = min_x + width * 0.05
        scale_y = min_y + height * 0.05
        ax.plot([scale_x, scale_x + scale_length], [scale_y, scale_y], 
               'k-', linewidth=4)
        ax.text(scale_x + scale_length/2, scale_y - height*0.03, 
               f'{scale_length}m', ha='center', fontweight='bold', fontsize=12)
        
        # Formatting
        ax.set_xlabel('X Position (m)', fontsize=12)
        ax.set_ylabel('Y Position (m)', fontsize=12)
        ax.set_title('Arena Layout with Feature Identification', fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('Occupancy Probability', fontsize=12)
        
        # Add legend
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='red', alpha=0.6, label='Workstations'),
            Patch(facecolor='blue', alpha=0.4, label='Robot Paths'),
            Patch(facecolor='none', edgecolor='black', linewidth=2, label='Arena Boundary')
        ]
        ax.legend(handles=legend_elements, loc='upper right')
    
    def _create_feature_analysis_plot(self, ax):
        """Create feature analysis pie chart."""
        areas = [
            self.feature_stats['workstation_area'],
            self.feature_stats['path_area'],
            self.feature_stats['total_coverage_area'] - self.feature_stats['workstation_area'] - self.feature_stats['path_area']
        ]
        
        labels = ['Workstations', 'Robot Paths', 'Other Areas']
        colors = ['red', 'blue', 'lightgray']
        
        wedges, texts, autotexts = ax.pie(areas, labels=labels, colors=colors, 
                                         autopct='%1.1f%%', startangle=90)
        
        ax.set_title('Arena Feature Distribution', fontweight='bold')
        
    def _create_statistics_table(self, ax):
        """Create comprehensive statistics table."""
        stats_data = [
            ['Arena Statistics', ''],
            ['Dimensions', f"{self.arena_chars['spatial_dimensions']['width']:.1f} × {self.arena_chars['spatial_dimensions']['height']:.1f} m"],
            ['Total Area', f"{self.arena_chars['spatial_dimensions']['total_area']:.1f} m²"],
            ['Coverage', f"{self.arena_chars['data_coverage']['spatial_coverage_percentage']:.1f}%"],
            ['', ''],
            ['Feature Areas', ''],
            ['Workstations', f"{self.feature_stats['workstation_area']:.1f} m²"],
            ['Robot Paths', f"{self.feature_stats['path_area']:.1f} m²"],
            ['Operational Area', f"{self.feature_stats['total_coverage_area']:.1f} m²"],
            ['', ''],
            ['Data Quality', ''],
            ['Total Samples', f"{self.arena_chars['data_coverage']['total_samples']:,}"],
            ['Sample Density', f"{self.arena_chars['data_coverage']['samples_per_square_meter']:.1f}/m²"],
            ['Occupancy Ratio', f"{self.arena_chars['occupancy_statistics']['occupancy_ratio']*100:.1f}%"]
        ]
        
        table = ax.table(cellText=stats_data, cellLoc='left', loc='center',
                        colWidths=[0.6, 0.4])
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1.0, 1.3)
        
        # Style headers
        for i, row in enumerate(stats_data):
            if row[1] == '':
                table[(i, 0)].set_facecolor('#4472C4')
                table[(i, 0)].set_text_props(weight='bold', color='white')
                table[(i, 1)].set_facecolor('#4472C4')
        
        ax.axis('off')
        ax.set_title('Arena Statistics', fontweight='bold')
    
    def _create_coverage_analysis(self, ax):
        """Create coverage analysis plot."""
        coverage_percentage = self.arena_chars['data_coverage']['spatial_coverage_percentage']
        uncovered_percentage = 100 - coverage_percentage
        
        ax.pie([coverage_percentage, uncovered_percentage], 
               labels=['Covered', 'Uncovered'],
               colors=['lightblue', 'lightgray'],
               autopct='%1.1f%%',
               startangle=90)
        ax.set_title('Spatial Coverage', fontweight='bold')
    
    def _create_density_profile(self, ax):
        """Create density profile analysis."""
        # Calculate density along X and Y axes
        x_profile = np.mean(self.density_grid, axis=0)
        y_profile = np.mean(self.density_grid, axis=1)
        
        x_coords = np.linspace(self.arena_chars['spatial_dimensions']['min_x'],
                              self.arena_chars['spatial_dimensions']['max_x'],
                              len(x_profile))
        y_coords = np.linspace(self.arena_chars['spatial_dimensions']['min_y'],
                              self.arena_chars['spatial_dimensions']['max_y'],
                              len(y_profile))
        
        ax.plot(x_coords, x_profile, 'b-', label='X-axis profile', linewidth=2)
        ax.set_xlabel('Position (m)')
        ax.set_ylabel('Average Sample Density')
        ax.set_title('Density Profiles', fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend()
    
    def _create_occupancy_histogram(self, ax):
        """Create occupancy distribution histogram."""
        valid_occupancy = self.occupancy_grid[self.occupancy_grid > 0]
        
        if len(valid_occupancy) > 0:
            ax.hist(valid_occupancy, bins=20, alpha=0.7, color='skyblue', 
                   edgecolor='black', density=True)
            
            mean_occ = np.mean(valid_occupancy)
            ax.axvline(mean_occ, color='red', linestyle='--', linewidth=2, 
                      label=f'Mean: {mean_occ:.3f}')
            
            ax.set_xlabel('Occupancy Probability')
            ax.set_ylabel('Density')
            ax.set_title('Occupancy Distribution', fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)
    
    def generate_comprehensive_report(self):
        """Generate comprehensive arena characterization report."""
        print("📄 Generating comprehensive report...")
        
        report = {
            'arena_characterization_report': {
                'generation_date': datetime.now().isoformat(),
                'arena_dimensions': {
                    'width_m': self.arena_chars['spatial_dimensions']['width'],
                    'height_m': self.arena_chars['spatial_dimensions']['height'],
                    'total_area_m2': self.arena_chars['spatial_dimensions']['total_area'],
                    'bounds': {
                        'x_min': self.arena_chars['spatial_dimensions']['min_x'],
                        'x_max': self.arena_chars['spatial_dimensions']['max_x'],
                        'y_min': self.arena_chars['spatial_dimensions']['min_y'],
                        'y_max': self.arena_chars['spatial_dimensions']['max_y']
                    }
                },
                'feature_analysis': {
                    'workstation_area_m2': self.feature_stats['workstation_area'],
                    'robot_path_area_m2': self.feature_stats['path_area'],
                    'operational_area_m2': self.feature_stats['total_coverage_area'],
                    'workstation_percentage': (self.feature_stats['workstation_area'] / self.feature_stats['total_coverage_area']) * 100,
                    'path_percentage': (self.feature_stats['path_area'] / self.feature_stats['total_coverage_area']) * 100
                },
                'data_quality_assessment': {
                    'total_samples': self.arena_chars['data_coverage']['total_samples'],
                    'unique_positions': self.arena_chars['data_coverage']['unique_positions'],
                    'spatial_coverage_percentage': self.arena_chars['data_coverage']['spatial_coverage_percentage'],
                    'sample_density_per_m2': self.arena_chars['data_coverage']['samples_per_square_meter'],
                    'well_sampled_regions': self.arena_chars['data_quality']['well_sampled_regions'],
                    'under_sampled_regions': self.arena_chars['data_quality']['under_sampled_regions']
                },
                'occupancy_statistics': {
                    'overall_occupancy_ratio': self.arena_chars['occupancy_statistics']['occupancy_ratio'],
                    'occupied_samples': self.arena_chars['occupancy_statistics']['occupied_samples'],
                    'free_samples': self.arena_chars['occupancy_statistics']['free_samples']
                },
                'technical_specifications': {
                    'grid_resolution_m': self.arena_chars['grid_properties']['resolution'],
                    'grid_dimensions': f"{self.arena_chars['grid_properties']['grid_width']} × {self.arena_chars['grid_properties']['grid_height']}",
                    'total_grid_cells': self.arena_chars['grid_properties']['total_cells'],
                    'coordinate_system': 'Real-world meters',
                    'temporal_window': 3
                }
            }
        }
        
        # Save comprehensive report
        report_path = self.viz_dir / 'comprehensive_arena_report.json'
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"  ✅ Saved comprehensive report: {report_path}")
        
    def run_step3_advanced_visualization(self):
        """Run complete Step 3 advanced visualization creation."""
        print("🚀 Starting Step 3: Advanced Interactive Visualizations")
        print("=" * 70)
        
        try:
            # Create interactive Plotly visualization
            self.create_interactive_plotly_visualization()
            
            # Create high-resolution publication figure
            self.create_high_resolution_publication_figure()
            
            # Generate comprehensive report
            self.generate_comprehensive_report()
            
            print("\n" + "=" * 70)
            print("🎉 STEP 3 COMPLETED SUCCESSFULLY!")
            print("=" * 70)
            print(f"📁 Advanced visualizations saved to: {self.viz_dir}")
            print("🔄 Ready for Step 4: Final Integration and Summary")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Step 3 failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function for Step 3."""
    print("🔬 Arena Characterization System - Step 3")
    print("🎯 Advanced Interactive Visualizations")
    print("=" * 80)
    
    visualizer = AdvancedArenaVisualizer()
    success = visualizer.run_step3_advanced_visualization()
    
    if success:
        print("\n✅ Step 3 completed! Proceed to Step 4 for final integration.")
    else:
        print("\n❌ Step 3 failed. Check the error messages above.")
    
    return success


if __name__ == "__main__":
    main()
