#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])

from data import create_data_loaders
from model import create_model


class FrameByFrameSpatialAnalyzer:
    """Analyze spatial distribution frame by frame for temporal models."""
    
    def __init__(self, config: Dict, model_name: str, checkpoint_path: str, output_dir: str):
        self.config = config
        self.model_name = model_name
        self.checkpoint_path = checkpoint_path
        self.output_dir = output_dir
        self.device = torch.device(config["training"]["device"])
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Set plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        plt.rcParams['figure.dpi'] = 300
        plt.rcParams['savefig.dpi'] = 300
        
    def load_model_and_data(self, temporal_window: int):
        """Load model and test data."""
        print(f"Loading model and test data for temporal window {temporal_window}...")
        
        # Create data loaders
        data_loaders = create_data_loaders(self.config)
        test_loader = data_loaders[f"temporal_{temporal_window}"]["test"]
        
        # Create and load model
        model = create_model(self.config)
        checkpoint = torch.load(self.checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint["model_state_dict"])
        model = model.to(self.device)
        model.eval()
        
        return model, test_loader, checkpoint
    
    def extract_frame_data(self, model, test_loader, temporal_window: int, max_frames: int = 50):
        """Extract frame-by-frame data for spatial analysis."""
        print(f"Extracting frame-by-frame data for {max_frames} frames...")
        
        frame_data = []
        frames_collected = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="Processing batches")):
                if frames_collected >= max_frames:
                    break
                    
                batch = batch.to(self.device)
                
                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)
                probabilities = torch.sigmoid(logits)
                predictions = probabilities > 0.5
                
                # Process each graph in the batch
                for graph_idx in range(batch.num_graphs):
                    if frames_collected >= max_frames:
                        break
                        
                    # Get node mask for this graph
                    node_mask = batch.batch == graph_idx
                    
                    # Extract graph data
                    graph_positions = batch.pos[node_mask].cpu().numpy()
                    graph_targets = batch.y[node_mask].cpu().numpy()
                    
                    # Get predictions for this graph
                    if logits.size(0) == batch.y.size(0):
                        # Node-level predictions
                        graph_probs = probabilities[node_mask].cpu().numpy()
                        graph_preds = predictions[node_mask].cpu().numpy()
                    else:
                        # Graph-level predictions
                        if graph_idx < logits.size(0):
                            single_prob = probabilities[graph_idx].item()
                            single_pred = predictions[graph_idx].item()
                        else:
                            single_prob = probabilities.mean().item()
                            single_pred = (single_prob > 0.5)
                        
                        graph_probs = np.full(graph_targets.shape, single_prob)
                        graph_preds = np.full(graph_targets.shape, single_pred)
                    
                    # Store frame data
                    frame_info = {
                        'frame_id': frames_collected,
                        'batch_id': batch_idx,
                        'graph_id': graph_idx,
                        'positions': graph_positions,
                        'true_labels': graph_targets,
                        'predicted_labels': graph_preds.astype(int),
                        'probabilities': graph_probs,
                        'num_points': len(graph_positions),
                        'accuracy': np.mean(graph_targets == graph_preds.astype(int)),
                        'mean_confidence': np.abs(graph_probs - 0.5).mean() * 2
                    }
                    
                    frame_data.append(frame_info)
                    frames_collected += 1
        
        print(f"Collected data for {len(frame_data)} frames")
        return frame_data
    
    def create_frame_by_frame_visualization(self, frame_data: List[Dict], temporal_window: int, 
                                          frames_per_row: int = 5, max_frames: int = 30):
        """Create comprehensive frame-by-frame spatial visualization."""
        print(f"Creating frame-by-frame visualization for {min(len(frame_data), max_frames)} frames...")
        
        # Limit frames for visualization
        frames_to_show = min(len(frame_data), max_frames)
        selected_frames = frame_data[:frames_to_show]
        
        # Calculate grid dimensions
        rows = (frames_to_show + frames_per_row - 1) // frames_per_row
        
        # Create figure with subplots for true vs predicted comparison
        fig, axes = plt.subplots(rows * 2, frames_per_row, figsize=(frames_per_row * 4, rows * 6))
        
        # Handle single row case
        if rows == 1:
            axes = axes.reshape(2, -1)
        elif frames_per_row == 1:
            axes = axes.reshape(-1, 1)
        
        # Get global position bounds for consistent scaling
        all_positions = np.vstack([frame['positions'] for frame in selected_frames])
        x_min, x_max = all_positions[:, 0].min(), all_positions[:, 0].max()
        y_min, y_max = all_positions[:, 1].min(), all_positions[:, 1].max()
        
        # Add padding
        x_padding = (x_max - x_min) * 0.1
        y_padding = (y_max - y_min) * 0.1
        x_min -= x_padding
        x_max += x_padding
        y_min -= y_padding
        y_max += y_padding
        
        for frame_idx, frame in enumerate(selected_frames):
            row = (frame_idx // frames_per_row) * 2
            col = frame_idx % frames_per_row
            
            positions = frame['positions']
            true_labels = frame['true_labels']
            pred_labels = frame['predicted_labels']
            probabilities = frame['probabilities']
            
            # True labels subplot (top row)
            if row < axes.shape[0] and col < axes.shape[1]:
                ax_true = axes[row, col]
                scatter_true = ax_true.scatter(positions[:, 0], positions[:, 1], 
                                             c=true_labels, cmap='RdYlBu', 
                                             s=50, alpha=0.8, vmin=0, vmax=1)
                ax_true.set_xlim(x_min, x_max)
                ax_true.set_ylim(y_min, y_max)
                ax_true.set_title(f'Frame {frame["frame_id"]} - True Labels\n'
                                f'Points: {frame["num_points"]}, Acc: {frame["accuracy"]:.3f}', 
                                fontsize=10)
                ax_true.set_xlabel('X Position')
                ax_true.set_ylabel('Y Position')
                ax_true.grid(True, alpha=0.3)
                
                # Add colorbar for first frame
                if frame_idx == 0:
                    plt.colorbar(scatter_true, ax=ax_true, label='True Label')
            
            # Predicted labels subplot (bottom row)
            if row + 1 < axes.shape[0] and col < axes.shape[1]:
                ax_pred = axes[row + 1, col]
                scatter_pred = ax_pred.scatter(positions[:, 0], positions[:, 1], 
                                             c=pred_labels, cmap='RdYlBu', 
                                             s=50, alpha=0.8, vmin=0, vmax=1)
                ax_pred.set_xlim(x_min, x_max)
                ax_pred.set_ylim(y_min, y_max)
                ax_pred.set_title(f'Frame {frame["frame_id"]} - Predictions\n'
                                f'Conf: {frame["mean_confidence"]:.3f}, '
                                f'Prob: {probabilities.mean():.3f}', 
                                fontsize=10)
                ax_pred.set_xlabel('X Position')
                ax_pred.set_ylabel('Y Position')
                ax_pred.grid(True, alpha=0.3)
                
                # Add colorbar for first frame
                if frame_idx == 0:
                    plt.colorbar(scatter_pred, ax=ax_pred, label='Prediction')
        
        # Hide unused subplots
        for frame_idx in range(frames_to_show, rows * frames_per_row):
            row = (frame_idx // frames_per_row) * 2
            col = frame_idx % frames_per_row
            if row < axes.shape[0] and col < axes.shape[1]:
                axes[row, col].axis('off')
            if row + 1 < axes.shape[0] and col < axes.shape[1]:
                axes[row + 1, col].axis('off')
        
        plt.suptitle(f'{self.model_name} - Frame-by-Frame Spatial Analysis\n'
                    f'Temporal Window: {temporal_window}, Frames: {frames_to_show}', 
                    fontsize=16, weight='bold')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 
                                f'frame_by_frame_spatial_temporal_{temporal_window}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Frame-by-frame visualization saved")
    
    def create_error_analysis_by_frame(self, frame_data: List[Dict], temporal_window: int):
        """Create error analysis visualization across frames."""
        print("Creating frame-by-frame error analysis...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Extract frame-level statistics
        frame_ids = [frame['frame_id'] for frame in frame_data]
        accuracies = [frame['accuracy'] for frame in frame_data]
        confidences = [frame['mean_confidence'] for frame in frame_data]
        num_points = [frame['num_points'] for frame in frame_data]
        
        # Calculate error rates
        error_rates = [1 - acc for acc in accuracies]
        
        # 1. Accuracy over frames
        axes[0, 0].plot(frame_ids, accuracies, 'o-', linewidth=2, markersize=6)
        axes[0, 0].axhline(y=np.mean(accuracies), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(accuracies):.3f}')
        axes[0, 0].set_xlabel('Frame ID')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].set_title('Accuracy Across Frames')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Confidence over frames
        axes[0, 1].plot(frame_ids, confidences, 'o-', color='green', linewidth=2, markersize=6)
        axes[0, 1].axhline(y=np.mean(confidences), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(confidences):.3f}')
        axes[0, 1].set_xlabel('Frame ID')
        axes[0, 1].set_ylabel('Mean Confidence')
        axes[0, 1].set_title('Confidence Across Frames')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Number of points per frame
        axes[0, 2].plot(frame_ids, num_points, 'o-', color='purple', linewidth=2, markersize=6)
        axes[0, 2].axhline(y=np.mean(num_points), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(num_points):.1f}')
        axes[0, 2].set_xlabel('Frame ID')
        axes[0, 2].set_ylabel('Number of Points')
        axes[0, 2].set_title('Points per Frame')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. Accuracy vs Confidence scatter
        axes[1, 0].scatter(confidences, accuracies, alpha=0.7, s=60)
        axes[1, 0].set_xlabel('Mean Confidence')
        axes[1, 0].set_ylabel('Accuracy')
        axes[1, 0].set_title('Accuracy vs Confidence')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Add correlation coefficient
        corr_coef = np.corrcoef(confidences, accuracies)[0, 1]
        axes[1, 0].text(0.05, 0.95, f'Correlation: {corr_coef:.3f}', 
                       transform=axes[1, 0].transAxes, fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        # 5. Accuracy vs Number of Points
        axes[1, 1].scatter(num_points, accuracies, alpha=0.7, s=60, color='orange')
        axes[1, 1].set_xlabel('Number of Points')
        axes[1, 1].set_ylabel('Accuracy')
        axes[1, 1].set_title('Accuracy vs Points per Frame')
        axes[1, 1].grid(True, alpha=0.3)
        
        # Add correlation coefficient
        corr_coef = np.corrcoef(num_points, accuracies)[0, 1]
        axes[1, 1].text(0.05, 0.95, f'Correlation: {corr_coef:.3f}', 
                       transform=axes[1, 1].transAxes, fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        # 6. Frame statistics summary
        axes[1, 2].axis('off')
        stats_text = f"""
FRAME-BY-FRAME STATISTICS

Total Frames Analyzed: {len(frame_data)}
Temporal Window: {temporal_window}

ACCURACY STATISTICS:
• Mean Accuracy: {np.mean(accuracies):.4f}
• Std Accuracy: {np.std(accuracies):.4f}
• Min Accuracy: {np.min(accuracies):.4f}
• Max Accuracy: {np.max(accuracies):.4f}
• Range: {np.max(accuracies) - np.min(accuracies):.4f}

CONFIDENCE STATISTICS:
• Mean Confidence: {np.mean(confidences):.4f}
• Std Confidence: {np.std(confidences):.4f}
• Min Confidence: {np.min(confidences):.4f}
• Max Confidence: {np.max(confidences):.4f}

POINTS PER FRAME:
• Mean Points: {np.mean(num_points):.1f}
• Std Points: {np.std(num_points):.1f}
• Min Points: {np.min(num_points)}
• Max Points: {np.max(num_points)}

CORRELATIONS:
• Accuracy-Confidence: {np.corrcoef(confidences, accuracies)[0, 1]:.3f}
• Accuracy-Points: {np.corrcoef(num_points, accuracies)[0, 1]:.3f}
        """
        
        axes[1, 2].text(0.05, 0.95, stats_text, transform=axes[1, 2].transAxes, 
                        fontsize=10, verticalalignment='top', fontfamily='monospace',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        
        plt.suptitle(f'{self.model_name} - Frame-by-Frame Error Analysis', 
                    fontsize=16, weight='bold')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 
                                f'frame_error_analysis_temporal_{temporal_window}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Frame error analysis saved")
    
    def run_frame_analysis(self, temporal_window: int, max_frames: int = 50):
        """Run complete frame-by-frame analysis."""
        print(f"\n🎬 Starting frame-by-frame spatial analysis for {self.model_name}")
        
        # Load model and data
        model, test_loader, checkpoint = self.load_model_and_data(temporal_window)
        
        # Extract frame data
        frame_data = self.extract_frame_data(model, test_loader, temporal_window, max_frames)
        
        # Create visualizations
        self.create_frame_by_frame_visualization(frame_data, temporal_window)
        self.create_error_analysis_by_frame(frame_data, temporal_window)
        
        print(f"✅ Frame-by-frame analysis completed for {len(frame_data)} frames")
        return frame_data


def main():
    """Run frame-by-frame spatial analysis for complex models."""
    
    # Model configurations
    models_to_analyze = [
        {
            'name': 'Complex GATv2 (Temp 3)',
            'config_path': 'config.yaml',
            'checkpoint_path': 'checkpoints_gatv2_complex_4layers_temp3/model_temporal_3_best.pt',
            'output_dir': 'frame_by_frame_analysis_complex_temp3',
            'temporal_window': 3
        },
        {
            'name': 'Complex GATv2 (Temp 5)',
            'config_path': 'config_complex_temp5.yaml',
            'checkpoint_path': 'checkpoints_gatv2_complex_temp5/model_temporal_5_best.pt',
            'output_dir': 'frame_by_frame_analysis_complex_temp5',
            'temporal_window': 5
        }
    ]
    
    for model_config in models_to_analyze:
        try:
            # Load configuration
            with open(model_config['config_path'], 'r') as f:
                config = yaml.safe_load(f)
            
            # Adjust input dimension
            config['model']['input_dim'] = 10
            
            # Create analyzer
            analyzer = FrameByFrameSpatialAnalyzer(
                config=config,
                model_name=model_config['name'],
                checkpoint_path=model_config['checkpoint_path'],
                output_dir=model_config['output_dir']
            )
            
            # Run analysis with more frames
            frame_data = analyzer.run_frame_analysis(
                temporal_window=model_config['temporal_window'], 
                max_frames=60  # Analyze 60 frames
            )
            
        except Exception as e:
            print(f"❌ Error analyzing {model_config['name']}: {e}")
            continue
    
    print("\n🎉 Frame-by-frame spatial analysis completed for all models!")


if __name__ == "__main__":
    main()
