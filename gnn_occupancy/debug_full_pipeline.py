#!/usr/bin/env python3
"""
Debug script to test the full evaluation pipeline.
"""

import torch
import numpy as np
import yaml
from data import OccupancyDataset
from torch_geometric.data import DataLoader
from model import create_model

def debug_full_pipeline():
    """Debug the full evaluation pipeline."""
    print("🔍 Debugging full evaluation pipeline...")
    
    # Load model configuration
    config_path = "checkpoints_gatv2_temp3/config.yaml"
    model_path = "checkpoints_gatv2_temp3/model_temporal_3_best.pt"
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    print(f"📋 Config loaded: {config['model']}")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location='cpu')
    print(f"📦 Checkpoint keys: {list(checkpoint.keys())}")
    
    # Try to infer model parameters
    if 'convs.0.att' in checkpoint['model_state_dict']:
        att_shape = checkpoint['model_state_dict']['convs.0.att'].shape
        print(f"🔍 Attention shape: {att_shape}")
        if len(att_shape) >= 2:
            inferred_heads = att_shape[1]
            print(f"📊 Inferred attention heads: {inferred_heads}")
            config['model']['attention_heads'] = inferred_heads
    
    if 'embedding.weight' in checkpoint['model_state_dict']:
        embedding_shape = checkpoint['model_state_dict']['embedding.weight'].shape
        print(f"🔍 Embedding shape: {embedding_shape}")
        if len(embedding_shape) >= 2:
            inferred_input_dim = embedding_shape[1]
            print(f"📊 Inferred input dimension: {inferred_input_dim}")
            config['model']['input_dim'] = inferred_input_dim
    
    # Create model
    try:
        model = create_model(config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        print(f"✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False
    
    # Create dataset
    dataset = OccupancyDataset(
        root="../data/07_gnn_ready",
        split='test',
        temporal_window=3,
        binary_mapping={'occupied': [1, 2, 3, 4], 'unoccupied': [0]}
    )
    
    # Create data loader
    data_loader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # Test first few batches
    for batch_idx, batch in enumerate(data_loader):
        if batch_idx >= 3:  # Only test first 3 batches
            break
            
        print(f"\n🔄 Testing batch {batch_idx}:")
        print(f"  - batch.x.shape: {batch.x.shape}")
        print(f"  - batch.y.shape: {batch.y.shape}")
        print(f"  - batch.pos.shape: {batch.pos.shape}")
        print(f"  - batch.edge_index.shape: {batch.edge_index.shape}")
        print(f"  - batch.batch: {batch.batch}")
        
        # Test model prediction
        try:
            with torch.no_grad():
                predictions = model(batch.x, batch.edge_index, batch.batch)
                predictions = torch.sigmoid(predictions).cpu().numpy().flatten()
            print(f"  ✅ Model prediction successful: {predictions.shape}")
            print(f"  📊 Predictions: {predictions}")
        except Exception as e:
            print(f"  ❌ Model prediction failed: {e}")
            import traceback
            traceback.print_exc()
            continue
        
        # Test position extraction
        try:
            positions = batch.pos[:, :2].cpu().numpy()
            labels = batch.y.cpu().numpy().flatten()
            print(f"  ✅ Position extraction successful")
            print(f"  📍 Positions shape: {positions.shape}")
            print(f"  🏷️ Labels shape: {labels.shape}")
        except Exception as e:
            print(f"  ❌ Position extraction failed: {e}")
            import traceback
            traceback.print_exc()
            continue
        
        # Test grid conversion
        try:
            resolution = 0.1
            padding = 0.5
            
            # Calculate grid bounds
            min_x, min_y = positions.min(axis=0) - padding
            max_x, max_y = positions.max(axis=0) + padding
            
            # Calculate grid dimensions
            grid_width = int(np.ceil((max_x - min_x) / resolution))
            grid_height = int(np.ceil((max_y - min_y) / resolution))
            
            # Initialize grids
            gt_grid = np.zeros((grid_height, grid_width), dtype=np.float32)
            pred_grid = np.zeros((grid_height, grid_width), dtype=np.float32)
            gt_count = np.zeros((grid_height, grid_width), dtype=np.int32)
            pred_count = np.zeros((grid_height, grid_width), dtype=np.int32)
            
            # Convert points to grid indices
            grid_x = ((positions[:, 0] - min_x) / resolution).astype(int)
            grid_y = ((positions[:, 1] - min_y) / resolution).astype(int)
            
            # Clip to grid bounds
            grid_x = np.clip(grid_x, 0, grid_width - 1)
            grid_y = np.clip(grid_y, 0, grid_height - 1)
            
            # Aggregate
            for i in range(len(positions)):
                y, x = grid_y[i], grid_x[i]
                gt_grid[y, x] += labels[i]
                gt_count[y, x] += 1
                pred_grid[y, x] += predictions[i]
                pred_count[y, x] += 1
            
            # Average
            valid_gt = gt_count > 0
            gt_grid[valid_gt] = gt_grid[valid_gt] / gt_count[valid_gt]
            
            valid_pred = pred_count > 0
            pred_grid[valid_pred] = pred_grid[valid_pred] / pred_count[valid_pred]
            
            # Apply thresholds
            gt_grid = (gt_grid > 0.5).astype(np.float32)
            pred_grid = (pred_grid > 0.5).astype(np.float32)
            
            print(f"  ✅ Grid conversion successful")
            print(f"  🗺️ Grid shape: {gt_grid.shape}")
            
            # Calculate IoU
            gt_flat = gt_grid.flatten()
            pred_flat = pred_grid.flatten()
            
            intersection = np.sum((gt_flat == 1) & (pred_flat == 1))
            union = np.sum((gt_flat == 1) | (pred_flat == 1))
            
            if union == 0:
                iou = 1.0 if intersection == 0 else 0.0
            else:
                iou = intersection / union
            
            print(f"  🎯 IoU: {iou}")
            
        except Exception as e:
            print(f"  ❌ Grid conversion failed: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    print(f"\n✅ Full pipeline test completed!")
    return True

if __name__ == "__main__":
    debug_full_pipeline()
