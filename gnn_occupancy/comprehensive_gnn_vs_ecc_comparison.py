#!/usr/bin/env python3
"""
Comprehensive GNN vs ECC Model Comparison Analysis
"""

import os
import sys
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime
import matplotlib.patches as mpatches
from matplotlib.backends.backend_pdf import PdfPages

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


class GNNvsECCComparator:
    """Comprehensive comparison between GNN and ECC models."""
    
    def __init__(self):
        self.gnn_results_dir = Path("results/2d_iou_evaluation")
        self.ecc_results_dir = Path("results/ecc_2d_iou_evaluation")
        self.comparison_results_dir = Path("results/gnn_vs_ecc_comparison")
        self.comparison_results_dir.mkdir(parents=True, exist_ok=True)
        
        self.gnn_results = {}
        self.ecc_results = {}
        self.combined_results = {}
    
    def load_results(self):
        """Load results from both GNN and ECC evaluations."""
        print("📊 Loading GNN and ECC evaluation results...")
        
        # Load GNN results
        if self.gnn_results_dir.exists():
            for result_file in self.gnn_results_dir.glob("*_iou_results.json"):
                with open(result_file, 'r') as f:
                    data = json.load(f)
                    model_name = data['model_name']
                    self.gnn_results[model_name] = data
                    print(f"  ✅ Loaded GNN: {model_name}")
        
        # Load ECC results
        if self.ecc_results_dir.exists():
            for result_file in self.ecc_results_dir.glob("*_iou_results.json"):
                with open(result_file, 'r') as f:
                    data = json.load(f)
                    model_name = data['model_name']
                    self.ecc_results[model_name] = data
                    print(f"  ✅ Loaded ECC: {model_name}")
        
        # Combine results
        self.combined_results = {**self.gnn_results, **self.ecc_results}
        
        print(f"📈 Total models loaded: {len(self.combined_results)} ({len(self.gnn_results)} GNN + {len(self.ecc_results)} ECC)")
    
    def create_comprehensive_comparison(self):
        """Create comprehensive comparison visualizations."""
        print("🎨 Creating comprehensive GNN vs ECC comparison...")
        
        if not self.combined_results:
            print("❌ No results to compare!")
            return
        
        # Set up plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create main comparison figure
        fig = plt.figure(figsize=(24, 16))
        
        # 1. Overall Performance Comparison
        ax1 = plt.subplot(2, 4, 1)
        self._create_architecture_comparison(ax1)
        
        # 2. Performance Distribution
        ax2 = plt.subplot(2, 4, 2)
        self._create_performance_distribution(ax2)
        
        # 3. Temporal Window Analysis
        ax3 = plt.subplot(2, 4, 3)
        self._create_temporal_analysis(ax3)
        
        # 4. Model Complexity vs Performance
        ax4 = plt.subplot(2, 4, 4)
        self._create_complexity_analysis(ax4)
        
        # 5. Top Models Ranking
        ax5 = plt.subplot(2, 4, 5)
        self._create_top_models_ranking(ax5)
        
        # 6. Performance Variance Analysis
        ax6 = plt.subplot(2, 4, 6)
        self._create_variance_analysis(ax6)
        
        # 7. Architecture Efficiency
        ax7 = plt.subplot(2, 4, 7)
        self._create_efficiency_analysis(ax7)
        
        # 8. Summary Statistics
        ax8 = plt.subplot(2, 4, 8)
        self._create_summary_statistics(ax8)
        
        plt.tight_layout()
        plt.savefig(self.comparison_results_dir / 'gnn_vs_ecc_comprehensive_comparison.png', 
                   dpi=300, bbox_inches='tight')
        plt.savefig(self.comparison_results_dir / 'gnn_vs_ecc_comprehensive_comparison.pdf', 
                   bbox_inches='tight')
        plt.close()
        
        print(f"  ✅ Saved comprehensive comparison charts")
    
    def _create_architecture_comparison(self, ax):
        """Compare GNN vs ECC architectures."""
        gnn_ious = [data['mean_iou'] for data in self.gnn_results.values()]
        ecc_ious = [data['mean_iou'] for data in self.ecc_results.values()]
        
        data = [gnn_ious, ecc_ious]
        labels = ['GNN Models', 'ECC Models']
        colors = ['skyblue', 'lightcoral']
        
        bp = ax.boxplot(data, labels=labels, patch_artist=True)
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax.set_ylabel('Mean IoU')
        ax.set_title('GNN vs ECC Architecture Comparison')
        ax.grid(True, alpha=0.3)
        
        # Add statistical comparison
        if len(gnn_ious) > 0 and len(ecc_ious) > 0:
            gnn_mean = np.mean(gnn_ious)
            ecc_mean = np.mean(ecc_ious)
            ax.text(0.5, 0.95, f'GNN Mean: {gnn_mean:.3f}\nECC Mean: {ecc_mean:.3f}',
                   transform=ax.transAxes, ha='center', va='top',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    def _create_performance_distribution(self, ax):
        """Create performance distribution comparison."""
        # Get all IoU values for each architecture type
        gnn_all_ious = []
        ecc_all_ious = []
        
        for data in self.gnn_results.values():
            gnn_all_ious.extend(data['sample_ious'])
        
        for data in self.ecc_results.values():
            ecc_all_ious.extend(data['sample_ious'])
        
        # Create histograms
        bins = np.linspace(0, 1, 30)
        ax.hist(gnn_all_ious, bins=bins, alpha=0.6, label='GNN Models', color='skyblue', density=True)
        ax.hist(ecc_all_ious, bins=bins, alpha=0.6, label='ECC Models', color='lightcoral', density=True)
        
        ax.set_xlabel('IoU Score')
        ax.set_ylabel('Density')
        ax.set_title('IoU Score Distribution Comparison')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_temporal_analysis(self, ax):
        """Analyze performance by temporal window."""
        temp_data = {'GNN': {'temp3': [], 'temp5': []}, 'ECC': {'temp3': [], 'temp5': []}}
        
        # Categorize by temporal window
        for name, data in self.gnn_results.items():
            if 'temp3' in name:
                temp_data['GNN']['temp3'].append(data['mean_iou'])
            elif 'temp5' in name:
                temp_data['GNN']['temp5'].append(data['mean_iou'])
        
        for name, data in self.ecc_results.items():
            if 'temp3' in name:
                temp_data['ECC']['temp3'].append(data['mean_iou'])
            elif 'temp5' in name:
                temp_data['ECC']['temp5'].append(data['mean_iou'])
        
        # Create grouped bar chart
        x = np.arange(2)
        width = 0.35
        
        gnn_means = [np.mean(temp_data['GNN']['temp3']) if temp_data['GNN']['temp3'] else 0,
                     np.mean(temp_data['GNN']['temp5']) if temp_data['GNN']['temp5'] else 0]
        ecc_means = [np.mean(temp_data['ECC']['temp3']) if temp_data['ECC']['temp3'] else 0,
                     np.mean(temp_data['ECC']['temp5']) if temp_data['ECC']['temp5'] else 0]
        
        ax.bar(x - width/2, gnn_means, width, label='GNN', color='skyblue', alpha=0.7)
        ax.bar(x + width/2, ecc_means, width, label='ECC', color='lightcoral', alpha=0.7)
        
        ax.set_xlabel('Temporal Window')
        ax.set_ylabel('Mean IoU')
        ax.set_title('Performance by Temporal Window')
        ax.set_xticks(x)
        ax.set_xticklabels(['Temporal-3', 'Temporal-5'])
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_complexity_analysis(self, ax):
        """Analyze performance vs model complexity."""
        # This would require parameter count data - simplified version
        gnn_names = list(self.gnn_results.keys())
        ecc_names = list(self.ecc_results.keys())
        
        gnn_ious = [self.gnn_results[name]['mean_iou'] for name in gnn_names]
        ecc_ious = [self.ecc_results[name]['mean_iou'] for name in ecc_names]
        
        # Create scatter plot (simplified - using model index as proxy for complexity)
        ax.scatter(range(len(gnn_ious)), gnn_ious, c='skyblue', s=100, alpha=0.7, label='GNN', edgecolors='black')
        ax.scatter(range(len(gnn_ious), len(gnn_ious) + len(ecc_ious)), ecc_ious, 
                  c='lightcoral', s=100, alpha=0.7, label='ECC', edgecolors='black')
        
        ax.set_xlabel('Model Index (Complexity Proxy)')
        ax.set_ylabel('Mean IoU')
        ax.set_title('Performance vs Model Complexity')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _create_top_models_ranking(self, ax):
        """Create ranking of top models."""
        # Get top 8 models overall
        all_models = [(name, data['mean_iou'], 'GNN' if name in self.gnn_results else 'ECC') 
                     for name, data in self.combined_results.items()]
        all_models.sort(key=lambda x: x[1], reverse=True)
        top_models = all_models[:8]
        
        names = [model[0].split('_')[0] for model in top_models]
        ious = [model[1] for model in top_models]
        types = [model[2] for model in top_models]
        
        colors = ['skyblue' if t == 'GNN' else 'lightcoral' for t in types]
        
        bars = ax.barh(range(len(names)), ious, color=colors, alpha=0.7, edgecolor='black')
        ax.set_yticks(range(len(names)))
        ax.set_yticklabels(names)
        ax.set_xlabel('Mean IoU')
        ax.set_title('Top 8 Models Ranking')
        ax.grid(True, alpha=0.3)
        
        # Add value labels
        for i, (bar, iou) in enumerate(zip(bars, ious)):
            ax.text(iou + 0.01, i, f'{iou:.3f}', va='center', fontsize=8)
        
        # Create legend
        gnn_patch = mpatches.Patch(color='skyblue', label='GNN')
        ecc_patch = mpatches.Patch(color='lightcoral', label='ECC')
        ax.legend(handles=[gnn_patch, ecc_patch])
    
    def _create_variance_analysis(self, ax):
        """Analyze performance variance."""
        gnn_stds = [data['std_iou'] for data in self.gnn_results.values()]
        ecc_stds = [data['std_iou'] for data in self.ecc_results.values()]
        
        data = [gnn_stds, ecc_stds]
        labels = ['GNN Models', 'ECC Models']
        colors = ['skyblue', 'lightcoral']
        
        bp = ax.boxplot(data, labels=labels, patch_artist=True)
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax.set_ylabel('IoU Standard Deviation')
        ax.set_title('Performance Variance Comparison')
        ax.grid(True, alpha=0.3)
    
    def _create_efficiency_analysis(self, ax):
        """Analyze model efficiency (simplified)."""
        # Simplified efficiency analysis
        gnn_means = [data['mean_iou'] for data in self.gnn_results.values()]
        ecc_means = [data['mean_iou'] for data in self.ecc_results.values()]
        
        if gnn_means and ecc_means:
            categories = ['Best Performance', 'Average Performance', 'Consistency']
            gnn_scores = [max(gnn_means), np.mean(gnn_means), 1 - np.std(gnn_means)]
            ecc_scores = [max(ecc_means), np.mean(ecc_means), 1 - np.std(ecc_means)]
            
            x = np.arange(len(categories))
            width = 0.35
            
            ax.bar(x - width/2, gnn_scores, width, label='GNN', color='skyblue', alpha=0.7)
            ax.bar(x + width/2, ecc_scores, width, label='ECC', color='lightcoral', alpha=0.7)
            
            ax.set_xlabel('Efficiency Metrics')
            ax.set_ylabel('Score')
            ax.set_title('Model Efficiency Analysis')
            ax.set_xticks(x)
            ax.set_xticklabels(categories, rotation=45, ha='right')
            ax.legend()
            ax.grid(True, alpha=0.3)
    
    def _create_summary_statistics(self, ax):
        """Create summary statistics table."""
        # Calculate summary statistics
        gnn_ious = [data['mean_iou'] for data in self.gnn_results.values()]
        ecc_ious = [data['mean_iou'] for data in self.ecc_results.values()]
        
        stats_data = [
            ['Architecture', 'GNN', 'ECC'],
            ['Count', len(gnn_ious), len(ecc_ious)],
            ['Best IoU', f"{max(gnn_ious):.3f}" if gnn_ious else "N/A", 
             f"{max(ecc_ious):.3f}" if ecc_ious else "N/A"],
            ['Mean IoU', f"{np.mean(gnn_ious):.3f}" if gnn_ious else "N/A", 
             f"{np.mean(ecc_ious):.3f}" if ecc_ious else "N/A"],
            ['Std Dev', f"{np.std(gnn_ious):.3f}" if gnn_ious else "N/A", 
             f"{np.std(ecc_ious):.3f}" if ecc_ious else "N/A"]
        ]
        
        table = ax.table(cellText=stats_data, cellLoc='center', loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        
        # Color code the header
        for i in range(3):
            table[(0, i)].set_facecolor('#40466e')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        ax.axis('off')
        ax.set_title('Summary Statistics', fontsize=12, fontweight='bold', pad=20)
    
    def generate_detailed_report(self):
        """Generate detailed comparison report."""
        print("📄 Generating detailed comparison report...")
        
        # Create comprehensive DataFrame
        report_data = []
        
        for name, data in self.combined_results.items():
            model_type = 'GNN' if name in self.gnn_results else 'ECC'
            architecture = name.split('_')[0]
            temporal_window = 3 if 'temp3' in name else 5 if 'temp5' in name else 'Unknown'
            
            report_data.append({
                'Model_Name': name,
                'Model_Type': model_type,
                'Architecture': architecture,
                'Temporal_Window': temporal_window,
                'Mean_IoU': data['mean_iou'],
                'Median_IoU': data['median_iou'],
                'Std_IoU': data['std_iou'],
                'Min_IoU': data['min_iou'],
                'Max_IoU': data['max_iou'],
                'Total_Samples': data['total_samples']
            })
        
        df = pd.DataFrame(report_data)
        df = df.sort_values('Mean_IoU', ascending=False)
        
        # Save detailed CSV
        csv_path = self.comparison_results_dir / 'gnn_vs_ecc_detailed_comparison.csv'
        df.to_csv(csv_path, index=False)
        
        # Save summary JSON
        summary = {
            'evaluation_date': datetime.now().isoformat(),
            'total_models': len(self.combined_results),
            'gnn_models': len(self.gnn_results),
            'ecc_models': len(self.ecc_results),
            'best_overall': {
                'model': df.iloc[0]['Model_Name'],
                'type': df.iloc[0]['Model_Type'],
                'iou': df.iloc[0]['Mean_IoU']
            },
            'best_gnn': {
                'model': df[df['Model_Type'] == 'GNN'].iloc[0]['Model_Name'],
                'iou': df[df['Model_Type'] == 'GNN'].iloc[0]['Mean_IoU']
            } if len(df[df['Model_Type'] == 'GNN']) > 0 else None,
            'best_ecc': {
                'model': df[df['Model_Type'] == 'ECC'].iloc[0]['Model_Name'],
                'iou': df[df['Model_Type'] == 'ECC'].iloc[0]['Mean_IoU']
            } if len(df[df['Model_Type'] == 'ECC']) > 0 else None,
            'architecture_comparison': {
                'gnn_mean': df[df['Model_Type'] == 'GNN']['Mean_IoU'].mean() if len(df[df['Model_Type'] == 'GNN']) > 0 else 0,
                'ecc_mean': df[df['Model_Type'] == 'ECC']['Mean_IoU'].mean() if len(df[df['Model_Type'] == 'ECC']) > 0 else 0
            }
        }
        
        json_path = self.comparison_results_dir / 'gnn_vs_ecc_summary.json'
        with open(json_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"  ✅ Saved detailed comparison report: {csv_path}")
        print(f"  ✅ Saved summary report: {json_path}")
        
        return df, summary
    
    def run_complete_comparison(self):
        """Run complete GNN vs ECC comparison."""
        print("🚀 Starting Complete GNN vs ECC Comparison")
        print("=" * 60)
        
        try:
            # Load results
            self.load_results()
            
            if not self.combined_results:
                print("❌ No results found to compare!")
                return False
            
            # Create visualizations
            self.create_comprehensive_comparison()
            
            # Generate reports
            df, summary = self.generate_detailed_report()
            
            # Print summary
            print("\n" + "=" * 60)
            print("🎉 COMPARISON COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print(f"📊 Total Models Compared: {len(self.combined_results)}")
            print(f"🔵 GNN Models: {len(self.gnn_results)}")
            print(f"🔴 ECC Models: {len(self.ecc_results)}")
            print(f"📁 Results Directory: {self.comparison_results_dir}")
            
            if summary['best_overall']:
                print(f"🏆 Best Overall Model: {summary['best_overall']['model']} ({summary['best_overall']['type']})")
                print(f"🎯 Best IoU: {summary['best_overall']['iou']:.4f}")
            
            if summary['best_gnn']:
                print(f"🔵 Best GNN Model: {summary['best_gnn']['model']}")
                print(f"🎯 Best GNN IoU: {summary['best_gnn']['iou']:.4f}")
            
            if summary['best_ecc']:
                print(f"🔴 Best ECC Model: {summary['best_ecc']['model']}")
                print(f"🎯 Best ECC IoU: {summary['best_ecc']['iou']:.4f}")
            
            print(f"\n📈 Architecture Comparison:")
            print(f"  🔵 GNN Mean IoU: {summary['architecture_comparison']['gnn_mean']:.4f}")
            print(f"  🔴 ECC Mean IoU: {summary['architecture_comparison']['ecc_mean']:.4f}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Comparison failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function."""
    print("🔬 Comprehensive GNN vs ECC Model Comparison Analysis")
    print("=" * 80)
    
    comparator = GNNvsECCComparator()
    success = comparator.run_complete_comparison()
    
    if success:
        print("\n✅ Comparison completed! Check the results directory for detailed analysis.")
    else:
        print("\n❌ Comparison failed. Check the error messages above.")
    
    return success


if __name__ == "__main__":
    main()
