#!/usr/bin/env python3

import os
import torch
import yaml
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from typing import Dict, List, Tuple
import pandas as pd
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    confusion_matrix, roc_curve, auc, precision_recall_curve,
    classification_report, mean_squared_error, r2_score, average_precision_score
)
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.calibration import calibration_curve
import warnings
warnings.filterwarnings('ignore')

# Add safe globals for PyTorch serialization
torch.serialization.add_safe_globals([
    'torch_geometric.data.data.Data',
    'torch_geometric.data.data.DataEdgeAttr',
    'torch_geometric.data.data.DataNodeAttr',
    'torch_geometric.data.storage.EdgeStorage',
    'torch_geometric.data.storage.NodeStorage',
])

from data import create_data_loaders
from model import create_model


class AdvancedTestEvaluator:
    """Advanced evaluation on test dataset with comprehensive visualizations."""

    def __init__(self, config: Dict, model_name: str, checkpoint_path: str, output_dir: str):
        self.config = config
        self.model_name = model_name
        self.checkpoint_path = checkpoint_path
        self.output_dir = output_dir
        self.device = torch.device(config["training"]["device"])

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Set advanced plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        plt.rcParams['figure.dpi'] = 300
        plt.rcParams['savefig.dpi'] = 300
        plt.rcParams['font.size'] = 10

    def load_model_and_test_data(self, temporal_window: int):
        """Load model and test data for comprehensive evaluation."""
        print(f"Loading model and test data for temporal window {temporal_window}...")

        # Create data loaders
        data_loaders = create_data_loaders(self.config)
        test_loader = data_loaders[f"temporal_{temporal_window}"]["test"]

        print(f"Test dataset size: {len(test_loader)} batches")

        # Create and load model
        model = create_model(self.config)
        checkpoint = torch.load(self.checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint["model_state_dict"])
        model = model.to(self.device)
        model.eval()

        return model, test_loader, checkpoint

    def comprehensive_test_evaluation(self, model, test_loader):
        """Perform comprehensive evaluation on test dataset."""
        print("Performing comprehensive test evaluation...")

        all_predictions = []
        all_targets = []
        all_probabilities = []
        all_node_features = []
        all_graph_features = []
        all_positions = []
        all_batch_indices = []
        all_edge_indices = []
        all_confidences = []

        total_samples = 0
        correct_predictions = 0

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="Evaluating on test data")):
                batch = batch.to(self.device)

                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)
                probabilities = torch.sigmoid(logits)
                predictions = probabilities > 0.5

                # Process each graph in the batch
                for graph_idx in range(batch.num_graphs):
                    # Get node mask for this graph
                    node_mask = batch.batch == graph_idx

                    # Extract graph-level data
                    graph_nodes = batch.x[node_mask]
                    graph_positions = batch.pos[node_mask]
                    graph_targets = batch.y[node_mask]

                    # Get graph-level prediction (mean of node predictions)
                    if logits.size(0) == batch.y.size(0):
                        # Node-level predictions
                        graph_probs = probabilities[node_mask]
                        graph_preds = predictions[node_mask]
                    else:
                        # Graph-level predictions - handle single prediction per graph
                        if graph_idx < logits.size(0):
                            single_prob = probabilities[graph_idx].item()
                            single_pred = predictions[graph_idx].item()
                        else:
                            # Fallback: use mean of all predictions
                            single_prob = probabilities.mean().item()
                            single_pred = (single_prob > 0.5)

                        graph_probs = torch.full((graph_targets.size(0),), single_prob)
                        graph_preds = torch.full((graph_targets.size(0),), single_pred)

                    # Calculate graph-level metrics
                    graph_prob_mean = graph_probs.mean().cpu().numpy()
                    graph_pred_mean = (graph_prob_mean > 0.5).astype(int)
                    graph_target_mean = graph_targets.float().mean().round().cpu().numpy()
                    graph_confidence = abs(graph_prob_mean - 0.5) * 2

                    # Store data
                    all_probabilities.append(graph_prob_mean)
                    all_predictions.append(graph_pred_mean)
                    all_targets.append(graph_target_mean)
                    all_confidences.append(graph_confidence)

                    # Store features for advanced analysis
                    all_node_features.append(graph_nodes.cpu().numpy())
                    all_positions.append(graph_positions.cpu().numpy())
                    all_batch_indices.append(graph_idx)

                    # Calculate graph-level features
                    graph_feature_mean = graph_nodes.mean(dim=0).cpu().numpy()
                    all_graph_features.append(graph_feature_mean)

                    # Update counters
                    total_samples += 1
                    if graph_pred_mean == graph_target_mean:
                        correct_predictions += 1

        print(f"Processed {total_samples} test samples")
        print(f"Test accuracy: {correct_predictions/total_samples:.4f}")

        return {
            'predictions': np.array(all_predictions),
            'targets': np.array(all_targets),
            'probabilities': np.array(all_probabilities),
            'confidences': np.array(all_confidences),
            'node_features': all_node_features,
            'graph_features': np.array(all_graph_features),
            'positions': all_positions,
            'batch_indices': all_batch_indices,
            'total_samples': total_samples
        }

    def create_advanced_performance_dashboard(self, data: Dict, temporal_window: int, checkpoint: Dict):
        """Create advanced performance analysis dashboard."""
        print("Creating advanced performance dashboard...")

        fig = plt.figure(figsize=(24, 20))

        # Calculate comprehensive metrics
        accuracy = accuracy_score(data['targets'], data['predictions'])
        precision = precision_score(data['targets'], data['predictions'], zero_division=0)
        recall = recall_score(data['targets'], data['predictions'], zero_division=0)
        f1 = f1_score(data['targets'], data['predictions'], zero_division=0)
        roc_auc = roc_auc_score(data['targets'], data['probabilities'])
        avg_precision = average_precision_score(data['targets'], data['probabilities'])

        # 1. Enhanced Confusion Matrix with Statistics
        ax1 = plt.subplot(4, 5, 1)
        cm = confusion_matrix(data['targets'], data['predictions'])
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]

        sns.heatmap(cm_normalized, annot=True, fmt='.3f', cmap='Blues',
                   xticklabels=['Unoccupied', 'Occupied'],
                   yticklabels=['Unoccupied', 'Occupied'],
                   cbar_kws={'label': 'Normalized Rate'})
        ax1.set_title(f'Confusion Matrix\n(n={data["total_samples"]} samples)')
        ax1.set_xlabel('Predicted')
        ax1.set_ylabel('True')

        # Add raw counts as text
        for i in range(2):
            for j in range(2):
                ax1.text(j+0.5, i+0.7, f'n={cm[i,j]}', ha='center', va='center',
                        fontsize=8, color='red', weight='bold')

        # 2. ROC Curve with Confidence Intervals
        ax2 = plt.subplot(4, 5, 2)
        fpr, tpr, thresholds = roc_curve(data['targets'], data['probabilities'])

        # Bootstrap confidence intervals
        n_bootstrap = 100
        tpr_bootstrap = []
        for _ in range(n_bootstrap):
            indices = np.random.choice(len(data['targets']), len(data['targets']), replace=True)
            if len(np.unique(data['targets'][indices])) < 2:
                continue
            fpr_boot, tpr_boot, _ = roc_curve(data['targets'][indices], data['probabilities'][indices])
            tpr_interp = np.interp(fpr, fpr_boot, tpr_boot)
            tpr_bootstrap.append(tpr_interp)

        tpr_bootstrap = np.array(tpr_bootstrap)
        tpr_mean = np.mean(tpr_bootstrap, axis=0)
        tpr_std = np.std(tpr_bootstrap, axis=0)

        ax2.plot(fpr, tpr, color='darkorange', lw=3, label=f'ROC (AUC = {roc_auc:.3f})')
        ax2.fill_between(fpr, tpr_mean - tpr_std, tpr_mean + tpr_std, alpha=0.2, color='orange')
        ax2.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', alpha=0.8)

        # Add optimal threshold point
        optimal_idx = np.argmax(tpr - fpr)
        ax2.plot(fpr[optimal_idx], tpr[optimal_idx], 'ro', markersize=8,
                label=f'Optimal (t={thresholds[optimal_idx]:.3f})')

        ax2.set_xlim([0.0, 1.0])
        ax2.set_ylim([0.0, 1.05])
        ax2.set_xlabel('False Positive Rate')
        ax2.set_ylabel('True Positive Rate')
        ax2.set_title('ROC Curve with Confidence Bands')
        ax2.legend(loc="lower right")
        ax2.grid(True, alpha=0.3)

        # 3. Precision-Recall Curve with AP Score
        ax3 = plt.subplot(4, 5, 3)
        precision_curve, recall_curve, pr_thresholds = precision_recall_curve(data['targets'], data['probabilities'])

        ax3.plot(recall_curve, precision_curve, color='darkgreen', lw=3,
                label=f'PR Curve (AP = {avg_precision:.3f})')
        ax3.axhline(y=data['targets'].mean(), color='red', linestyle='--', alpha=0.8,
                   label=f'Baseline ({data["targets"].mean():.3f})')
        ax3.set_xlabel('Recall')
        ax3.set_ylabel('Precision')
        ax3.set_title('Precision-Recall Curve')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. Probability Distribution Analysis
        ax4 = plt.subplot(4, 5, 4)
        bins = np.linspace(0, 1, 30)
        ax4.hist(data['probabilities'][data['targets'] == 0], bins=bins, alpha=0.7,
                label=f'Unoccupied (n={np.sum(data["targets"] == 0)})', color='blue', density=True)
        ax4.hist(data['probabilities'][data['targets'] == 1], bins=bins, alpha=0.7,
                label=f'Occupied (n={np.sum(data["targets"] == 1)})', color='red', density=True)
        ax4.axvline(x=0.5, color='black', linestyle='--', alpha=0.8, label='Threshold')
        ax4.set_xlabel('Predicted Probability')
        ax4.set_ylabel('Density')
        ax4.set_title('Probability Distribution by Class')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. Calibration Analysis
        ax5 = plt.subplot(4, 5, 5)
        fraction_of_positives, mean_predicted_value = calibration_curve(
            data['targets'], data['probabilities'], n_bins=10)

        ax5.plot(mean_predicted_value, fraction_of_positives, "s-",
                label="Model", color='red', linewidth=2, markersize=8)
        ax5.plot([0, 1], [0, 1], "k:", label="Perfect calibration", linewidth=2)

        # Calculate calibration error
        calibration_error = np.mean(np.abs(fraction_of_positives - mean_predicted_value))
        ax5.set_xlabel('Mean Predicted Probability')
        ax5.set_ylabel('Fraction of Positives')
        ax5.set_title(f'Calibration Plot\n(Error = {calibration_error:.3f})')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. Feature Importance Analysis
        ax6 = plt.subplot(4, 5, 6)
        feature_names = ['X', 'Y', 'Z', 'Intensity', 'Temp1', 'Temp2', 'Temp3', 'Temp4', 'Temp5', 'Label']
        feature_importance = np.abs(data['graph_features']).mean(axis=0)

        bars = ax6.bar(range(len(feature_importance)), feature_importance,
                      color='skyblue', alpha=0.8)
        ax6.set_xlabel('Feature Index')
        ax6.set_ylabel('Mean Absolute Value')
        ax6.set_title('Feature Importance Analysis')
        ax6.set_xticks(range(len(feature_importance)))
        ax6.set_xticklabels(feature_names, rotation=45)
        ax6.grid(True, alpha=0.3)

        # 7. Error Analysis by Confidence
        ax7 = plt.subplot(4, 5, 7)
        errors = np.abs(data['targets'] - data['predictions'])

        # Bin by confidence
        conf_bins = np.linspace(0, 1, 11)
        bin_centers = (conf_bins[:-1] + conf_bins[1:]) / 2
        error_rates = []
        sample_counts = []

        for i in range(len(conf_bins) - 1):
            mask = (data['confidences'] >= conf_bins[i]) & (data['confidences'] < conf_bins[i+1])
            if mask.sum() > 0:
                error_rates.append(errors[mask].mean())
                sample_counts.append(mask.sum())
            else:
                error_rates.append(0)
                sample_counts.append(0)

        bars = ax7.bar(bin_centers, error_rates, width=0.08, alpha=0.7, color='red')
        ax7.set_xlabel('Prediction Confidence')
        ax7.set_ylabel('Error Rate')
        ax7.set_title('Error Rate vs Confidence')
        ax7.grid(True, alpha=0.3)

        # Add sample count labels
        for i, (bar, count) in enumerate(zip(bars, sample_counts)):
            if count > 0:
                ax7.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                        f'n={count}', ha='center', va='bottom', fontsize=8)

        # 8. Threshold Analysis
        ax8 = plt.subplot(4, 5, 8)
        thresholds = np.linspace(0.1, 0.9, 50)
        accuracies = []
        f1_scores = []
        precisions = []
        recalls = []

        for threshold in thresholds:
            preds_thresh = (data['probabilities'] > threshold).astype(int)
            accuracies.append(accuracy_score(data['targets'], preds_thresh))
            f1_scores.append(f1_score(data['targets'], preds_thresh, zero_division=0))
            precisions.append(precision_score(data['targets'], preds_thresh, zero_division=0))
            recalls.append(recall_score(data['targets'], preds_thresh, zero_division=0))

        ax8.plot(thresholds, accuracies, label='Accuracy', linewidth=2)
        ax8.plot(thresholds, f1_scores, label='F1 Score', linewidth=2)
        ax8.plot(thresholds, precisions, label='Precision', linewidth=2)
        ax8.plot(thresholds, recalls, label='Recall', linewidth=2)
        ax8.axvline(x=0.5, color='black', linestyle='--', alpha=0.8, label='Default')
        ax8.set_xlabel('Classification Threshold')
        ax8.set_ylabel('Score')
        ax8.set_title('Threshold Sensitivity Analysis')
        ax8.legend()
        ax8.grid(True, alpha=0.3)

        # 9. Class Distribution Analysis
        ax9 = plt.subplot(4, 5, 9)
        class_counts = np.bincount(data['targets'].astype(int))
        colors = ['lightblue', 'lightcoral']
        wedges, texts, autotexts = ax9.pie(class_counts, labels=['Unoccupied', 'Occupied'],
                                          colors=colors, autopct='%1.1f%%', startangle=90)
        ax9.set_title(f'Test Set Class Distribution\n(Total: {data["total_samples"]} samples)')

        # 10. Performance Metrics Summary
        ax10 = plt.subplot(4, 5, 10)
        metrics = {
            'Accuracy': accuracy,
            'Precision': precision,
            'Recall': recall,
            'F1 Score': f1,
            'ROC AUC': roc_auc,
            'Avg Precision': avg_precision
        }

        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())
        colors = ['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum', 'orange']

        bars = ax10.bar(range(len(metrics)), metric_values, color=colors, alpha=0.8)
        ax10.set_ylim(0, 1)
        ax10.set_ylabel('Score')
        ax10.set_title('Performance Metrics Summary')
        ax10.set_xticks(range(len(metrics)))
        ax10.set_xticklabels(metric_names, rotation=45)

        # Add value labels on bars
        for bar, value in zip(bars, metric_values):
            ax10.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                     f'{value:.3f}', ha='center', va='bottom', fontsize=9, weight='bold')

        # 11. Confidence Distribution
        ax11 = plt.subplot(4, 5, 11)
        ax11.hist(data['confidences'], bins=30, alpha=0.7, color='purple', density=True)
        ax11.axvline(x=data['confidences'].mean(), color='red', linestyle='--',
                    label=f'Mean: {data["confidences"].mean():.3f}')
        ax11.set_xlabel('Prediction Confidence')
        ax11.set_ylabel('Density')
        ax11.set_title('Confidence Distribution')
        ax11.legend()
        ax11.grid(True, alpha=0.3)

        # 12. Model Information and Statistics
        ax12 = plt.subplot(4, 5, 12)
        info_text = f"""
{self.model_name}
Temporal Window: {temporal_window}

TEST EVALUATION RESULTS:
• Total Samples: {data['total_samples']:,}
• Accuracy: {accuracy:.4f}
• Precision: {precision:.4f}
• Recall: {recall:.4f}
• F1 Score: {f1:.4f}
• ROC AUC: {roc_auc:.4f}
• Avg Precision: {avg_precision:.4f}

CONFIDENCE ANALYSIS:
• Mean Confidence: {data['confidences'].mean():.3f}
• Std Confidence: {data['confidences'].std():.3f}
• High Conf (>0.8): {np.sum(data['confidences'] > 0.8)} samples
• Low Conf (<0.2): {np.sum(data['confidences'] < 0.2)} samples

CLASS DISTRIBUTION:
• Unoccupied: {class_counts[0]} ({class_counts[0]/data['total_samples']*100:.1f}%)
• Occupied: {class_counts[1]} ({class_counts[1]/data['total_samples']*100:.1f}%)
        """
        ax12.text(0.05, 0.95, info_text, transform=ax12.transAxes, fontsize=8,
                 verticalalignment='top', fontfamily='monospace',
                 bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        ax12.axis('off')

        # 13-20: Additional advanced visualizations will be added in next panels

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, f'advanced_test_evaluation_temporal_{temporal_window}.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Advanced performance dashboard saved")

        # Create additional specialized visualizations
        self.create_embedding_analysis(data, temporal_window)
        self.create_spatial_analysis(data, temporal_window)
        self.create_statistical_analysis(data, temporal_window)

    def create_embedding_analysis(self, data: Dict, temporal_window: int):
        """Create advanced embedding and feature analysis."""
        print("Creating embedding analysis...")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 1. t-SNE Embedding Analysis
        print("Computing t-SNE embedding...")
        sample_size = min(1000, len(data['graph_features']))  # Sample for speed
        indices = np.random.choice(len(data['graph_features']), sample_size, replace=False)

        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, sample_size//4))
        embeddings_2d = tsne.fit_transform(data['graph_features'][indices])

        scatter = axes[0, 0].scatter(embeddings_2d[:, 0], embeddings_2d[:, 1],
                                   c=data['targets'][indices], cmap='RdYlBu', alpha=0.7, s=50)
        axes[0, 0].set_title(f't-SNE Embedding (n={sample_size})')
        axes[0, 0].set_xlabel('t-SNE 1')
        axes[0, 0].set_ylabel('t-SNE 2')
        plt.colorbar(scatter, ax=axes[0, 0], label='True Label')

        # 2. PCA Analysis with Explained Variance
        pca = PCA(n_components=2)
        pca_embeddings = pca.fit_transform(data['graph_features'])

        scatter = axes[0, 1].scatter(pca_embeddings[:, 0], pca_embeddings[:, 1],
                                   c=data['targets'], cmap='RdYlBu', alpha=0.7, s=30)
        axes[0, 1].set_title(f'PCA Embedding\n(Var: {pca.explained_variance_ratio_.sum():.3f})')
        axes[0, 1].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.3f})')
        axes[0, 1].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.3f})')
        plt.colorbar(scatter, ax=axes[0, 1], label='True Label')

        # 3. Feature Correlation Heatmap
        feature_names = ['X', 'Y', 'Z', 'Intensity', 'Temp1', 'Temp2', 'Temp3', 'Temp4', 'Temp5', 'Label']
        corr_matrix = np.corrcoef(data['graph_features'].T)

        im = axes[0, 2].imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
        axes[0, 2].set_title('Feature Correlation Matrix')
        axes[0, 2].set_xticks(range(len(feature_names)))
        axes[0, 2].set_yticks(range(len(feature_names)))
        axes[0, 2].set_xticklabels(feature_names, rotation=45)
        axes[0, 2].set_yticklabels(feature_names)
        plt.colorbar(im, ax=axes[0, 2])

        # 4. Prediction Confidence in Embedding Space
        scatter = axes[1, 0].scatter(pca_embeddings[:, 0], pca_embeddings[:, 1],
                                   c=data['confidences'], cmap='viridis', alpha=0.7, s=30)
        axes[1, 0].set_title('Prediction Confidence in PCA Space')
        axes[1, 0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.3f})')
        axes[1, 0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.3f})')
        plt.colorbar(scatter, ax=axes[1, 0], label='Confidence')

        # 5. Feature Distribution by Class
        feature_idx = 3  # Intensity feature
        axes[1, 1].hist(data['graph_features'][data['targets'] == 0, feature_idx],
                       bins=30, alpha=0.7, label='Unoccupied', color='blue', density=True)
        axes[1, 1].hist(data['graph_features'][data['targets'] == 1, feature_idx],
                       bins=30, alpha=0.7, label='Occupied', color='red', density=True)
        axes[1, 1].set_title(f'Feature Distribution: {feature_names[feature_idx]}')
        axes[1, 1].set_xlabel('Feature Value')
        axes[1, 1].set_ylabel('Density')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # 6. Clustering Analysis
        kmeans = KMeans(n_clusters=4, random_state=42)
        clusters = kmeans.fit_predict(data['graph_features'])

        scatter = axes[1, 2].scatter(pca_embeddings[:, 0], pca_embeddings[:, 1],
                                   c=clusters, cmap='tab10', alpha=0.7, s=30)
        axes[1, 2].set_title('K-Means Clustering (k=4)')
        axes[1, 2].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.3f})')
        axes[1, 2].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.3f})')
        plt.colorbar(scatter, ax=axes[1, 2], label='Cluster')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, f'embedding_analysis_temporal_{temporal_window}.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ Embedding analysis saved")

    def create_spatial_analysis(self, data: Dict, temporal_window: int):
        """Create spatial distribution analysis."""
        print("Creating spatial analysis...")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # Sample positions for visualization (take first position from each graph)
        sample_positions = []
        for pos_list in data['positions'][:1000]:  # Limit for visualization
            if len(pos_list) > 0:
                sample_positions.append(pos_list[0])  # Take first position

        if len(sample_positions) == 0:
            print("No position data available for spatial analysis")
            return

        sample_positions = np.array(sample_positions)
        sample_targets = data['targets'][:len(sample_positions)]
        sample_predictions = data['predictions'][:len(sample_positions)]
        sample_confidences = data['confidences'][:len(sample_positions)]

        # 1. Spatial Distribution of True Labels
        scatter = axes[0, 0].scatter(sample_positions[:, 0], sample_positions[:, 1],
                                   c=sample_targets, cmap='RdYlBu', alpha=0.7, s=50)
        axes[0, 0].set_title('Spatial Distribution - True Labels')
        axes[0, 0].set_xlabel('X Position')
        axes[0, 0].set_ylabel('Y Position')
        plt.colorbar(scatter, ax=axes[0, 0], label='True Label')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. Spatial Distribution of Predictions
        scatter = axes[0, 1].scatter(sample_positions[:, 0], sample_positions[:, 1],
                                   c=sample_predictions, cmap='RdYlBu', alpha=0.7, s=50)
        axes[0, 1].set_title('Spatial Distribution - Predictions')
        axes[0, 1].set_xlabel('X Position')
        axes[0, 1].set_ylabel('Y Position')
        plt.colorbar(scatter, ax=axes[0, 1], label='Prediction')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. Spatial Distribution of Errors
        errors = np.abs(sample_targets - sample_predictions)
        scatter = axes[0, 2].scatter(sample_positions[:, 0], sample_positions[:, 1],
                                   c=errors, cmap='Reds', alpha=0.7, s=50)
        axes[0, 2].set_title('Spatial Distribution - Prediction Errors')
        axes[0, 2].set_xlabel('X Position')
        axes[0, 2].set_ylabel('Y Position')
        plt.colorbar(scatter, ax=axes[0, 2], label='Error')
        axes[0, 2].grid(True, alpha=0.3)

        # 4. Spatial Distribution of Confidence
        scatter = axes[1, 0].scatter(sample_positions[:, 0], sample_positions[:, 1],
                                   c=sample_confidences, cmap='viridis', alpha=0.7, s=50)
        axes[1, 0].set_title('Spatial Distribution - Confidence')
        axes[1, 0].set_xlabel('X Position')
        axes[1, 0].set_ylabel('Y Position')
        plt.colorbar(scatter, ax=axes[1, 0], label='Confidence')
        axes[1, 0].grid(True, alpha=0.3)

        # 5. Position-based Performance Analysis
        # Divide space into grid and calculate performance per cell
        x_bins = np.linspace(sample_positions[:, 0].min(), sample_positions[:, 0].max(), 10)
        y_bins = np.linspace(sample_positions[:, 1].min(), sample_positions[:, 1].max(), 10)

        accuracy_grid = np.zeros((len(y_bins)-1, len(x_bins)-1))
        count_grid = np.zeros((len(y_bins)-1, len(x_bins)-1))

        for i in range(len(y_bins)-1):
            for j in range(len(x_bins)-1):
                mask = ((sample_positions[:, 0] >= x_bins[j]) &
                       (sample_positions[:, 0] < x_bins[j+1]) &
                       (sample_positions[:, 1] >= y_bins[i]) &
                       (sample_positions[:, 1] < y_bins[i+1]))

                if mask.sum() > 0:
                    accuracy_grid[i, j] = (sample_targets[mask] == sample_predictions[mask]).mean()
                    count_grid[i, j] = mask.sum()

        im = axes[1, 1].imshow(accuracy_grid, cmap='RdYlGn', vmin=0, vmax=1,
                              extent=[x_bins[0], x_bins[-1], y_bins[0], y_bins[-1]],
                              origin='lower', alpha=0.8)
        axes[1, 1].set_title('Spatial Accuracy Heatmap')
        axes[1, 1].set_xlabel('X Position')
        axes[1, 1].set_ylabel('Y Position')
        plt.colorbar(im, ax=axes[1, 1], label='Accuracy')

        # 6. Sample Density Heatmap
        axes[1, 2].hist2d(sample_positions[:, 0], sample_positions[:, 1], bins=20, cmap='Blues')
        axes[1, 2].set_title('Sample Density Heatmap')
        axes[1, 2].set_xlabel('X Position')
        axes[1, 2].set_ylabel('Y Position')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, f'spatial_analysis_temporal_{temporal_window}.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ Spatial analysis saved")

    def create_statistical_analysis(self, data: Dict, temporal_window: int):
        """Create statistical analysis and model diagnostics."""
        print("Creating statistical analysis...")

        fig, axes = plt.subplots(3, 3, figsize=(18, 15))

        # 1. Residual Analysis (for regression-like interpretation)
        residuals = data['probabilities'] - data['targets']
        axes[0, 0].scatter(data['probabilities'], residuals, alpha=0.6, s=30)
        axes[0, 0].axhline(y=0, color='red', linestyle='--')
        axes[0, 0].set_xlabel('Predicted Probability')
        axes[0, 0].set_ylabel('Residuals')
        axes[0, 0].set_title('Residual Plot')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. Q-Q Plot for Residuals
        from scipy import stats
        stats.probplot(residuals, dist="norm", plot=axes[0, 1])
        axes[0, 1].set_title('Q-Q Plot of Residuals')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. Bootstrap Confidence Intervals for Metrics
        n_bootstrap = 1000
        bootstrap_metrics = {'accuracy': [], 'f1': [], 'roc_auc': []}

        for _ in range(n_bootstrap):
            indices = np.random.choice(len(data['targets']), len(data['targets']), replace=True)
            if len(np.unique(data['targets'][indices])) < 2:
                continue

            bootstrap_metrics['accuracy'].append(
                accuracy_score(data['targets'][indices], data['predictions'][indices]))
            bootstrap_metrics['f1'].append(
                f1_score(data['targets'][indices], data['predictions'][indices], zero_division=0))
            bootstrap_metrics['roc_auc'].append(
                roc_auc_score(data['targets'][indices], data['probabilities'][indices]))

        # Plot bootstrap distributions
        axes[0, 2].hist(bootstrap_metrics['accuracy'], bins=30, alpha=0.7, label='Accuracy')
        axes[0, 2].hist(bootstrap_metrics['f1'], bins=30, alpha=0.7, label='F1 Score')
        axes[0, 2].hist(bootstrap_metrics['roc_auc'], bins=30, alpha=0.7, label='ROC AUC')
        axes[0, 2].set_xlabel('Metric Value')
        axes[0, 2].set_ylabel('Frequency')
        axes[0, 2].set_title('Bootstrap Metric Distributions')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, f'statistical_analysis_temporal_{temporal_window}.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ Statistical analysis saved")

    def run_comprehensive_evaluation(self, temporal_window: int):
        """Run complete advanced evaluation pipeline."""
        print(f"\n🚀 Starting comprehensive test evaluation for {self.model_name} (temporal {temporal_window})")

        # Load model and data
        model, test_loader, checkpoint = self.load_model_and_test_data(temporal_window)

        # Perform comprehensive evaluation
        evaluation_data = self.comprehensive_test_evaluation(model, test_loader)

        # Create advanced visualizations
        self.create_advanced_performance_dashboard(evaluation_data, temporal_window, checkpoint)

        # Print summary statistics
        print(f"\n📊 Test Evaluation Summary:")
        print(f"Total test samples: {evaluation_data['total_samples']}")
        print(f"Accuracy: {accuracy_score(evaluation_data['targets'], evaluation_data['predictions']):.4f}")
        print(f"F1 Score: {f1_score(evaluation_data['targets'], evaluation_data['predictions']):.4f}")
        print(f"ROC AUC: {roc_auc_score(evaluation_data['targets'], evaluation_data['probabilities']):.4f}")

        return evaluation_data


def main():
    """Run advanced test evaluation for complex models."""

    # Model configurations for advanced evaluation
    models_to_evaluate = [
        {
            'name': 'Complex GATv2 (Temp 3)',
            'config_path': 'config.yaml',
            'checkpoint_path': 'checkpoints_gatv2_complex_4layers_temp3/model_temporal_3_best.pt',
            'output_dir': 'advanced_test_evaluation_complex_temp3',
            'temporal_window': 3
        },
        {
            'name': 'Complex GATv2 (Temp 5)',
            'config_path': 'config_complex_temp5.yaml',
            'checkpoint_path': 'checkpoints_gatv2_complex_temp5/model_temporal_5_best.pt',
            'output_dir': 'advanced_test_evaluation_complex_temp5',
            'temporal_window': 5
        },
        {
            'name': 'Enhanced GATv2 (Temp 3)',
            'config_path': 'config.yaml',  # Use base config and modify
            'checkpoint_path': 'checkpoints_enhanced_temp3/model_temporal_3_best.pt',
            'output_dir': 'advanced_test_evaluation_enhanced_temp3',
            'temporal_window': 3
        }
    ]

    all_results = {}

    for model_config in models_to_evaluate:
        try:
            # Load configuration
            with open(model_config['config_path'], 'r') as f:
                config = yaml.safe_load(f)

            # Adjust input dimension
            config['model']['input_dim'] = 10

            # Special handling for Enhanced model
            if 'Enhanced' in model_config['name']:
                config['model']['gnn_type'] = 'gatv2'  # Use gatv2 as base
                config['model']['hidden_dim'] = 192
                config['model']['attention_heads'] = 8
                config['model']['num_layers'] = 4
                config['model']['skip_connections'] = True
                config['model']['layer_norm'] = True

            # Create evaluator
            evaluator = AdvancedTestEvaluator(
                config=config,
                model_name=model_config['name'],
                checkpoint_path=model_config['checkpoint_path'],
                output_dir=model_config['output_dir']
            )

            # Run evaluation
            results = evaluator.run_comprehensive_evaluation(model_config['temporal_window'])
            all_results[model_config['name']] = results

        except Exception as e:
            print(f"❌ Error evaluating {model_config['name']}: {e}")
            continue

    print("\n🎉 Advanced test evaluation completed for all models!")
    print(f"📁 Results saved in respective output directories")


if __name__ == "__main__":
    main()
