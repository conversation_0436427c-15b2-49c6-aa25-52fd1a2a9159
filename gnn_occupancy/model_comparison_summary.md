# GNN-based Occupancy Prediction Model Comparison

## Summary of Results

This document provides a comprehensive comparison of the GNN models trained with different configurations for the occupancy prediction task. The models were evaluated on their ability to predict whether a space is occupied or unoccupied based on point cloud data.

## Model Configurations

All models share the following base configuration:
- Hidden Dimension: 64
- Number of Layers: 3
- Dropout: 0.2
- Skip Connections: Enabled
- Batch Normalization: Enabled
- Pooling: mean_max

The models differ in:
1. **GNN Type**: GATv2 (Graph Attention Network v2) vs. GraphSAGE (Graph Sample and Aggregate)
2. **Data Source**: Combined data (old + new) vs. Old data only
3. **Temporal Window Size**: 3 vs. 5 frames

## Performance Metrics Comparison

| Metric | GATv2 (Combined, Temp 3) | GATv2 (Combined, Temp 5) | GATv2 (Old Data, Temp 3) | GATv2 (Old Data, Temp 5) | GraphSAGE (Old Data, Temp 3) | GraphSAGE (Old Data, Temp 5) |
|--------|--------------------------|--------------------------|--------------------------|--------------------------|------------------------------|------------------------------|
| Test Accuracy | 0.6617 | 0.6385 | 0.6973 | **0.7521** | 0.7304 | 0.7369 |
| Test Precision | 0.5908 | 0.5739 | 0.6585 | **0.7141** | 0.7036 | 0.7066 |
| Test Recall | 0.8383 | 0.8359 | **0.9508** | 0.9434 | 0.8932 | 0.9218 |
| Test F1 | 0.6931 | 0.6806 | 0.7781 | **0.8129** | 0.7872 | 0.8000 |
| Test ROC AUC | 0.6948 | 0.6896 | 0.7533 | 0.7718 | 0.7613 | **0.7984** |
| R-squared (accuracy) | 0.2770 | 0.2770 | 0.3946 | **0.5042** | 0.4608 | 0.4738 |

*Bold values indicate the best performance for each metric.*

## Training Process Comparison

| Model | Epochs to Converge | Early Stopping Patience | Best Validation F1 |
|-------|--------------------|-----------------------|-------------------|
| GATv2 (Combined, Temp 3) | 22 | 20 | 0.6931 |
| GATv2 (Combined, Temp 5) | 32 | 20 | 0.6806 |
| GATv2 (Old Data, Temp 3) | 23 | 20 | 0.7177 |
| GATv2 (Old Data, Temp 5) | 35 | 20 | 0.7274 |
| GraphSAGE (Old Data, Temp 3) | 46 | 20 | 0.7486 |
| GraphSAGE (Old Data, Temp 5) | 57 | 20 | 0.7656 |

## Confusion Matrix Analysis

### GATv2 (Old Data, Temp 5) - Best Overall Model
- True Positives: 47.17% (excellent at identifying occupied spaces)
- False Negatives: 2.83% (very few occupied spaces missed)
- True Negatives: 31.11% (good at identifying unoccupied spaces)
- False Positives: 18.89% (some unoccupied spaces incorrectly classified as occupied)

### GATv2 (Old Data, Temp 3)
- True Positives: 47.54% (excellent at identifying occupied spaces)
- False Negatives: 2.46% (very few occupied spaces missed)
- True Negatives: 25.35% (moderate at identifying unoccupied spaces)
- False Positives: 24.65% (significant number of false positives)

### GraphSAGE (Old Data, Temp 3)
- True Positives: 44.66% (very good at identifying occupied spaces)
- False Negatives: 5.34% (few occupied spaces missed)
- True Negatives: 31.19% (good at identifying unoccupied spaces)
- False Positives: 18.81% (some unoccupied spaces incorrectly classified as occupied)

### GraphSAGE (Old Data, Temp 5) - Best ROC AUC
- True Positives: 46.09% (excellent at identifying occupied spaces)
- False Negatives: 3.91% (very few occupied spaces missed)
- True Negatives: 30.86% (good at identifying unoccupied spaces)
- False Positives: 19.14% (some unoccupied spaces incorrectly classified as occupied)

### GATv2 (Combined Data, Temp 3 & 5)
Both models trained on combined data showed lower precision and higher false positive rates compared to the models trained on old data only.

## Key Findings

1. **Data Quality vs. Quantity**: Models trained on old data only significantly outperform models trained on the combined dataset, suggesting that data quality and consistency are more important than data quantity for this task.

2. **GNN Architecture Impact**:
   - GATv2 models generally achieve higher recall but lower precision compared to GraphSAGE.
   - GraphSAGE achieves a better balance between precision and recall, with fewer false positives but slightly more false negatives.
   - For applications where false positives are more costly, GraphSAGE might be preferred.
   - GraphSAGE with temporal window 5 achieves the highest ROC AUC (79.84%), indicating excellent discriminative ability.

3. **Temporal Window Impact**:
   - For combined data: Increasing the temporal window from 3 to 5 slightly decreased performance, suggesting that additional temporal information may introduce noise or inconsistencies.
   - For old data only: Increasing the temporal window from 3 to 5 improved performance for both GATv2 and GraphSAGE, indicating that the temporal information in the old data is valuable and consistent.

4. **Precision vs. Recall Trade-off**:
   - All models achieve high recall (>83%), indicating they rarely miss occupied spaces.
   - Models trained on old data achieve higher precision, with the GATv2 temporal window 5 model showing the best balance between precision and recall.
   - GraphSAGE models show a better precision-recall balance than GATv2 models with the same temporal window.

5. **Best Overall Model**: The GATv2 model with temporal window 5 trained on old data achieves the best performance across most metrics, with:
   - Highest accuracy (75.21%)
   - Highest precision (71.41%)
   - Second-highest recall (94.34%)
   - Highest F1 score (81.29%)
   - Second-highest ROC AUC (77.18%)
   - Highest R-squared (50.42%)

6. **Best ROC AUC Model**: The GraphSAGE model with temporal window 5 trained on old data achieves the highest ROC AUC (79.84%), indicating the best discriminative ability among all models.

7. **Training Convergence**:
   - GATv2 models trained on old data required more epochs to converge when using a larger temporal window (35 vs. 23 epochs), suggesting that learning temporal patterns requires more training iterations.
   - GraphSAGE required the most epochs to converge (46 for temporal window 3 and 57 for temporal window 5), indicating it might be more difficult to train but can achieve comparable or better performance.

## Recommendations

1. **Model Selection**:
   - For highest overall performance: The GATv2 model with temporal window 5 trained on old data is recommended due to its superior performance across most metrics, especially F1 score.
   - For applications where false positives are more costly: The GraphSAGE model with temporal window 3 trained on old data offers a better balance with fewer false positives.
   - For best discriminative ability: The GraphSAGE model with temporal window 5 trained on old data achieves the highest ROC AUC, making it ideal for applications where ranking predictions correctly is critical.

2. **Data Processing**: Focus on data quality and consistency rather than simply increasing the dataset size. The results suggest that the new data may have different characteristics or noise patterns that affect model performance.

3. **GNN Architecture**: Both GATv2 and GraphSAGE are effective for occupancy prediction tasks, with different strengths:
   - GATv2: Higher recall, better for applications where missing occupied spaces is critical
   - GraphSAGE: Better precision, better for applications where false alarms are costly
   - GraphSAGE with temporal window 5: Best ROC AUC, ideal for ranking predictions correctly

4. **Temporal Information**: For high-quality data, incorporating longer temporal windows (5 frames) improves performance by capturing more temporal patterns and relationships for both GATv2 and GraphSAGE models.

5. **Future Work**:
   - Investigate why the new data leads to decreased performance when combined with old data.
   - Consider training separate models for different data sources if they have significantly different characteristics.
   - Explore data harmonization techniques to make the new data more compatible with the old data.
   - Experiment with even longer temporal windows to see if performance continues to improve.
   - Try other GNN architectures like ECC (Edge-Conditioned Convolution) to compare performance.
   - Investigate ensemble methods combining GATv2 and GraphSAGE models to potentially achieve better performance.

## Conclusion

The results demonstrate that both GATv2 and GraphSAGE architectures are effective for occupancy prediction tasks using point cloud data. However, the quality and consistency of the training data have a more significant impact on model performance than the specific architectural choices or the size of the dataset.

The GATv2 model with temporal window 5 trained on old data achieves high accuracy (75.21%) and an excellent balance between precision (71.41%) and recall (94.34%), making it suitable for real-world deployment in robotics applications where both avoiding collisions (high recall) and efficient path planning (reasonable precision) are important.

The GraphSAGE model with temporal window 5 trained on old data achieves the highest ROC AUC (79.84%), indicating superior discriminative ability, while maintaining good accuracy (73.69%), precision (70.66%), and recall (92.18%).

For applications with different precision-recall requirements, these models offer alternatives that can be selected based on the specific needs of the application.

Date: May 22, 2025
