#!/usr/bin/env python3

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse

def create_confusion_matrix_from_metrics(model_dir, accuracy, precision, recall):
    """
    Create a confusion matrix visualization based on accuracy, precision, and recall.
    
    Args:
        model_dir: Directory to save the confusion matrix
        accuracy: Accuracy value
        precision: Precision value
        recall: Recall value
    """
    # Calculate confusion matrix values
    # For binary classification with 100 samples:
    # TP + TN + FP + FN = 100
    # accuracy = (TP + TN) / 100
    # precision = TP / (TP + FP)
    # recall = TP / (TP + FN)
    
    # Let's assume we have 100 samples with 50 positive and 50 negative
    n_samples = 100
    n_pos = 50
    n_neg = 50
    
    # Calculate TP, FP, TN, FN
    tp = recall * n_pos
    fn = n_pos - tp
    fp = tp / precision - tp
    tn = n_neg - fp
    
    # Create confusion matrix
    cm = np.array([[tn, fp], [fn, tp]])
    
    # Create directory for visualizations if it doesn't exist
    vis_dir = os.path.join(model_dir, "visualizations")
    os.makedirs(vis_dir, exist_ok=True)
    
    # Plot confusion matrix
    plt.figure(figsize=(10, 8))
    
    # Use seaborn for a nicer heatmap
    sns.heatmap(cm, annot=True, fmt='.1f', cmap='Blues', 
                xticklabels=['Unoccupied', 'Occupied'],
                yticklabels=['Unoccupied', 'Occupied'])
    
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title(f'Confusion Matrix (Reconstructed from Metrics)')
    
    # Save the figure
    cm_path = os.path.join(vis_dir, "confusion_matrix.png")
    plt.savefig(cm_path, dpi=300, bbox_inches='tight')
    
    # Also save to the main directory for easy access
    cm_path_main = os.path.join(model_dir, "confusion_matrix.png")
    plt.savefig(cm_path_main, dpi=300, bbox_inches='tight')
    
    print(f"Confusion matrix saved to {cm_path} and {cm_path_main}")
    
    # Print the confusion matrix
    print(f"\nConfusion Matrix (normalized to 100 samples):\n{cm}")
    
    # Calculate and print metrics
    calculated_accuracy = (tp + tn) / (tp + tn + fp + fn)
    calculated_precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    calculated_recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * calculated_precision * calculated_recall / (calculated_precision + calculated_recall) if (calculated_precision + calculated_recall) > 0 else 0
    
    print(f"\nMetrics:")
    print(f"  Accuracy:  {calculated_accuracy:.4f} (input: {accuracy:.4f})")
    print(f"  Precision: {calculated_precision:.4f} (input: {precision:.4f})")
    print(f"  Recall:    {calculated_recall:.4f} (input: {recall:.4f})")
    print(f"  F1 Score:  {f1:.4f}")
    
    return cm

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Create confusion matrix from metrics")
    parser.add_argument(
        "--model_dir",
        type=str,
        required=True,
        help="Directory to save the confusion matrix",
    )
    parser.add_argument(
        "--accuracy",
        type=float,
        required=True,
        help="Accuracy value",
    )
    parser.add_argument(
        "--precision",
        type=float,
        required=True,
        help="Precision value",
    )
    parser.add_argument(
        "--recall",
        type=float,
        required=True,
        help="Recall value",
    )
    
    args = parser.parse_args()
    
    cm = create_confusion_matrix_from_metrics(
        args.model_dir,
        args.accuracy,
        args.precision,
        args.recall
    )
