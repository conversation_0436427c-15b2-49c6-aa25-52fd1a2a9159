#!/usr/bin/env python3
"""
Step 1: Arena Data Analysis and Characterization
Analyze the spatial data to understand the experimental arena layout and characteristics.
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json
from datetime import datetime
from tqdm import tqdm

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data import OccupancyDataset
from torch_geometric.data import DataLoader


@dataclass
class ArenaCharacteristics:
    """Data class to store arena characteristics."""
    # Spatial dimensions
    min_x: float
    max_x: float
    min_y: float
    max_y: float
    width: float
    height: float
    total_area: float
    
    # Grid properties
    grid_resolution: float
    grid_width: int
    grid_height: int
    total_grid_cells: int
    
    # Data coverage
    total_samples: int
    unique_positions: int
    spatial_coverage_percentage: float
    temporal_span_samples: int
    
    # Occupancy statistics
    occupied_samples: int
    free_samples: int
    occupancy_ratio: float
    
    # Data quality metrics
    samples_per_square_meter: float
    under_sampled_threshold: float
    well_sampled_regions: int
    under_sampled_regions: int


class ArenaAnalyzer:
    """Comprehensive arena data analyzer."""
    
    def __init__(self, data_dir: str = "../data/07_gnn_ready", temporal_window: int = 3):
        self.data_dir = Path(data_dir)
        self.temporal_window = temporal_window
        self.results_dir = Path("results/arena_characterization")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Analysis results storage
        self.all_positions = None
        self.all_labels = None
        self.arena_chars = None
        self.spatial_grid = None
        self.density_grid = None
        self.occupancy_grid = None
        
    def load_and_analyze_data(self):
        """Load all test data and perform comprehensive spatial analysis."""
        print("🔍 Step 1: Loading and analyzing arena data...")
        
        # Create dataset
        dataset = OccupancyDataset(
            root=str(self.data_dir),
            split='test',
            temporal_window=self.temporal_window,
            binary_mapping={'occupied': [1, 2, 3, 4], 'unoccupied': [0]}
        )
        
        # Create data loader
        data_loader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
        
        print(f"📊 Processing {len(dataset)} test samples...")
        
        # Collect all spatial data
        all_positions = []
        all_labels = []
        all_features = []
        
        for batch_idx, batch in enumerate(tqdm(data_loader, desc="Analyzing spatial data")):
            try:
                # Extract positions (X, Y coordinates)
                if batch.pos.shape[1] >= 2:
                    positions = batch.pos[:, :2].numpy()  # X, Y coordinates
                else:
                    # Fallback to features if pos doesn't have X,Y
                    positions = batch.x[:, 2:4].numpy()  # X,Y from features
                
                labels = batch.y.numpy().flatten()
                features = batch.x.numpy()
                
                all_positions.append(positions)
                all_labels.append(labels)
                all_features.append(features)
                
            except Exception as e:
                print(f"  ⚠️ Error processing batch {batch_idx}: {e}")
                continue
        
        # Combine all data
        self.all_positions = np.vstack(all_positions)
        self.all_labels = np.concatenate(all_labels)
        self.all_features = np.vstack(all_features)
        
        print(f"✅ Loaded {len(self.all_positions)} spatial points")
        print(f"📍 Position range: X[{self.all_positions[:, 0].min():.2f}, {self.all_positions[:, 0].max():.2f}], Y[{self.all_positions[:, 1].min():.2f}, {self.all_positions[:, 1].max():.2f}]")
        
    def calculate_arena_characteristics(self, grid_resolution: float = 0.1):
        """Calculate comprehensive arena characteristics."""
        print("📏 Calculating arena characteristics...")
        
        if self.all_positions is None:
            raise ValueError("Data not loaded. Call load_and_analyze_data() first.")
        
        # Spatial dimensions
        min_x, min_y = self.all_positions.min(axis=0)
        max_x, max_y = self.all_positions.max(axis=0)
        width = max_x - min_x
        height = max_y - min_y
        total_area = width * height
        
        # Grid properties
        grid_width = int(np.ceil(width / grid_resolution))
        grid_height = int(np.ceil(height / grid_resolution))
        total_grid_cells = grid_width * grid_height
        
        # Data coverage analysis
        total_samples = len(self.all_positions)
        unique_positions = len(np.unique(self.all_positions, axis=0))
        
        # Calculate spatial coverage
        grid_x = ((self.all_positions[:, 0] - min_x) / grid_resolution).astype(int)
        grid_y = ((self.all_positions[:, 1] - min_y) / grid_resolution).astype(int)
        grid_x = np.clip(grid_x, 0, grid_width - 1)
        grid_y = np.clip(grid_y, 0, grid_height - 1)
        
        # Create coverage grid
        coverage_grid = np.zeros((grid_height, grid_width), dtype=bool)
        coverage_grid[grid_y, grid_x] = True
        covered_cells = np.sum(coverage_grid)
        spatial_coverage_percentage = (covered_cells / total_grid_cells) * 100
        
        # Occupancy statistics
        occupied_samples = np.sum(self.all_labels > 0.5)
        free_samples = np.sum(self.all_labels <= 0.5)
        occupancy_ratio = occupied_samples / total_samples
        
        # Data quality metrics
        samples_per_square_meter = total_samples / total_area
        
        # Create density grid for quality assessment
        density_grid = np.zeros((grid_height, grid_width), dtype=int)
        for i in range(len(self.all_positions)):
            y, x = grid_y[i], grid_x[i]
            density_grid[y, x] += 1
        
        # Define under-sampled threshold (e.g., less than 10% of average density)
        avg_density = np.mean(density_grid[density_grid > 0])
        under_sampled_threshold = avg_density * 0.1
        
        well_sampled_regions = np.sum(density_grid >= under_sampled_threshold)
        under_sampled_regions = np.sum((density_grid > 0) & (density_grid < under_sampled_threshold))
        
        # Store grids for visualization
        self.spatial_grid = coverage_grid
        self.density_grid = density_grid
        
        # Create occupancy grid
        occupancy_grid = np.zeros((grid_height, grid_width), dtype=float)
        occupancy_count = np.zeros((grid_height, grid_width), dtype=int)
        
        for i in range(len(self.all_positions)):
            y, x = grid_y[i], grid_x[i]
            occupancy_grid[y, x] += self.all_labels[i]
            occupancy_count[y, x] += 1
        
        # Average occupancy per cell
        valid_cells = occupancy_count > 0
        occupancy_grid[valid_cells] = occupancy_grid[valid_cells] / occupancy_count[valid_cells]
        self.occupancy_grid = occupancy_grid
        
        # Create arena characteristics object
        self.arena_chars = ArenaCharacteristics(
            min_x=float(min_x),
            max_x=float(max_x),
            min_y=float(min_y),
            max_y=float(max_y),
            width=float(width),
            height=float(height),
            total_area=float(total_area),
            grid_resolution=grid_resolution,
            grid_width=grid_width,
            grid_height=grid_height,
            total_grid_cells=total_grid_cells,
            total_samples=total_samples,
            unique_positions=unique_positions,
            spatial_coverage_percentage=float(spatial_coverage_percentage),
            temporal_span_samples=total_samples,  # Simplified for now
            occupied_samples=int(occupied_samples),
            free_samples=int(free_samples),
            occupancy_ratio=float(occupancy_ratio),
            samples_per_square_meter=float(samples_per_square_meter),
            under_sampled_threshold=float(under_sampled_threshold),
            well_sampled_regions=int(well_sampled_regions),
            under_sampled_regions=int(under_sampled_regions)
        )
        
        print(f"✅ Arena characteristics calculated")
        self._print_arena_summary()
        
    def _print_arena_summary(self):
        """Print a summary of arena characteristics."""
        chars = self.arena_chars
        print(f"\n📊 ARENA CHARACTERISTICS SUMMARY")
        print(f"=" * 50)
        print(f"🏟️  Arena Dimensions:")
        print(f"   Width: {chars.width:.2f}m, Height: {chars.height:.2f}m")
        print(f"   Total Area: {chars.total_area:.2f}m²")
        print(f"   Bounds: X[{chars.min_x:.2f}, {chars.max_x:.2f}], Y[{chars.min_y:.2f}, {chars.max_y:.2f}]")
        
        print(f"\n📏 Grid Properties:")
        print(f"   Resolution: {chars.grid_resolution}m per cell")
        print(f"   Grid Size: {chars.grid_width} × {chars.grid_height} cells")
        print(f"   Total Cells: {chars.total_grid_cells:,}")
        
        print(f"\n📈 Data Coverage:")
        print(f"   Total Samples: {chars.total_samples:,}")
        print(f"   Unique Positions: {chars.unique_positions:,}")
        print(f"   Spatial Coverage: {chars.spatial_coverage_percentage:.1f}%")
        print(f"   Sample Density: {chars.samples_per_square_meter:.1f} samples/m²")
        
        print(f"\n🎯 Occupancy Statistics:")
        print(f"   Occupied Samples: {chars.occupied_samples:,} ({chars.occupancy_ratio*100:.1f}%)")
        print(f"   Free Samples: {chars.free_samples:,} ({(1-chars.occupancy_ratio)*100:.1f}%)")
        
        print(f"\n📊 Data Quality:")
        print(f"   Well-sampled Regions: {chars.well_sampled_regions:,}")
        print(f"   Under-sampled Regions: {chars.under_sampled_regions:,}")
        print(f"   Under-sampling Threshold: {chars.under_sampled_threshold:.1f} samples/cell")
        
    def save_analysis_results(self):
        """Save analysis results to files."""
        print("💾 Saving arena analysis results...")
        
        if self.arena_chars is None:
            raise ValueError("Analysis not completed. Run calculate_arena_characteristics() first.")
        
        # Save arena characteristics as JSON
        chars_dict = {
            'analysis_date': datetime.now().isoformat(),
            'temporal_window': self.temporal_window,
            'spatial_dimensions': {
                'min_x': self.arena_chars.min_x,
                'max_x': self.arena_chars.max_x,
                'min_y': self.arena_chars.min_y,
                'max_y': self.arena_chars.max_y,
                'width': self.arena_chars.width,
                'height': self.arena_chars.height,
                'total_area': self.arena_chars.total_area
            },
            'grid_properties': {
                'resolution': self.arena_chars.grid_resolution,
                'grid_width': self.arena_chars.grid_width,
                'grid_height': self.arena_chars.grid_height,
                'total_cells': self.arena_chars.total_grid_cells
            },
            'data_coverage': {
                'total_samples': self.arena_chars.total_samples,
                'unique_positions': self.arena_chars.unique_positions,
                'spatial_coverage_percentage': self.arena_chars.spatial_coverage_percentage,
                'samples_per_square_meter': self.arena_chars.samples_per_square_meter
            },
            'occupancy_statistics': {
                'occupied_samples': self.arena_chars.occupied_samples,
                'free_samples': self.arena_chars.free_samples,
                'occupancy_ratio': self.arena_chars.occupancy_ratio
            },
            'data_quality': {
                'well_sampled_regions': self.arena_chars.well_sampled_regions,
                'under_sampled_regions': self.arena_chars.under_sampled_regions,
                'under_sampled_threshold': self.arena_chars.under_sampled_threshold
            }
        }
        
        # Save JSON
        json_path = self.results_dir / 'arena_characteristics.json'
        with open(json_path, 'w') as f:
            json.dump(chars_dict, f, indent=2)
        
        # Save grids as numpy arrays
        np.save(self.results_dir / 'spatial_coverage_grid.npy', self.spatial_grid)
        np.save(self.results_dir / 'density_grid.npy', self.density_grid)
        np.save(self.results_dir / 'occupancy_grid.npy', self.occupancy_grid)
        
        # Save raw data
        np.save(self.results_dir / 'all_positions.npy', self.all_positions)
        np.save(self.results_dir / 'all_labels.npy', self.all_labels)
        
        print(f"✅ Analysis results saved to: {self.results_dir}")
        print(f"   📄 arena_characteristics.json")
        print(f"   📊 spatial_coverage_grid.npy")
        print(f"   📊 density_grid.npy")
        print(f"   📊 occupancy_grid.npy")
        print(f"   📍 all_positions.npy")
        print(f"   🏷️ all_labels.npy")
        
    def run_step1_analysis(self):
        """Run complete Step 1 analysis."""
        print("🚀 Starting Step 1: Arena Data Analysis and Characterization")
        print("=" * 70)
        
        try:
            # Load and analyze data
            self.load_and_analyze_data()
            
            # Calculate characteristics
            self.calculate_arena_characteristics()
            
            # Save results
            self.save_analysis_results()
            
            print("\n" + "=" * 70)
            print("🎉 STEP 1 COMPLETED SUCCESSFULLY!")
            print("=" * 70)
            print(f"📁 Results saved to: {self.results_dir}")
            print("🔄 Ready for Step 2: Basic Visualization Creation")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Step 1 failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function for Step 1."""
    print("🔬 Arena Characterization System - Step 1")
    print("📊 Data Analysis and Spatial Characterization")
    print("=" * 80)
    
    analyzer = ArenaAnalyzer(
        data_dir="../data/07_gnn_ready",
        temporal_window=3
    )
    
    success = analyzer.run_step1_analysis()
    
    if success:
        print("\n✅ Step 1 completed! Proceed to Step 2 for visualization creation.")
    else:
        print("\n❌ Step 1 failed. Check the error messages above.")
    
    return success


if __name__ == "__main__":
    main()
