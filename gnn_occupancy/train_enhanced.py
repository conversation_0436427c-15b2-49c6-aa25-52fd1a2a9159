#!/usr/bin/env python3

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR, LinearLR, SequentialLR
import yaml
import logging
import numpy as np
from tqdm import tqdm
from typing import Dict, Tuple
import matplotlib.pyplot as plt
from pathlib import Path

from data import create_data_loaders
from enhanced_model import create_enhanced_model


def setup_logging(log_file: str = "enhanced_training.log"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )


class EnhancedTrainer:
    """Enhanced trainer with advanced optimization techniques."""

    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device(config["training"]["device"])

        # Create directories
        os.makedirs(config["training"]["checkpoint_dir"], exist_ok=True)
        os.makedirs(config["evaluation"]["visualization"]["save_dir"], exist_ok=True)

        # Setup logging
        setup_logging(os.path.join(config["training"]["checkpoint_dir"], "training.log"))
        self.logger = logging.getLogger(__name__)

    def create_optimizer(self, model: nn.Module) -> optim.Optimizer:
        """Create optimizer with advanced configuration."""
        optimizer_config = self.config["training"].get("optimizer", {})
        optimizer_type = optimizer_config.get("type", "adamw").lower()

        if optimizer_type == "adamw":
            optimizer = optim.AdamW(
                model.parameters(),
                lr=self.config["training"]["learning_rate"],
                weight_decay=self.config["training"]["weight_decay"],
                betas=optimizer_config.get("betas", [0.9, 0.999]),
                eps=optimizer_config.get("eps", 1e-8)
            )
        elif optimizer_type == "adam":
            optimizer = optim.Adam(
                model.parameters(),
                lr=self.config["training"]["learning_rate"],
                weight_decay=self.config["training"]["weight_decay"]
            )
        else:
            raise ValueError(f"Unknown optimizer type: {optimizer_type}")

        return optimizer

    def create_scheduler(self, optimizer: optim.Optimizer) -> optim.lr_scheduler._LRScheduler:
        """Create learning rate scheduler."""
        scheduler_config = self.config["training"].get("lr_scheduler", {})
        scheduler_type = scheduler_config.get("type", "cosine_annealing").lower()

        if scheduler_type == "cosine_annealing":
            main_scheduler = CosineAnnealingLR(
                optimizer,
                T_max=scheduler_config.get("T_max", self.config["training"]["epochs"]),
                eta_min=scheduler_config.get("eta_min", 1e-6)
            )
        else:
            main_scheduler = None

        # Add warmup if enabled
        warmup_config = self.config["training"].get("warmup", {})
        if warmup_config.get("enabled", False):
            warmup_epochs = warmup_config.get("warmup_epochs", 10)
            warmup_factor = warmup_config.get("warmup_factor", 0.1)

            warmup_scheduler = LinearLR(
                optimizer,
                start_factor=warmup_factor,
                end_factor=1.0,
                total_iters=warmup_epochs
            )

            if main_scheduler:
                scheduler = SequentialLR(
                    optimizer,
                    schedulers=[warmup_scheduler, main_scheduler],
                    milestones=[warmup_epochs]
                )
            else:
                scheduler = warmup_scheduler
        else:
            scheduler = main_scheduler

        return scheduler

    def train_epoch(
        self,
        model: nn.Module,
        train_loader: torch.utils.data.DataLoader,
        optimizer: optim.Optimizer,
        criterion: nn.Module,
        epoch: int
    ) -> Tuple[float, float, float]:
        """Train for one epoch."""
        model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        all_preds = []
        all_targets = []

        # Gradient clipping config
        grad_clip_config = self.config["training"].get("gradient_clipping", {})
        use_grad_clip = grad_clip_config.get("enabled", False)
        max_norm = grad_clip_config.get("max_norm", 1.0)

        progress_bar = tqdm(train_loader, desc=f"Training Epoch {epoch}")

        for batch in progress_bar:
            batch = batch.to(self.device)
            optimizer.zero_grad()

            # Forward pass
            logits = model(batch.x, batch.edge_index, batch.batch)

            # Handle different target shapes
            if logits.size(0) != batch.y.size(0):
                # Graph-level prediction
                graph_labels = []
                for i in range(batch.num_graphs):
                    mask = batch.batch == i
                    graph_label = batch.y[mask].float().mean().round()
                    graph_labels.append(graph_label)
                targets = torch.tensor(graph_labels, device=self.device).float().unsqueeze(1)
            else:
                targets = batch.y.float().unsqueeze(1)

            # Compute loss
            loss = criterion(logits, targets)

            # Backward pass
            loss.backward()

            # Gradient clipping
            if use_grad_clip:
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)

            optimizer.step()

            # Statistics
            total_loss += loss.item()
            probs = torch.sigmoid(logits)
            preds = (probs > 0.5).float()
            correct += (preds == targets).sum().item()
            total += targets.size(0)

            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())

            # Update progress bar
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{correct/total:.4f}'
            })

        # Calculate metrics
        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total

        # Calculate F1 score
        from sklearn.metrics import f1_score
        f1 = f1_score(all_targets, all_preds, zero_division=0)

        return avg_loss, accuracy, f1

    def validate(
        self,
        model: nn.Module,
        val_loader: torch.utils.data.DataLoader,
        criterion: nn.Module
    ) -> Tuple[float, float, float]:
        """Validate the model."""
        model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        all_preds = []
        all_targets = []

        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Validating"):
                batch = batch.to(self.device)

                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)

                # Handle different target shapes
                if logits.size(0) != batch.y.size(0):
                    # Graph-level prediction
                    graph_labels = []
                    for i in range(batch.num_graphs):
                        mask = batch.batch == i
                        graph_label = batch.y[mask].float().mean().round()
                        graph_labels.append(graph_label)
                    targets = torch.tensor(graph_labels, device=self.device).float().unsqueeze(1)
                else:
                    targets = batch.y.float().unsqueeze(1)

                # Compute loss
                loss = criterion(logits, targets)

                # Statistics
                total_loss += loss.item()
                probs = torch.sigmoid(logits)
                preds = (probs > 0.5).float()
                correct += (preds == targets).sum().item()
                total += targets.size(0)

                all_preds.extend(preds.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())

        # Calculate metrics
        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total

        # Calculate F1 score
        from sklearn.metrics import f1_score
        f1 = f1_score(all_targets, all_preds, zero_division=0)

        return avg_loss, accuracy, f1

    def train(self, temporal_window: int = 3):
        """Train the enhanced model."""
        self.logger.info("Starting enhanced model training...")

        # Create data loaders
        data_loaders = create_data_loaders(self.config)
        train_loader = data_loaders[f"temporal_{temporal_window}"]["train"]
        val_loader = data_loaders[f"temporal_{temporal_window}"]["val"]

        # Adjust input dimension based on temporal window
        if temporal_window > 1:
            self.config["model"]["input_dim"] = 10  # Additional temporal feature
        else:
            self.config["model"]["input_dim"] = 9   # Only spatial features

        # Create model
        model = create_enhanced_model(self.config)
        model = model.to(self.device)

        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        self.logger.info(f"Model created with {trainable_params:,} trainable parameters (total: {total_params:,})")

        # Create optimizer and scheduler
        optimizer = self.create_optimizer(model)
        scheduler = self.create_scheduler(optimizer)
        criterion = nn.BCEWithLogitsLoss()

        # Training loop
        best_f1 = 0.0
        patience_counter = 0
        patience = self.config["training"]["early_stopping"]["patience"]
        min_delta = self.config["training"]["early_stopping"]["min_delta"]

        history = {
            'train_loss': [], 'train_acc': [], 'train_f1': [],
            'val_loss': [], 'val_acc': [], 'val_f1': [], 'lr': []
        }

        for epoch in range(1, self.config["training"]["epochs"] + 1):
            # Training
            train_loss, train_acc, train_f1 = self.train_epoch(
                model, train_loader, optimizer, criterion, epoch
            )

            # Validation
            val_loss, val_acc, val_f1 = self.validate(model, val_loader, criterion)

            # Update learning rate
            if scheduler:
                scheduler.step()

            # Log metrics
            current_lr = optimizer.param_groups[0]['lr']
            self.logger.info(
                f"Epoch {epoch:3d}: "
                f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, Train F1: {train_f1:.4f} | "
                f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}, Val F1: {val_f1:.4f} | "
                f"LR: {current_lr:.6f}"
            )

            # Save history
            history['train_loss'].append(train_loss)
            history['train_acc'].append(train_acc)
            history['train_f1'].append(train_f1)
            history['val_loss'].append(val_loss)
            history['val_acc'].append(val_acc)
            history['val_f1'].append(val_f1)
            history['lr'].append(current_lr)

            # Early stopping and model saving
            if val_f1 > best_f1 + min_delta:
                best_f1 = val_f1
                patience_counter = 0

                # Save best model
                checkpoint_path = os.path.join(
                    self.config["training"]["checkpoint_dir"],
                    f"model_temporal_{temporal_window}_best.pt"
                )
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'best_f1': best_f1,
                    'config': self.config
                }, checkpoint_path)

                self.logger.info(f"New best model saved with F1: {best_f1:.4f}")
            else:
                patience_counter += 1

            # Check early stopping
            if patience_counter >= patience:
                self.logger.info(f"Early stopping triggered after {epoch} epochs")
                break

        # Save training history
        self.save_training_history(history, temporal_window)

        self.logger.info("Training completed!")
        return model, history

    def save_training_history(self, history: Dict, temporal_window: int):
        """Save training history plot."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # Loss plot
        axes[0, 0].plot(history['train_loss'], label='Train Loss')
        axes[0, 0].plot(history['val_loss'], label='Val Loss')
        axes[0, 0].set_title('Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)

        # Accuracy plot
        axes[0, 1].plot(history['train_acc'], label='Train Acc')
        axes[0, 1].plot(history['val_acc'], label='Val Acc')
        axes[0, 1].set_title('Accuracy')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True)

        # F1 plot
        axes[1, 0].plot(history['train_f1'], label='Train F1')
        axes[1, 0].plot(history['val_f1'], label='Val F1')
        axes[1, 0].set_title('F1 Score')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('F1 Score')
        axes[1, 0].legend()
        axes[1, 0].grid(True)

        # Learning rate plot
        axes[1, 1].plot(history['lr'])
        axes[1, 1].set_title('Learning Rate')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Learning Rate')
        axes[1, 1].grid(True)

        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["training"]["checkpoint_dir"],
            f"history_temporal_{temporal_window}.png"
        ))
        plt.close()


if __name__ == "__main__":
    # Load configuration
    with open("config_enhanced.yaml", "r") as f:
        config = yaml.safe_load(f)

    # Create trainer
    trainer = EnhancedTrainer(config)

    # Train model
    model, history = trainer.train(temporal_window=3)

    print("Enhanced model training completed!")
    print(f"Best validation F1: {max(history['val_f1']):.4f}")
    print(f"Checkpoints saved to: {config['training']['checkpoint_dir']}")
    print(f"Visualizations will be saved to: {config['evaluation']['visualization']['save_dir']}")
