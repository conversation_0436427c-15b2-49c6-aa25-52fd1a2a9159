#!/usr/bin/env python3
"""
Debug script to understand the data format for IoU evaluation.
"""

import torch
from data import OccupancyDataset
from torch_geometric.data import DataLoader

def debug_data_format():
    """Debug the data format to understand the structure."""
    print("🔍 Debugging data format...")
    
    # Create dataset
    dataset = OccupancyDataset(
        root="../data/07_gnn_ready",
        split='test',
        temporal_window=3,
        binary_mapping={'occupied': [1, 2, 3, 4], 'unoccupied': [0]}
    )
    
    # Create data loader
    data_loader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # Get first batch
    batch = next(iter(data_loader))
    
    print(f"📊 Batch information:")
    print(f"  - batch.x.shape: {batch.x.shape}")
    print(f"  - batch.y.shape: {batch.y.shape}")
    print(f"  - batch.pos.shape: {batch.pos.shape}")
    print(f"  - batch.edge_index.shape: {batch.edge_index.shape}")
    
    print(f"\n📍 Position data sample:")
    print(f"  - batch.pos[:5]: {batch.pos[:5]}")
    
    print(f"\n🎯 Feature data sample:")
    print(f"  - batch.x[:5]: {batch.x[:5]}")
    
    print(f"\n🏷️ Label data sample:")
    print(f"  - batch.y[:10]: {batch.y[:10]}")
    print(f"  - Unique labels: {torch.unique(batch.y)}")
    
    # Check if X,Y coordinates are in features
    if batch.x.shape[1] >= 2:
        print(f"\n📐 X,Y from features:")
        print(f"  - batch.x[:5, :2]: {batch.x[:5, :2]}")
    
    # Check if X,Y coordinates are in different positions
    if batch.x.shape[1] >= 5:
        print(f"\n📐 X,Y from features (positions 2,3):")
        print(f"  - batch.x[:5, 2:4]: {batch.x[:5, 2:4]}")
    
    return batch

if __name__ == "__main__":
    batch = debug_data_format()
