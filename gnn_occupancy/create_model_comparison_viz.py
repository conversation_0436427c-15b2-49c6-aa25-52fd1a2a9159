#!/usr/bin/env python3

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
import os
from PIL import Image
import glob

# Set style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

def create_comprehensive_model_comparison():
    """Create comprehensive model comparison visualizations."""
    
    # Model performance data
    models_data = {
        'Model': [
            'GraphSAGE (Old Data)',
            'Complex GATv2 (Temp 3)',
            'Complex GATv2 (Temp 5)', 
            'Enhanced GATv2 (Temp 3)',
            'GATv2 Standard (Temp 3)',
            'GATv2 (Temp 5)',
            'GATv2 5-Layer (Temp 3)'
        ],
        'Accuracy': [73.04, 72.84, 70.03, 67.25, 66.17, 63.85, 57.83],
        'Precision': [70.36, 71.05, 66.93, 60.14, 59.08, 57.39, 52.09],
        'Recall': [89.32, 68.17, 69.08, 83.46, 83.83, 83.59, 93.06],
        'F1_Score': [78.72, 69.58, 67.99, 69.90, 69.31, 68.06, 66.79],
        'ROC_AUC': [76.13, 79.93, 77.61, 71.85, 69.48, 68.96, 64.89],
        'Parameters': [15000, 169601, 169601, 6045313, 25000, 30000, 51905],
        'Training_Time_Hours': [2, 4, 2.5, 6, 1, 1.5, 1],
        'Epochs': [46, 86, 59, 11, 22, 32, 21],
        'Data_Type': ['Old', 'Current', 'Current', 'Current', 'Current', 'Current', 'Current']
    }
    
    df = pd.DataFrame(models_data)
    
    # Create comprehensive comparison figure
    fig = plt.figure(figsize=(24, 16))
    
    # 1. Performance Metrics Radar Chart
    ax1 = plt.subplot(3, 4, 1, projection='polar')
    
    # Select top 4 models for radar chart
    top_models = df.nlargest(4, 'F1_Score')
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1_Score', 'ROC_AUC']
    
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # Complete the circle
    
    colors = ['red', 'blue', 'green', 'orange']
    for i, (idx, model) in enumerate(top_models.iterrows()):
        values = [model[metric]/100 for metric in metrics]
        values += values[:1]  # Complete the circle
        
        ax1.plot(angles, values, 'o-', linewidth=2, label=model['Model'], color=colors[i])
        ax1.fill(angles, values, alpha=0.1, color=colors[i])
    
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(metrics)
    ax1.set_ylim(0, 1)
    ax1.set_title('Top 4 Models - Performance Radar', size=12, weight='bold')
    ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    # 2. Accuracy vs Parameters Scatter
    ax2 = plt.subplot(3, 4, 2)
    colors = ['red' if dt == 'Old' else 'blue' for dt in df['Data_Type']]
    scatter = ax2.scatter(df['Parameters'], df['Accuracy'], c=colors, s=100, alpha=0.7)
    
    for i, model in enumerate(df['Model']):
        ax2.annotate(model.split('(')[0].strip(), 
                    (df['Parameters'].iloc[i], df['Accuracy'].iloc[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    ax2.set_xscale('log')
    ax2.set_xlabel('Parameters (log scale)')
    ax2.set_ylabel('Accuracy (%)')
    ax2.set_title('Accuracy vs Model Complexity')
    ax2.grid(True, alpha=0.3)
    
    # Add legend for data type
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor='red', label='Old Data'),
                      Patch(facecolor='blue', label='Current Data')]
    ax2.legend(handles=legend_elements)
    
    # 3. Training Efficiency Analysis
    ax3 = plt.subplot(3, 4, 3)
    efficiency = df['Accuracy'] / df['Training_Time_Hours']
    bars = ax3.bar(range(len(df)), efficiency, color='skyblue', alpha=0.7)
    ax3.set_xlabel('Model')
    ax3.set_ylabel('Accuracy per Hour')
    ax3.set_title('Training Efficiency')
    ax3.set_xticks(range(len(df)))
    ax3.set_xticklabels([m.split('(')[0].strip() for m in df['Model']], rotation=45)
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}', ha='center', va='bottom', fontsize=8)
    
    # 4. F1 Score Comparison
    ax4 = plt.subplot(3, 4, 4)
    colors = ['gold' if 'Complex' in model else 'lightblue' for model in df['Model']]
    bars = ax4.barh(range(len(df)), df['F1_Score'], color=colors, alpha=0.8)
    ax4.set_yticks(range(len(df)))
    ax4.set_yticklabels([m.split('(')[0].strip() for m in df['Model']])
    ax4.set_xlabel('F1 Score (%)')
    ax4.set_title('F1 Score Comparison')
    
    # Add value labels
    for i, bar in enumerate(bars):
        width = bar.get_width()
        ax4.text(width + 0.5, bar.get_y() + bar.get_height()/2.,
                f'{width:.1f}%', ha='left', va='center', fontsize=9)
    
    # 5. Parameter Efficiency
    ax5 = plt.subplot(3, 4, 5)
    param_efficiency = df['F1_Score'] / (df['Parameters'] / 1000)  # F1 per 1K parameters
    bars = ax5.bar(range(len(df)), param_efficiency, color='lightgreen', alpha=0.7)
    ax5.set_xlabel('Model')
    ax5.set_ylabel('F1 Score per 1K Parameters')
    ax5.set_title('Parameter Efficiency')
    ax5.set_xticks(range(len(df)))
    ax5.set_xticklabels([m.split('(')[0].strip() for m in df['Model']], rotation=45)
    
    # 6. Precision vs Recall
    ax6 = plt.subplot(3, 4, 6)
    colors = ['red' if 'Old' in model else 'blue' for model in df['Model']]
    ax6.scatter(df['Recall'], df['Precision'], c=colors, s=100, alpha=0.7)
    
    for i, model in enumerate(df['Model']):
        ax6.annotate(model.split('(')[0].strip(), 
                    (df['Recall'].iloc[i], df['Precision'].iloc[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    ax6.set_xlabel('Recall (%)')
    ax6.set_ylabel('Precision (%)')
    ax6.set_title('Precision vs Recall Trade-off')
    ax6.grid(True, alpha=0.3)
    
    # 7. Temporal Window Analysis
    ax7 = plt.subplot(3, 4, 7)
    temp3_models = df[df['Model'].str.contains('Temp 3|Old')]
    temp5_models = df[df['Model'].str.contains('Temp 5')]
    
    x = np.arange(len(temp3_models))
    width = 0.35
    
    bars1 = ax7.bar(x - width/2, temp3_models['Accuracy'], width, 
                   label='Temporal 3', color='lightblue', alpha=0.8)
    
    # Match temp5 models with temp3 models
    temp5_acc = []
    for model in temp3_models['Model']:
        if 'Old' in model:
            temp5_acc.append(0)  # No temp5 equivalent
        else:
            base_name = model.split('(')[0].strip()
            temp5_match = temp5_models[temp5_models['Model'].str.contains(base_name)]
            if not temp5_match.empty:
                temp5_acc.append(temp5_match['Accuracy'].iloc[0])
            else:
                temp5_acc.append(0)
    
    bars2 = ax7.bar(x + width/2, temp5_acc, width, 
                   label='Temporal 5', color='orange', alpha=0.8)
    
    ax7.set_xlabel('Model Type')
    ax7.set_ylabel('Accuracy (%)')
    ax7.set_title('Temporal Window Comparison')
    ax7.set_xticks(x)
    ax7.set_xticklabels([m.split('(')[0].strip() for m in temp3_models['Model']], rotation=45)
    ax7.legend()
    
    # 8. ROC AUC Performance
    ax8 = plt.subplot(3, 4, 8)
    sorted_df = df.sort_values('ROC_AUC', ascending=True)
    colors = ['red' if 'Complex' in model else 'lightcoral' for model in sorted_df['Model']]
    bars = ax8.barh(range(len(sorted_df)), sorted_df['ROC_AUC'], color=colors, alpha=0.8)
    ax8.set_yticks(range(len(sorted_df)))
    ax8.set_yticklabels([m.split('(')[0].strip() for m in sorted_df['Model']])
    ax8.set_xlabel('ROC AUC (%)')
    ax8.set_title('ROC AUC Performance Ranking')
    
    # 9. Training Convergence
    ax9 = plt.subplot(3, 4, 9)
    ax9.scatter(df['Epochs'], df['Accuracy'], c=df['Training_Time_Hours'], 
               s=100, alpha=0.7, cmap='viridis')
    
    for i, model in enumerate(df['Model']):
        ax9.annotate(model.split('(')[0].strip(), 
                    (df['Epochs'].iloc[i], df['Accuracy'].iloc[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    ax9.set_xlabel('Epochs to Convergence')
    ax9.set_ylabel('Final Accuracy (%)')
    ax9.set_title('Training Convergence Analysis')
    cbar = plt.colorbar(ax9.collections[0], ax=ax9)
    cbar.set_label('Training Time (hours)')
    ax9.grid(True, alpha=0.3)
    
    # 10. Model Complexity Tiers
    ax10 = plt.subplot(3, 4, 10)
    complexity_tiers = ['Simple\n(<50K)', 'Moderate\n(50K-200K)', 'Complex\n(>200K)']
    tier_performance = []
    
    for tier in ['Simple', 'Moderate', 'Complex']:
        if tier == 'Simple':
            mask = df['Parameters'] < 50000
        elif tier == 'Moderate':
            mask = (df['Parameters'] >= 50000) & (df['Parameters'] < 200000)
        else:
            mask = df['Parameters'] >= 200000
        
        if mask.any():
            tier_performance.append(df[mask]['F1_Score'].mean())
        else:
            tier_performance.append(0)
    
    bars = ax10.bar(complexity_tiers, tier_performance, color=['lightgreen', 'yellow', 'lightcoral'])
    ax10.set_ylabel('Average F1 Score (%)')
    ax10.set_title('Performance by Complexity Tier')
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        if height > 0:
            ax10.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                     f'{height:.1f}%', ha='center', va='bottom', fontsize=10)
    
    # 11. Data Quality Impact
    ax11 = plt.subplot(3, 4, 11)
    old_data_perf = df[df['Data_Type'] == 'Old']['F1_Score'].mean()
    current_data_perf = df[df['Data_Type'] == 'Current']['F1_Score'].mean()
    
    bars = ax11.bar(['Old Data', 'Current Data'], [old_data_perf, current_data_perf], 
                   color=['red', 'blue'], alpha=0.7)
    ax11.set_ylabel('Average F1 Score (%)')
    ax11.set_title('Data Quality Impact')
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax11.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                 f'{height:.1f}%', ha='center', va='bottom', fontsize=12, weight='bold')
    
    # 12. Summary Statistics
    ax12 = plt.subplot(3, 4, 12)
    summary_text = f"""
MODEL COMPARISON SUMMARY

📊 Total Models Evaluated: {len(df)}
🏆 Best Overall F1: {df['F1_Score'].max():.1f}%
⚡ Most Efficient: {df.loc[df['F1_Score'].idxmax(), 'Model'].split('(')[0].strip()}
🎯 Highest Accuracy: {df['Accuracy'].max():.1f}%
🔍 Best ROC AUC: {df['ROC_AUC'].max():.1f}%

💡 Key Insights:
• Simple models can outperform complex ones
• Old data shows superior performance
• Attention mechanisms improve discrimination
• Parameter efficiency varies significantly
• Temporal windows affect convergence speed

📈 Performance Range:
• Accuracy: {df['Accuracy'].min():.1f}% - {df['Accuracy'].max():.1f}%
• F1 Score: {df['F1_Score'].min():.1f}% - {df['F1_Score'].max():.1f}%
• Parameters: {df['Parameters'].min():,} - {df['Parameters'].max():,}
    """
    
    ax12.text(0.05, 0.95, summary_text, transform=ax12.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    ax12.axis('off')
    
    plt.suptitle('Comprehensive GNN Model Comparison Analysis', fontsize=16, weight='bold', y=0.98)
    plt.tight_layout()
    plt.savefig('comprehensive_model_comparison_dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Comprehensive model comparison dashboard created!")

def create_existing_viz_collage():
    """Create a collage of existing visualizations."""
    
    # Find existing visualization images
    viz_patterns = [
        'checkpoints_gatv2_complex_4layers_temp3/visualizations_gatv2_complex_4layers_temp3/*.png',
        'checkpoints_gatv2_complex_temp5/visualizations_gatv2_complex_temp5/*.png',
        'checkpoints_enhanced_temp3/visualizations_enhanced_temp3/*.png'
    ]
    
    all_images = []
    for pattern in viz_patterns:
        all_images.extend(glob.glob(pattern))
    
    if not all_images:
        print("❌ No existing visualizations found!")
        return
    
    # Create collage
    fig, axes = plt.subplots(3, 3, figsize=(18, 18))
    axes = axes.flatten()
    
    for i, img_path in enumerate(all_images[:9]):  # Show up to 9 images
        try:
            img = Image.open(img_path)
            axes[i].imshow(img)
            axes[i].axis('off')
            
            # Extract model name and visualization type from path
            model_name = img_path.split('/')[-2].replace('visualizations_', '').replace('_', ' ').title()
            viz_type = os.path.basename(img_path).replace('.png', '').replace('_', ' ').title()
            axes[i].set_title(f'{model_name}\n{viz_type}', fontsize=10, weight='bold')
            
        except Exception as e:
            axes[i].text(0.5, 0.5, f'Error loading\n{os.path.basename(img_path)}', 
                        ha='center', va='center', transform=axes[i].transAxes)
            axes[i].axis('off')
    
    # Hide unused subplots
    for i in range(len(all_images), 9):
        axes[i].axis('off')
    
    plt.suptitle('Existing Model Visualizations Collage', fontsize=16, weight='bold')
    plt.tight_layout()
    plt.savefig('existing_visualizations_collage.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Visualization collage created with {min(len(all_images), 9)} images!")

def main():
    """Generate all comparison visualizations."""
    print("🎨 Creating comprehensive model comparison visualizations...")
    
    # Create comprehensive comparison dashboard
    create_comprehensive_model_comparison()
    
    # Create collage of existing visualizations
    create_existing_viz_collage()
    
    print("🎉 All visualization comparisons completed!")

if __name__ == "__main__":
    main()
