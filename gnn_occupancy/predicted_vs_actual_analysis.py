#!/usr/bin/env python3
"""
Comprehensive Predicted vs Actual Arena Comparison Analysis
Shows exactly where models succeed and fail spatially across the arena.
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
import yaml
from datetime import datetime
from matplotlib.colors import LinearSegmentedColormap, ListedColormap
from matplotlib.patches import Rectangle
import matplotlib.gridspec as gridspec
from tqdm import tqdm
import pandas as pd

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data import OccupancyDataset
from model import create_model
from torch_geometric.data import DataLoader


class PredictedVsActualAnalyzer:
    """Comprehensive predicted vs actual arena comparison analyzer."""
    
    def __init__(self, data_dir: str = "../data/07_gnn_ready"):
        self.data_dir = Path(data_dir)
        self.results_dir = Path("results/predicted_vs_actual_analysis")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        (self.results_dir / "model_comparisons").mkdir(exist_ok=True)
        (self.results_dir / "error_analysis").mkdir(exist_ok=True)
        (self.results_dir / "interactive_comparisons").mkdir(exist_ok=True)
        (self.results_dir / "summary_analysis").mkdir(exist_ok=True)
        
        # Load arena characteristics
        arena_chars_file = Path("results/arena_characterization/arena_characteristics.json")
        with open(arena_chars_file, 'r') as f:
            self.arena_chars = json.load(f)
        
        # Analysis storage
        self.model_predictions = {}
        self.ground_truth_grid = None
        self.prediction_grids = {}
        self.error_grids = {}
        
        # Grid configuration
        self.grid_resolution = 0.1
        self.grid_bounds = {
            'min_x': self.arena_chars['spatial_dimensions']['min_x'],
            'max_x': self.arena_chars['spatial_dimensions']['max_x'],
            'min_y': self.arena_chars['spatial_dimensions']['min_y'],
            'max_y': self.arena_chars['spatial_dimensions']['max_y']
        }
        self.grid_width = int(np.ceil((self.grid_bounds['max_x'] - self.grid_bounds['min_x']) / self.grid_resolution))
        self.grid_height = int(np.ceil((self.grid_bounds['max_y'] - self.grid_bounds['min_y']) / self.grid_resolution))
        
    def discover_functional_models(self) -> List[Dict]:
        """Discover models that can actually make predictions."""
        print("🔍 Discovering ALL functional models (excluding GraphSAGE)...")

        functional_models = []

        # Complete list of ALL models from IoU evaluation (excluding GraphSAGE)
        all_models = [
            # GNN Models - GATv2 variants
            {
                'name': 'GATv2_Complex_4L_T3',
                'path': 'checkpoints_gatv2_complex_4layers_temp3',
                'config': 'config.yaml',
                'model': 'model_temporal_3_best.pt',
                'type': 'GNN',
                'expected_iou': 0.6699,
                'temporal_window': 3
            },
            {
                'name': 'GATv2_Standard_3L_T3_v1',
                'path': 'checkpoints_gatv2_temp3',
                'config': 'config.yaml',
                'model': 'model_temporal_3_best.pt',
                'type': 'GNN',
                'expected_iou': 0.6279,
                'temporal_window': 3
            },
            {
                'name': 'GATv2_Standard_3L_T3_v2',
                'path': 'checkpoints',
                'config': 'config.yaml',
                'model': 'model_temporal_3_best.pt',
                'type': 'GNN',
                'expected_iou': 0.6279,
                'temporal_window': 3
            },
            {
                'name': 'GATv2_Standard_3L_T5',
                'path': 'checkpoints_temp5',
                'config': 'config.yaml',
                'model': 'model_temporal_5_best.pt',
                'type': 'GNN',
                'expected_iou': 0.5862,
                'temporal_window': 5
            },
            {
                'name': 'GATv2_Deep_5L_T3',
                'path': 'checkpoints_gatv2_5layers_temp3',
                'config': 'config.yaml',
                'model': 'model_temporal_3_best.pt',
                'type': 'GNN',
                'expected_iou': 0.5598,
                'temporal_window': 3
            },
            {
                'name': 'GATv2_Complex_T5',
                'path': 'checkpoints_gatv2_complex_temp5',
                'config': None,  # May not have config
                'model': 'model_temporal_5_best.pt',
                'type': 'GNN',
                'expected_iou': 0.55,  # Estimated
                'temporal_window': 5
            },
            {
                'name': 'GATv2_Enhanced_T3',
                'path': 'checkpoints_enhanced_temp3',
                'config': None,  # May not have config
                'model': 'model_temporal_3_best.pt',
                'type': 'GNN',
                'expected_iou': 0.60,  # Estimated
                'temporal_window': 3
            },

            # ECC Models
            {
                'name': 'ECC_Standard_T3',
                'path': 'checkpoints_ecc_temp3',
                'config': None,
                'model': 'model_temporal_3_best.pt',
                'type': 'ECC',
                'expected_iou': 0.5669,
                'temporal_window': 3
            },
            {
                'name': 'ECC_Standard_T5',
                'path': 'checkpoints_ecc_temp5',
                'config': None,
                'model': 'model_temporal_5_best.pt',
                'type': 'ECC',
                'expected_iou': 0.0,  # Failed in IoU evaluation
                'temporal_window': 5
            },
            {
                'name': 'ECC_Hybrid_T3',
                'path': 'checkpoints_ecc_hybrid_temp3',
                'config': None,
                'model': 'model_temporal_3_best.pt',
                'type': 'ECC',
                'expected_iou': 0.0,  # Failed in IoU evaluation
                'temporal_window': 3
            }
        ]
        
        # Test each model
        for model_info in all_models:
            model_path = Path(model_info['path'])
            if model_path.exists() and (model_path / model_info['model']).exists():
                if model_info['config'] is None or (model_path / model_info['config']).exists():
                    functional_models.append(model_info)
                    print(f"  ✅ Found functional model: {model_info['name']} (Expected IoU: {model_info['expected_iou']:.1%})")
                else:
                    print(f"  ⚠️ Missing config for: {model_info['name']}")
            else:
                print(f"  ❌ Missing files for: {model_info['name']}")

        print(f"🎯 Discovered {len(functional_models)} functional models")
        return functional_models
    
    def load_model_and_predict(self, model_info: Dict) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Load a model and generate predictions on test data."""
        print(f"🤖 Loading model: {model_info['name']}")

        # Get temporal window from model info
        temporal_window = model_info.get('temporal_window', 3)
        
        # Load model configuration
        if model_info['type'] == 'GNN' and model_info['config'] is not None:
            config_path = Path(model_info['path']) / model_info['config']
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
        elif model_info['type'] == 'GNN':
            # GNN model without config - create default
            config = self._create_default_gnn_config(temporal_window)
        else:  # ECC model
            config = self._create_ecc_config(temporal_window)
        
        # Load checkpoint
        model_path = Path(model_info['path']) / model_info['model']
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Create and load model
        try:
            model = create_model(config)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"  ✅ Model loaded successfully")
        except Exception as e:
            print(f"  🔧 Attempting parameter inference...")
            # Try to infer parameters
            if 'embedding.weight' in checkpoint['model_state_dict']:
                embedding_shape = checkpoint['model_state_dict']['embedding.weight'].shape
                if len(embedding_shape) >= 2:
                    config['model']['input_dim'] = embedding_shape[1]
            
            if 'convs.0.att' in checkpoint['model_state_dict']:
                att_shape = checkpoint['model_state_dict']['convs.0.att'].shape
                if len(att_shape) >= 2:
                    config['model']['attention_heads'] = att_shape[1]
            
            model = create_model(config)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"  ✅ Model loaded with parameter inference")
        
        model.eval()
        
        # Load test data
        dataset = OccupancyDataset(
            root=str(self.data_dir),
            split='test',
            temporal_window=temporal_window,
            binary_mapping={'occupied': [1, 2, 3, 4], 'unoccupied': [0]}
        )
        
        data_loader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
        
        # Collect predictions and ground truth
        all_positions = []
        all_predictions = []
        all_labels = []
        
        print(f"  📊 Generating predictions on {len(dataset)} samples...")
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(data_loader, desc=f"Predicting {model_info['name']}")):
                try:
                    # Get model predictions
                    predictions = model(batch.x, batch.edge_index, batch.batch)
                    predictions = torch.sigmoid(predictions).cpu().numpy().flatten()
                    
                    # Extract positions and labels
                    try:
                        positions = batch.pos[:, :2].cpu().numpy()
                    except IndexError:
                        positions = batch.x[:, 2:4].cpu().numpy()
                    
                    labels = batch.y.cpu().numpy().flatten()
                    
                    # Handle graph-level predictions
                    if len(predictions) == 1 and len(labels) > 1:
                        predictions = np.full(len(labels), predictions[0])
                    elif len(predictions) != len(labels):
                        continue
                    
                    all_positions.append(positions)
                    all_predictions.append(predictions)
                    all_labels.append(labels)
                    
                except Exception as e:
                    continue
        
        # Combine all data
        if all_positions:
            all_positions = np.vstack(all_positions)
            all_predictions = np.concatenate(all_predictions)
            all_labels = np.concatenate(all_labels)
            
            print(f"  ✅ Collected {len(all_positions)} predictions")
            return all_positions, all_predictions, all_labels
        else:
            print(f"  ❌ No valid predictions collected")
            return np.array([]), np.array([]), np.array([])
    
    def _create_default_gnn_config(self, temporal_window: int) -> Dict:
        """Create default GNN configuration."""
        return {
            'model': {
                'gnn_type': 'gatv2',
                'input_dim': 10,
                'hidden_dim': 128,
                'output_dim': 1,
                'num_layers': 3,
                'dropout': 0.2,
                'skip_connections': True,
                'batch_norm': True,
                'layer_norm': False,
                'pooling': 'mean_max',
                'attention_heads': 4
            },
            'data': {
                'temporal_window': temporal_window,
                'binary_mapping': {
                    'occupied': [1, 2, 3, 4],
                    'unoccupied': [0]
                }
            }
        }

    def _create_ecc_config(self, temporal_window: int) -> Dict:
        """Create default ECC configuration."""
        return {
            'model': {
                'gnn_type': 'ecc',
                'input_dim': 10,
                'hidden_dim': 64,
                'output_dim': 1,
                'num_layers': 3,
                'dropout': 0.2,
                'skip_connections': True,
                'batch_norm': True,
                'layer_norm': False,
                'pooling': 'mean_max',
                'attention_heads': 4
            },
            'data': {
                'temporal_window': temporal_window,
                'binary_mapping': {
                    'occupied': [1, 2, 3, 4],
                    'unoccupied': [0]
                }
            }
        }
    
    def convert_to_grid(self, positions: np.ndarray, values: np.ndarray, 
                       aggregation: str = 'mean') -> np.ndarray:
        """Convert point data to 2D grid."""
        if len(positions) == 0:
            return np.zeros((self.grid_height, self.grid_width))
        
        # Convert positions to grid indices
        grid_x = ((positions[:, 0] - self.grid_bounds['min_x']) / self.grid_resolution).astype(int)
        grid_y = ((positions[:, 1] - self.grid_bounds['min_y']) / self.grid_resolution).astype(int)
        
        # Clip to grid bounds
        grid_x = np.clip(grid_x, 0, self.grid_width - 1)
        grid_y = np.clip(grid_y, 0, self.grid_height - 1)
        
        # Create grid
        grid = np.zeros((self.grid_height, self.grid_width))
        count_grid = np.zeros((self.grid_height, self.grid_width))
        
        # Aggregate values
        for i in range(len(positions)):
            y, x = grid_y[i], grid_x[i]
            grid[y, x] += values[i]
            count_grid[y, x] += 1
        
        # Apply aggregation
        if aggregation == 'mean':
            valid_cells = count_grid > 0
            grid[valid_cells] = grid[valid_cells] / count_grid[valid_cells]
        
        return grid
    
    def calculate_error_grid(self, gt_grid: np.ndarray, pred_grid: np.ndarray, 
                           threshold: float = 0.5) -> Dict[str, np.ndarray]:
        """Calculate spatial error patterns."""
        # Binarize grids
        gt_binary = (gt_grid > threshold).astype(int)
        pred_binary = (pred_grid > threshold).astype(int)
        
        # Calculate error types
        true_positives = (gt_binary == 1) & (pred_binary == 1)
        true_negatives = (gt_binary == 0) & (pred_binary == 0)
        false_positives = (gt_binary == 0) & (pred_binary == 1)
        false_negatives = (gt_binary == 1) & (pred_binary == 0)
        
        # Create error classification grid
        error_grid = np.zeros_like(gt_binary)
        error_grid[true_positives] = 1   # Correct occupied
        error_grid[true_negatives] = 2   # Correct free
        error_grid[false_positives] = 3  # Wrong - predicted occupied
        error_grid[false_negatives] = 4  # Wrong - missed occupied
        
        # Calculate difference grid
        difference_grid = pred_grid - gt_grid
        
        return {
            'error_classification': error_grid,
            'difference': difference_grid,
            'true_positives': true_positives,
            'true_negatives': true_negatives,
            'false_positives': false_positives,
            'false_negatives': false_negatives
        }

    def create_model_comparison_visualizations(self):
        """Create side-by-side model comparison visualizations."""
        print("  🎨 Creating model comparison visualizations...")

        # Create extent for plotting
        extent = [
            self.grid_bounds['min_x'], self.grid_bounds['max_x'],
            self.grid_bounds['min_y'], self.grid_bounds['max_y']
        ]

        # Define color maps
        occupancy_cmap = LinearSegmentedColormap.from_list('occupancy', ['white', 'blue', 'red'])
        error_colors = ['white', 'lightgreen', 'green', 'red', 'orange']  # No data, TN, TP, FP, FN
        error_cmap = ListedColormap(error_colors)

        for model_name, model_data in self.model_predictions.items():
            print(f"    📊 Creating comparison for {model_name}...")

            # Create 4-panel comparison
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'Predicted vs Actual Comparison: {model_name}\n'
                        f'Expected IoU: {model_data["info"]["expected_iou"]:.1%}',
                        fontsize=16, fontweight='bold')

            # Panel 1: Ground Truth
            ax1 = axes[0, 0]
            im1 = ax1.imshow(model_data['gt_grid'], extent=extent, origin='lower',
                           cmap=occupancy_cmap, vmin=0, vmax=1, aspect='equal')
            ax1.set_title('Ground Truth Occupancy', fontweight='bold')
            ax1.set_xlabel('X Position (m)')
            ax1.set_ylabel('Y Position (m)')
            plt.colorbar(im1, ax=ax1, fraction=0.046, pad=0.04, label='Occupancy Probability')

            # Panel 2: Model Predictions
            ax2 = axes[0, 1]
            im2 = ax2.imshow(model_data['pred_grid'], extent=extent, origin='lower',
                           cmap=occupancy_cmap, vmin=0, vmax=1, aspect='equal')
            ax2.set_title('Model Predictions', fontweight='bold')
            ax2.set_xlabel('X Position (m)')
            ax2.set_ylabel('Y Position (m)')
            plt.colorbar(im2, ax=ax2, fraction=0.046, pad=0.04, label='Prediction Probability')

            # Panel 3: Error Classification
            ax3 = axes[1, 0]
            im3 = ax3.imshow(model_data['error_analysis']['error_classification'],
                           extent=extent, origin='lower', cmap=error_cmap,
                           vmin=0, vmax=4, aspect='equal')
            ax3.set_title('Error Classification', fontweight='bold')
            ax3.set_xlabel('X Position (m)')
            ax3.set_ylabel('Y Position (m)')

            # Custom colorbar for error classification
            cbar3 = plt.colorbar(im3, ax=ax3, fraction=0.046, pad=0.04)
            cbar3.set_ticks([0.4, 1.2, 2.0, 2.8, 3.6])
            cbar3.set_ticklabels(['No Data', 'True Neg', 'True Pos', 'False Pos', 'False Neg'])

            # Panel 4: Difference Map
            ax4 = axes[1, 1]
            diff_grid = model_data['error_analysis']['difference']
            im4 = ax4.imshow(diff_grid, extent=extent, origin='lower',
                           cmap='RdBu_r', vmin=-1, vmax=1, aspect='equal')
            ax4.set_title('Prediction - Ground Truth', fontweight='bold')
            ax4.set_xlabel('X Position (m)')
            ax4.set_ylabel('Y Position (m)')
            plt.colorbar(im4, ax=ax4, fraction=0.046, pad=0.04, label='Difference')

            # Add arena boundary to all panels
            for ax in axes.flat:
                boundary = Rectangle((self.grid_bounds['min_x'], self.grid_bounds['min_y']),
                                   self.grid_bounds['max_x'] - self.grid_bounds['min_x'],
                                   self.grid_bounds['max_y'] - self.grid_bounds['min_y'],
                                   linewidth=2, edgecolor='black', facecolor='none')
                ax.add_patch(boundary)
                ax.grid(True, alpha=0.3)

            plt.tight_layout()

            # Save comparison
            comparison_path = self.results_dir / "model_comparisons" / f"{model_name.lower()}_vs_actual.png"
            plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
            comparison_pdf_path = self.results_dir / "model_comparisons" / f"{model_name.lower()}_vs_actual.pdf"
            plt.savefig(comparison_pdf_path, bbox_inches='tight')
            plt.close()

            print(f"      ✅ Saved: {comparison_path}")

    def create_error_analysis_visualizations(self):
        """Create comprehensive error analysis visualizations."""
        print("  🔍 Creating error analysis visualizations...")

        # Calculate aggregate error statistics
        error_stats = {}
        for model_name, model_data in self.model_predictions.items():
            error_analysis = model_data['error_analysis']

            total_cells = np.sum(error_analysis['error_classification'] > 0)
            if total_cells > 0:
                tp = np.sum(error_analysis['true_positives'])
                tn = np.sum(error_analysis['true_negatives'])
                fp = np.sum(error_analysis['false_positives'])
                fn = np.sum(error_analysis['false_negatives'])

                accuracy = (tp + tn) / (tp + tn + fp + fn) if (tp + tn + fp + fn) > 0 else 0
                precision = tp / (tp + fp) if (tp + fp) > 0 else 0
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

                error_stats[model_name] = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1': f1,
                    'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
                }

        # Create error statistics comparison
        if error_stats:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('Model Performance Comparison - Spatial Error Analysis',
                        fontsize=16, fontweight='bold')

            models = list(error_stats.keys())
            metrics = ['accuracy', 'precision', 'recall', 'f1']
            metric_titles = ['Accuracy', 'Precision', 'Recall', 'F1-Score']

            for i, (metric, title) in enumerate(zip(metrics, metric_titles)):
                ax = axes[i//2, i%2]
                values = [error_stats[model][metric] for model in models]
                colors = plt.cm.viridis(np.linspace(0, 1, len(models)))

                bars = ax.bar(range(len(models)), values, color=colors, alpha=0.7, edgecolor='black')
                ax.set_title(f'{title} by Model', fontweight='bold')
                ax.set_ylabel(title)
                ax.set_xticks(range(len(models)))
                ax.set_xticklabels([m.replace('_', '\n') for m in models], rotation=0, ha='center')
                ax.grid(True, alpha=0.3, axis='y')
                ax.set_ylim(0, 1)

                # Add value labels on bars
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

            plt.tight_layout()

            # Save error analysis
            error_path = self.results_dir / "error_analysis" / "spatial_error_metrics.png"
            plt.savefig(error_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"    ✅ Saved error metrics: {error_path}")

    def create_summary_analysis(self):
        """Create summary analysis comparing best vs worst models."""
        print("  📊 Creating summary analysis...")

        if len(self.model_predictions) < 2:
            print("    ⚠️ Need at least 2 models for comparison")
            return

        # Find best and worst models by expected IoU
        models_by_iou = sorted(self.model_predictions.items(),
                              key=lambda x: x[1]['info']['expected_iou'], reverse=True)

        best_model = models_by_iou[0]
        worst_model = models_by_iou[-1]

        # Create best vs worst comparison
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle(f'Best vs Worst Model Comparison\n'
                    f'Best: {best_model[0]} ({best_model[1]["info"]["expected_iou"]:.1%}) vs '
                    f'Worst: {worst_model[0]} ({worst_model[1]["info"]["expected_iou"]:.1%})',
                    fontsize=16, fontweight='bold')

        extent = [self.grid_bounds['min_x'], self.grid_bounds['max_x'],
                 self.grid_bounds['min_y'], self.grid_bounds['max_y']]

        # Best model row
        for i, (title, data_key) in enumerate([('Ground Truth', 'gt_grid'),
                                              ('Best Model Pred', 'pred_grid'),
                                              ('Best Model Errors', 'error_classification')]):
            ax = axes[0, i]
            if data_key == 'error_classification':
                data = best_model[1]['error_analysis'][data_key]
                cmap = ListedColormap(['white', 'lightgreen', 'green', 'red', 'orange'])
                vmin, vmax = 0, 4
            else:
                data = best_model[1][data_key]
                cmap = 'RdYlBu_r'
                vmin, vmax = 0, 1

            im = ax.imshow(data, extent=extent, origin='lower', cmap=cmap,
                          vmin=vmin, vmax=vmax, aspect='equal')
            ax.set_title(title, fontweight='bold')
            ax.set_xlabel('X Position (m)')
            if i == 0:
                ax.set_ylabel('Y Position (m)')
            plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

        # Worst model row
        for i, (title, data_key) in enumerate([('Ground Truth', 'gt_grid'),
                                              ('Worst Model Pred', 'pred_grid'),
                                              ('Worst Model Errors', 'error_classification')]):
            ax = axes[1, i]
            if data_key == 'error_classification':
                data = worst_model[1]['error_analysis'][data_key]
                cmap = ListedColormap(['white', 'lightgreen', 'green', 'red', 'orange'])
                vmin, vmax = 0, 4
            else:
                data = worst_model[1][data_key] if i > 0 else best_model[1][data_key]  # Same GT
                cmap = 'RdYlBu_r'
                vmin, vmax = 0, 1

            im = ax.imshow(data, extent=extent, origin='lower', cmap=cmap,
                          vmin=vmin, vmax=vmax, aspect='equal')
            ax.set_title(title, fontweight='bold')
            ax.set_xlabel('X Position (m)')
            if i == 0:
                ax.set_ylabel('Y Position (m)')
            plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

        plt.tight_layout()

        # Save summary
        summary_path = self.results_dir / "summary_analysis" / "best_vs_worst_comparison.png"
        plt.savefig(summary_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"    ✅ Saved summary: {summary_path}")

    def generate_analysis_report(self):
        """Generate comprehensive analysis report."""
        print("  📄 Generating analysis report...")

        # Calculate comprehensive statistics
        report_data = {
            'analysis_date': datetime.now().isoformat(),
            'arena_characteristics': self.arena_chars,
            'models_analyzed': len(self.model_predictions),
            'model_performance': {}
        }

        for model_name, model_data in self.model_predictions.items():
            error_analysis = model_data['error_analysis']

            # Calculate metrics
            tp = np.sum(error_analysis['true_positives'])
            tn = np.sum(error_analysis['true_negatives'])
            fp = np.sum(error_analysis['false_positives'])
            fn = np.sum(error_analysis['false_negatives'])

            total = tp + tn + fp + fn
            accuracy = (tp + tn) / total if total > 0 else 0
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

            report_data['model_performance'][model_name] = {
                'model_info': model_data['info'],
                'spatial_metrics': {
                    'accuracy': float(accuracy),
                    'precision': float(precision),
                    'recall': float(recall),
                    'f1_score': float(f1)
                },
                'confusion_matrix': {
                    'true_positives': int(tp),
                    'true_negatives': int(tn),
                    'false_positives': int(fp),
                    'false_negatives': int(fn)
                },
                'sample_statistics': {
                    'total_samples': len(model_data['positions']),
                    'prediction_range': [float(np.min(model_data['predictions'])),
                                       float(np.max(model_data['predictions']))],
                    'ground_truth_occupancy': float(np.mean(model_data['labels']))
                }
            }

        # Save report
        report_path = self.results_dir / "comprehensive_analysis_report.json"
        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)

        print(f"    ✅ Saved report: {report_path}")
    
    def run_comprehensive_analysis(self):
        """Run complete predicted vs actual analysis."""
        print("🚀 Starting Comprehensive Predicted vs Actual Analysis")
        print("=" * 80)
        
        try:
            # Discover functional models
            functional_models = self.discover_functional_models()
            
            if not functional_models:
                print("❌ No functional models found!")
                return False
            
            # Generate predictions for each model
            print("\n📊 Generating model predictions...")
            for model_info in functional_models:
                positions, predictions, labels = self.load_model_and_predict(model_info)
                
                if len(positions) > 0:
                    # Convert to grids
                    gt_grid = self.convert_to_grid(positions, labels)
                    pred_grid = self.convert_to_grid(positions, predictions)
                    
                    # Calculate errors
                    error_analysis = self.calculate_error_grid(gt_grid, pred_grid)
                    
                    # Store results
                    self.model_predictions[model_info['name']] = {
                        'info': model_info,
                        'positions': positions,
                        'predictions': predictions,
                        'labels': labels,
                        'gt_grid': gt_grid,
                        'pred_grid': pred_grid,
                        'error_analysis': error_analysis
                    }
                    
                    print(f"  ✅ Processed {model_info['name']}")
                else:
                    print(f"  ❌ Failed to process {model_info['name']}")
            
            if not self.model_predictions:
                print("❌ No successful model predictions!")
                return False
            
            # Create visualizations
            print("\n🎨 Creating comparison visualizations...")
            self.create_model_comparison_visualizations()
            self.create_error_analysis_visualizations()
            self.create_summary_analysis()

            print("📄 Generating comprehensive report...")
            self.generate_analysis_report()
            
            print("\n" + "=" * 80)
            print("🎉 PREDICTED VS ACTUAL ANALYSIS COMPLETED!")
            print("=" * 80)
            print(f"📁 Results saved to: {self.results_dir}")
            print(f"📊 Models analyzed: {len(self.model_predictions)}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function."""
    print("🔬 Predicted vs Actual Arena Comparison Analysis")
    print("🎯 Spatial Model Performance Visualization")
    print("=" * 80)
    
    analyzer = PredictedVsActualAnalyzer()
    success = analyzer.run_comprehensive_analysis()
    
    if success:
        print("\n✅ Analysis completed! Check the results directory for spatial performance insights.")
    else:
        print("\n❌ Analysis failed. Check the error messages above.")
    
    return success


if __name__ == "__main__":
    main()
